# Lucian Drives - Driver App

A Flutter mobile application for drivers in the Lucian Rides ride-sharing platform, specifically designed for local St. Lucia drivers.

## Project Overview

This is Stage 1 of the Lucian Drives driver application, focusing on core authentication, driver profile management, vehicle information setup, document verification, and basic navigation structure.

## Architecture

The project follows Clean Architecture principles with the following structure:

- **Presentation Layer**: UI widgets, screens, and controllers (Riverpod)
- **Domain Layer**: Business logic, entities, and use cases
- **Data Layer**: Repositories, data sources, and models
- **Infrastructure Layer**: External services, storage, and network

## Tech Stack

- **Framework**: Flutter (Dart SDK ^3.8.1)
- **State Management**: Riverpod
- **Navigation**: AutoRoute
- **Network**: Dio
- **Storage**: Flutter Secure Storage
- **Data Classes**: Freezed
- **JSON Serialization**: json_annotation
- **Location**: Geolocator
- **Image Picker**: image_picker
- **Dependency Injection**: GetIt

## Project Structure

```
lib/
├── core/                   # Core utilities and constants
│   ├── constants/          # App-wide constants
│   ├── config/            # App configuration
│   ├── di/                # Dependency injection
│   └── errors/            # Error handling
├── features/              # Feature-based modules
│   ├── auth/              # Authentication feature
│   ├── driver/            # Driver profile and vehicle management
│   └── documents/         # Document verification
├── services/              # External services
│   ├── api/               # API client
│   ├── auth/              # Authentication services
│   ├── location/          # Location services
│   └── storage/           # Storage services
├── shared/                # Shared components
│   ├── models/            # Common data models
│   └── widgets/           # Reusable UI components
└── main.dart              # Application entry point
```

## Getting Started

### Prerequisites

- Flutter SDK ^3.8.1
- Dart SDK ^3.8.1
- Android Studio / VS Code
- iOS development tools (for iOS builds)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```

3. Run code generation:
   ```bash
   dart run build_runner build
   ```

4. Run the app:
   ```bash
   flutter run
   ```

### Development Commands

```bash
# Install dependencies
flutter pub get

# Run code generation
dart run build_runner build

# Watch for changes during development
dart run build_runner watch

# Run tests
flutter test

# Analyze code
flutter analyze

# Build for Android
flutter build apk

# Build for iOS
flutter build ios
```

## API Integration

The app integrates with the Lucian Rides backend API:
- **Base URL**: `http://localhost:8000/api/v1`
- **Authentication**: JWT-based
- **Documentation**: Available in `Docs/api_docs.json`

## Features (Stage 1)

- [x] Project setup and dependencies
- [ ] Authentication system (login/register)
- [ ] Driver profile management
- [ ] Vehicle information setup
- [ ] Document verification system
- [ ] Location services integration
- [ ] Navigation and app structure
- [ ] Earnings and statistics
- [ ] Driver availability management

## Development Guidelines

- Follow Clean Architecture principles
- Use Riverpod for state management
- Implement proper error handling
- Write comprehensive tests
- Follow Flutter best practices
- Use const constructors where possible
- Maintain consistent code formatting

## Contributing

1. Follow the established architecture patterns
2. Write tests for new features
3. Update documentation as needed
4. Follow the existing code style
5. Create meaningful commit messages

## License

This project is proprietary software for the Lucian Rides platform.
