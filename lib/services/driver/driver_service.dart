import '../../shared/models/models.dart';

/// Service interface for driver-specific operations
/// Handles driver profile, vehicle management, and availability operations
abstract class DriverService {
  /// Driver Profile Operations
  Future<DriverProfile> getDriverProfile();
  Future<DriverProfile> createDriverProfile(DriverProfileCreate profileData);
  Future<DriverProfile> updateDriverProfile(DriverProfileUpdate profileData);

  /// Vehicle Operations
  Future<VehicleInfo> getVehicleInfo();
  Future<VehicleInfo> addVehicleInfo(VehicleInfoCreate vehicleData);
  Future<VehicleInfo> updateVehicleInfo(VehicleInfoUpdate vehicleData);

  /// Availability Management
  Future<void> updateAvailability(bool isAvailable);
  Future<bool> getAvailabilityStatus();

  /// Location Operations
  Future<void> updateLocation(LocationUpdate locationData);
  Future<LocationInfo?> getLastKnownLocation();

  /// Earnings and Statistics
  Future<EarningsInfo> getEarnings();
  Future<DriverStats> getDriverStats();

  /// Verification Status
  Future<VerificationStatus> getVerificationStatus();

  /// Profile Completion Check
  Future<bool> hasCompleteProfile();
}
