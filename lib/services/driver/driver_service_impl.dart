import '../../shared/models/models.dart';
import '../../core/errors/app_error.dart';
import '../api/api_client.dart';
import '../storage/driver_storage_helper.dart';
import 'driver_service.dart';

/// Implementation of DriverService
/// Handles driver-specific operations with API integration and local caching
class DriverServiceImpl implements DriverService {
  final ApiClient _apiClient;
  final DriverStorageHelper _storageHelper;

  DriverServiceImpl({
    required ApiClient apiClient,
    required DriverStorageHelper storageHelper,
  }) : _apiClient = apiClient,
       _storageHelper = storageHelper;

  @override
  Future<DriverProfile> getDriverProfile() async {
    try {
      // Fetch from API
      final response = await _apiClient.get('/drivers/profile');
      final profile = DriverProfile.fromJson(response);

      // Update cache
      await _storageHelper.storeDriverProfile(profile);

      return profile;
    } catch (e) {
      // If API fails, try to return cached data
      final cachedProfile = await _storageHelper.getDriverProfile();
      if (cachedProfile != null) {
        return cachedProfile;
      }

      throw _handleError(e);
    }
  }

  @override
  Future<DriverProfile> createDriverProfile(
    DriverProfileCreate profileData,
  ) async {
    try {
      final response = await _apiClient.post(
        '/drivers/profile',
        data: profileData.toJson(),
      );

      final profile = DriverProfile.fromJson(response);

      // Cache the new profile
      await _storageHelper.storeDriverProfile(profile);

      return profile;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<DriverProfile> updateDriverProfile(
    DriverProfileUpdate profileData,
  ) async {
    try {
      final response = await _apiClient.put(
        '/drivers/profile',
        data: profileData.toJson(),
      );

      final profile = DriverProfile.fromJson(response);

      // Update cache
      await _storageHelper.storeDriverProfile(profile);

      return profile;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<VehicleInfo> getVehicleInfo() async {
    try {
      // Fetch from API
      final response = await _apiClient.get('/drivers/vehicle');
      final vehicle = VehicleInfo.fromJson(response);

      // Update cache
      await _storageHelper.storeVehicleInfo(vehicle);

      return vehicle;
    } catch (e) {
      // If API fails, try to return cached data
      final cachedVehicle = await _storageHelper.getVehicleInfo();
      if (cachedVehicle != null) {
        return cachedVehicle;
      }

      throw _handleError(e);
    }
  }

  @override
  Future<VehicleInfo> addVehicleInfo(VehicleInfoCreate vehicleData) async {
    try {
      final response = await _apiClient.post(
        '/drivers/vehicle',
        data: vehicleData.toJson(),
      );

      final vehicle = VehicleInfo.fromJson(response);

      // Cache the new vehicle info
      await _storageHelper.storeVehicleInfo(vehicle);

      return vehicle;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<VehicleInfo> updateVehicleInfo(VehicleInfoUpdate vehicleData) async {
    try {
      final response = await _apiClient.put(
        '/drivers/vehicle',
        data: vehicleData.toJson(),
      );

      final vehicle = VehicleInfo.fromJson(response);

      // Update cache
      await _storageHelper.storeVehicleInfo(vehicle);

      return vehicle;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<void> updateAvailability(bool isAvailable) async {
    try {
      final updateData = DriverAvailabilityUpdate(isAvailable: isAvailable);

      await _apiClient.put('/drivers/availability', data: updateData.toJson());

      // Update cached availability status
      await _storageHelper.storeAvailabilityStatus(isAvailable);
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<bool> getAvailabilityStatus() async {
    try {
      // Fetch current profile to get latest availability
      final profile = await getDriverProfile();

      // Update cache with latest status
      await _storageHelper.storeAvailabilityStatus(profile.isAvailable);

      return profile.isAvailable;
    } catch (e) {
      // If API fails, return cached status or default to false
      final cachedStatus = await _storageHelper.getAvailabilityStatus();
      return cachedStatus ?? false;
    }
  }

  @override
  Future<void> updateLocation(LocationUpdate locationData) async {
    try {
      await _apiClient.post('/drivers/location', data: locationData.toJson());

      // Store as last known location
      final locationInfo = LocationInfo(
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        updatedAt: DateTime.now(),
        heading: locationData.heading,
        speed: locationData.speed,
        accuracy: locationData.accuracy,
      );

      await _storageHelper.storeLastLocation(locationInfo);
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<LocationInfo?> getLastKnownLocation() async {
    try {
      return await _storageHelper.getLastLocation();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<EarningsInfo> getEarnings() async {
    try {
      final response = await _apiClient.get('/drivers/earnings');
      final earnings = EarningsInfo.fromJson(response);

      // Cache earnings data
      await _storageHelper.storeEarningsInfo(earnings);

      return earnings;
    } catch (e) {
      // If API fails, try to return cached data
      final cachedEarnings = await _storageHelper.getEarningsInfo();
      if (cachedEarnings != null) {
        return cachedEarnings;
      }

      throw _handleError(e);
    }
  }

  @override
  Future<DriverStats> getDriverStats() async {
    try {
      final response = await _apiClient.get('/drivers/stats');
      return DriverStats.fromJson(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<VerificationStatus> getVerificationStatus() async {
    try {
      final response = await _apiClient.get('/drivers/verification-status');
      return VerificationStatus.fromJson(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<bool> hasCompleteProfile() async {
    try {
      return await _storageHelper.hasCompleteDriverProfile();
    } catch (e) {
      return false;
    }
  }

  /// Handle and transform errors into appropriate AppError types
  AppError _handleError(dynamic error) {
    if (error is AppError) {
      return error;
    }

    // Handle different types of errors and convert to AppError
    if (error.toString().contains('network') ||
        error.toString().contains('connection')) {
      return const AppError.network(
        message:
            'Network connection failed. Please check your internet connection.',
      );
    }

    if (error.toString().contains('401') ||
        error.toString().contains('unauthorized')) {
      return const AppError.authentication(
        message: 'Authentication failed. Please login again.',
        errorCode: '401',
      );
    }

    if (error.toString().contains('validation') ||
        error.toString().contains('400')) {
      return const AppError.validation(
        message: 'Invalid data provided.',
        fieldErrors: {},
      );
    }

    if (error.toString().contains('404')) {
      return const AppError.server(
        message:
            'Driver profile not found. Please complete your profile setup.',
        statusCode: 404,
      );
    }

    return AppError.unknown(
      message: 'An unexpected error occurred.',
      exception: error,
    );
  }
}
