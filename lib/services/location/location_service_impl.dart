import 'dart:async';
import 'dart:developer' as developer;
import 'package:geolocator/geolocator.dart';
import '../api/api_client.dart';
import '../../shared/models/location_update.dart';
import '../../core/errors/app_error.dart';
import 'location_service.dart';

/// Implementation of LocationService using Geolocator
class LocationServiceImpl implements LocationService {
  final ApiClient _apiClient;
  StreamSubscription<Position>? _positionSubscription;
  Position? _lastKnownLocation;
  bool _isTrackingLocation = false;

  LocationServiceImpl({required ApiClient apiClient}) : _apiClient = apiClient;

  @override
  Future<bool> requestLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        developer.log(
          'Location services are disabled',
          name: 'LocationService',
        );
        return false;
      }

      // Check current permission status
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          developer.log(
            'Location permissions are denied',
            name: 'LocationService',
          );
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        developer.log(
          'Location permissions are permanently denied',
          name: 'LocationService',
        );
        return false;
      }

      developer.log(
        'Location permission granted: $permission',
        name: 'LocationService',
      );
      return permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always;
    } catch (e) {
      developer.log(
        'Error requesting location permission: $e',
        name: 'LocationService',
      );
      return false;
    }
  }

  @override
  Future<LocationPermission> checkLocationPermission() async {
    return await Geolocator.checkPermission();
  }

  @override
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  @override
  Future<Position> getCurrentPosition() async {
    try {
      // Check permissions first
      bool hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        throw const AppError.location(
          message: 'Location permission not granted',
          errorType: LocationErrorType.permissionDenied,
        );
      }

      // Check if location services are enabled
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw const AppError.location(
          message: 'Location services are disabled',
          errorType: LocationErrorType.serviceDisabled,
        );
      }

      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 0,
      );

      Position position =
          await Geolocator.getCurrentPosition(
            locationSettings: locationSettings,
          ).timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              throw const AppError.location(
                message: 'Location request timed out',
                errorType: LocationErrorType.timeout,
              );
            },
          );

      _lastKnownLocation = position;
      developer.log(
        'Current position obtained: ${position.latitude}, ${position.longitude}',
        name: 'LocationService',
      );

      return position;
    } catch (e) {
      developer.log(
        'Error getting current position: $e',
        name: 'LocationService',
      );
      if (e is AppError) {
        rethrow;
      }
      throw AppError.location(
        message: 'Failed to get current location: ${e.toString()}',
        errorType: LocationErrorType.timeout,
      );
    }
  }

  @override
  Stream<Position> getLocationStream() {
    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Update every 10 meters
      timeLimit: Duration(seconds: 30),
    );

    return Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).handleError((error) {
      developer.log('Location stream error: $error', name: 'LocationService');
      throw AppError.location(
        message: 'Location tracking error: ${error.toString()}',
        errorType: LocationErrorType.timeout,
      );
    });
  }

  @override
  Future<void> startLocationTracking() async {
    if (_isTrackingLocation) {
      developer.log(
        'Location tracking already started',
        name: 'LocationService',
      );
      return;
    }

    try {
      // Check permissions first
      bool hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        throw const AppError.location(
          message: 'Cannot start location tracking without permission',
          errorType: LocationErrorType.permissionDenied,
        );
      }

      developer.log('Starting location tracking', name: 'LocationService');
      _isTrackingLocation = true;

      // Start listening to location updates
      _positionSubscription = getLocationStream().listen(
        (Position position) async {
          _lastKnownLocation = position;
          developer.log(
            'Location update: ${position.latitude}, ${position.longitude}',
            name: 'LocationService',
          );

          // Send location update to backend
          try {
            await updateDriverLocation(
              position.latitude,
              position.longitude,
              heading: position.heading,
              speed: position.speed,
              accuracy: position.accuracy,
            );
          } catch (e) {
            developer.log(
              'Failed to update driver location on backend: $e',
              name: 'LocationService',
            );
            // Don't stop tracking if backend update fails
          }
        },
        onError: (error) {
          developer.log(
            'Location tracking error: $error',
            name: 'LocationService',
          );
          _isTrackingLocation = false;
        },
      );
    } catch (e) {
      _isTrackingLocation = false;
      developer.log(
        'Failed to start location tracking: $e',
        name: 'LocationService',
      );
      rethrow;
    }
  }

  @override
  Future<void> stopLocationTracking() async {
    if (!_isTrackingLocation) {
      developer.log(
        'Location tracking already stopped',
        name: 'LocationService',
      );
      return;
    }

    developer.log('Stopping location tracking', name: 'LocationService');

    await _positionSubscription?.cancel();
    _positionSubscription = null;
    _isTrackingLocation = false;
  }

  @override
  Future<void> updateDriverLocation(
    double latitude,
    double longitude, {
    double? heading,
    double? speed,
    double? accuracy,
  }) async {
    try {
      final locationUpdate = LocationUpdate(
        latitude: latitude,
        longitude: longitude,
        heading: heading,
        speed: speed,
        accuracy: accuracy,
      );

      await _apiClient.post('/drivers/location', data: locationUpdate.toJson());

      developer.log(
        'Driver location updated on backend: $latitude, $longitude',
        name: 'LocationService',
      );
    } catch (e) {
      developer.log(
        'Failed to update driver location: $e',
        name: 'LocationService',
      );
      throw AppError.network(
        message: 'Failed to update location on server',
        details: e.toString(),
      );
    }
  }

  @override
  bool get isTrackingLocation => _isTrackingLocation;

  @override
  Position? get lastKnownLocation => _lastKnownLocation;

  /// Dispose resources
  void dispose() {
    stopLocationTracking();
  }
}
