import 'package:geolocator/geolocator.dart';

/// Abstract interface for location services
abstract class LocationService {
  /// Request location permissions from the user
  Future<bool> requestLocationPermission();

  /// Check current location permission status
  Future<LocationPermission> checkLocationPermission();

  /// Check if location services are enabled on the device
  Future<bool> isLocationServiceEnabled();

  /// Get current position once
  Future<Position> getCurrentPosition();

  /// Get a stream of position updates
  Stream<Position> getLocationStream();

  /// Start location tracking and send updates to backend
  Future<void> startLocationTracking();

  /// Stop location tracking
  Future<void> stopLocationTracking();

  /// Update driver location on the backend
  Future<void> updateDriverLocation(
    double latitude,
    double longitude, {
    double? heading,
    double? speed,
    double? accuracy,
  });

  /// Check if location tracking is currently active
  bool get isTrackingLocation;

  /// Get the last known location
  Position? get lastKnownLocation;
}
