import 'dart:io';

/// Abstract API client interface for the Lucian Drives driver app
abstract class ApiClient {
  /// GET request
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });

  /// POST request
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  });

  /// PUT request
  Future<Map<String, dynamic>> put(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  });

  /// DELETE request
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  });

  /// Upload file
  Future<Map<String, dynamic>> uploadFile(
    String endpoint,
    File file, {
    String? fieldName,
    Map<String, dynamic>? data,
    Map<String, String>? headers,
    void Function(int sent, int total)? onProgress,
  });

  /// Set authentication token
  void setAuthToken(String token);

  /// Clear authentication token
  void clearAuthToken();

  /// Get current authentication token
  String? get authToken;
}
