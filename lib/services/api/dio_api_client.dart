import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'api_client.dart';
import '../../core/constants/api_constants.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/app_error.dart';
import '../../core/errors/error_handler.dart';

/// Dio implementation of ApiClient for the Lucian Drives driver app
class DioApiClient implements ApiClient {
  late final Dio _dio;
  String? _authToken;

  DioApiClient() {
    _dio = Dio();
    _setupDio();
    _setupInterceptors();
  }

  void _setupDio() {
    _dio.options = BaseOptions(
      baseUrl: ApiConstants.baseUrl,
      connectTimeout: const Duration(milliseconds: ApiConstants.connectTimeout),
      receiveTimeout: const Duration(milliseconds: ApiConstants.receiveTimeout),
      sendTimeout: const Duration(milliseconds: ApiConstants.sendTimeout),
      headers: {
        'Content-Type': ApiConstants.contentTypeJson,
        'Accept': ApiConstants.contentTypeJson,
      },
      validateStatus: (status) {
        // Accept all status codes to handle them in error handler
        return status != null && status < 500;
      },
    );
  }

  void _setupInterceptors() {
    // Request interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add authentication token if available
          if (_authToken != null) {
            options.headers[ApiConstants.authorizationHeader] =
                ApiConstants.getAuthHeader(_authToken!);
          }

          // Log request in debug mode
          if (kDebugMode) {
            print('🚀 REQUEST: ${options.method} ${options.uri}');
            if (options.data != null) {
              print('📤 DATA: ${options.data}');
            }
            if (options.queryParameters.isNotEmpty) {
              print('🔍 QUERY: ${options.queryParameters}');
            }
          }

          handler.next(options);
        },
        onResponse: (response, handler) {
          // Log response in debug mode
          if (kDebugMode) {
            print(
              '✅ RESPONSE: ${response.statusCode} ${response.requestOptions.uri}',
            );
            print('📥 DATA: ${response.data}');
          }

          handler.next(response);
        },
        onError: (error, handler) {
          // Log error in debug mode
          if (kDebugMode) {
            print(
              '❌ ERROR: ${error.requestOptions.method} ${error.requestOptions.uri}',
            );
            print('💥 MESSAGE: ${error.message}');
            if (error.response != null) {
              print('📥 RESPONSE: ${error.response?.data}');
            }
          }

          handler.next(error);
        },
      ),
    );

    // Retry interceptor for network errors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) async {
          if (_shouldRetry(error)) {
            try {
              final response = await _retry(error.requestOptions);
              handler.resolve(response);
              return;
            } catch (retryError) {
              // If retry fails, continue with original error
            }
          }
          handler.next(error);
        },
      ),
    );
  }

  bool _shouldRetry(DioException error) {
    // Retry on network errors, timeouts, and 5xx server errors
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.connectionError ||
        (error.response?.statusCode != null &&
            error.response!.statusCode! >= 500);
  }

  Future<Response> _retry(RequestOptions requestOptions) async {
    // Simple retry logic - could be enhanced with exponential backoff
    await Future.delayed(
      const Duration(milliseconds: AppConstants.retryDelayMs),
    );

    return _dio.request(
      requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: Options(
        method: requestOptions.method,
        headers: requestOptions.headers,
        contentType: requestOptions.contentType,
      ),
    );
  }

  @override
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      return _handleResponse(response);
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    } catch (e) {
      throw AppError.unknown(
        message: 'An unexpected error occurred',
        exception: e,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        options: Options(headers: headers),
      );
      return _handleResponse(response);
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    } catch (e) {
      throw AppError.unknown(
        message: 'An unexpected error occurred',
        exception: e,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> put(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        options: Options(headers: headers),
      );
      return _handleResponse(response);
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    } catch (e) {
      throw AppError.unknown(
        message: 'An unexpected error occurred',
        exception: e,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        data: data,
        options: Options(headers: headers),
      );
      return _handleResponse(response);
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    } catch (e) {
      throw AppError.unknown(
        message: 'An unexpected error occurred',
        exception: e,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> uploadFile(
    String endpoint,
    File file, {
    String? fieldName,
    Map<String, dynamic>? data,
    Map<String, String>? headers,
    void Function(int sent, int total)? onProgress,
  }) async {
    try {
      final fileName = file.path.split('/').last;
      final formData = FormData();

      // Add file to form data
      formData.files.add(
        MapEntry(
          fieldName ?? 'file',
          await MultipartFile.fromFile(file.path, filename: fileName),
        ),
      );

      // Add additional data if provided
      if (data != null) {
        for (final entry in data.entries) {
          formData.fields.add(MapEntry(entry.key, entry.value.toString()));
        }
      }

      final response = await _dio.post(
        endpoint,
        data: formData,
        options: Options(
          headers: {
            ...?headers,
            'Content-Type': ApiConstants.contentTypeMultipart,
          },
        ),
        onSendProgress: onProgress,
      );

      return _handleResponse(response);
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    } catch (e) {
      throw AppError.unknown(message: 'File upload failed', exception: e);
    }
  }

  @override
  void setAuthToken(String token) {
    _authToken = token;
  }

  @override
  void clearAuthToken() {
    _authToken = null;
  }

  @override
  String? get authToken => _authToken;

  Map<String, dynamic> _handleResponse(Response response) {
    final statusCode = response.statusCode ?? 0;

    // Handle successful responses
    if (statusCode >= 200 && statusCode < 300) {
      if (response.data is Map<String, dynamic>) {
        return response.data as Map<String, dynamic>;
      } else if (response.data is List) {
        return {ApiConstants.dataKey: response.data};
      } else {
        return {ApiConstants.dataKey: response.data};
      }
    }

    // Handle error responses
    throw ErrorHandler.handleDioError(
      DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
      ),
    );
  }
}
