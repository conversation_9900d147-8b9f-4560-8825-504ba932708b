/// Abstract interface for secure storage operations
/// Handles token storage, user data persistence, and driver preferences
abstract class StorageService {
  /// Token management
  Future<void> storeToken(String token);
  Future<String?> getToken();
  Future<void> deleteToken();

  /// User data persistence
  Future<void> storeUserData(Map<String, dynamic> userData);
  Future<Map<String, dynamic>?> getUserData();
  Future<void> deleteUserData();

  /// Driver preferences
  Future<void> storeDriverPreferences(Map<String, dynamic> preferences);
  Future<Map<String, dynamic>?> getDriverPreferences();
  Future<void> deleteDriverPreferences();

  /// Generic key-value storage
  Future<void> store(String key, String value);
  Future<String?> get(String key);
  Future<void> delete(String key);

  /// Clear all stored data
  Future<void> clearAll();

  /// Check if a key exists
  Future<bool> containsKey(String key);

  /// Get all keys
  Future<Set<String>> getAllKeys();
}
