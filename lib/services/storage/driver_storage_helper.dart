import 'dart:convert';
import '../../shared/models/models.dart';
import 'storage_service.dart';

/// Helper class for driver-specific storage operations
/// Provides type-safe methods for storing and retrieving driver data
class DriverStorageHelper {
  static const String _driverProfileKey = 'driver_profile';
  static const String _vehicleInfoKey = 'vehicle_info';
  static const String _availabilityStatusKey = 'availability_status';
  static const String _lastLocationKey = 'last_location';
  static const String _earningsInfoKey = 'earnings_info';
  static const String _driverStatsKey = 'driver_stats';

  final StorageService _storageService;

  DriverStorageHelper(this._storageService);

  /// Driver Profile Storage
  Future<void> storeDriverProfile(DriverProfile profile) async {
    final jsonString = jsonEncode(profile.toJson());
    await _storageService.store(_driverProfileKey, jsonString);
  }

  Future<DriverProfile?> getDriverProfile() async {
    final jsonString = await _storageService.get(_driverProfileKey);
    if (jsonString == null) return null;

    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return DriverProfile.fromJson(json);
    } catch (e) {
      // If parsing fails, return null and let the app fetch fresh data
      return null;
    }
  }

  Future<void> deleteDriverProfile() async {
    await _storageService.delete(_driverProfileKey);
  }

  /// Vehicle Information Storage
  Future<void> storeVehicleInfo(VehicleInfo vehicleInfo) async {
    final jsonString = jsonEncode(vehicleInfo.toJson());
    await _storageService.store(_vehicleInfoKey, jsonString);
  }

  Future<VehicleInfo?> getVehicleInfo() async {
    final jsonString = await _storageService.get(_vehicleInfoKey);
    if (jsonString == null) return null;

    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return VehicleInfo.fromJson(json);
    } catch (e) {
      return null;
    }
  }

  Future<void> deleteVehicleInfo() async {
    await _storageService.delete(_vehicleInfoKey);
  }

  /// Availability Status Storage
  Future<void> storeAvailabilityStatus(bool isAvailable) async {
    await _storageService.store(_availabilityStatusKey, isAvailable.toString());
  }

  Future<bool?> getAvailabilityStatus() async {
    final value = await _storageService.get(_availabilityStatusKey);
    if (value == null) return null;

    return value.toLowerCase() == 'true';
  }

  Future<void> deleteAvailabilityStatus() async {
    await _storageService.delete(_availabilityStatusKey);
  }

  /// Last Known Location Storage
  Future<void> storeLastLocation(LocationInfo location) async {
    final jsonString = jsonEncode(location.toJson());
    await _storageService.store(_lastLocationKey, jsonString);
  }

  Future<LocationInfo?> getLastLocation() async {
    final jsonString = await _storageService.get(_lastLocationKey);
    if (jsonString == null) return null;

    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return LocationInfo.fromJson(json);
    } catch (e) {
      return null;
    }
  }

  Future<void> deleteLastLocation() async {
    await _storageService.delete(_lastLocationKey);
  }

  /// Earnings Information Storage
  Future<void> storeEarningsInfo(EarningsInfo earnings) async {
    final jsonString = jsonEncode(earnings.toJson());
    await _storageService.store(_earningsInfoKey, jsonString);
  }

  Future<EarningsInfo?> getEarningsInfo() async {
    final jsonString = await _storageService.get(_earningsInfoKey);
    if (jsonString == null) return null;

    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return EarningsInfo.fromJson(json);
    } catch (e) {
      return null;
    }
  }

  Future<void> deleteEarningsInfo() async {
    await _storageService.delete(_earningsInfoKey);
  }

  /// Driver Statistics Storage
  Future<void> storeDriverStats(DriverStats stats) async {
    final jsonString = jsonEncode(stats.toJson());
    await _storageService.store(_driverStatsKey, jsonString);
  }

  Future<DriverStats?> getDriverStats() async {
    final jsonString = await _storageService.get(_driverStatsKey);
    if (jsonString == null) return null;

    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return DriverStats.fromJson(json);
    } catch (e) {
      return null;
    }
  }

  Future<void> deleteDriverStats() async {
    await _storageService.delete(_driverStatsKey);
  }

  /// Clear all driver-specific data
  Future<void> clearAllDriverData() async {
    await Future.wait([
      deleteDriverProfile(),
      deleteVehicleInfo(),
      deleteAvailabilityStatus(),
      deleteLastLocation(),
      deleteEarningsInfo(),
      deleteDriverStats(),
    ]);
  }

  /// Check if driver has complete profile data stored
  Future<bool> hasCompleteDriverProfile() async {
    final profile = await getDriverProfile();
    final vehicle = await getVehicleInfo();

    return profile != null && vehicle != null;
  }
}
