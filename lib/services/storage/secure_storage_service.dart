import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'storage_service.dart';

/// Implementation of StorageService using flutter_secure_storage
/// Provides secure storage for sensitive data like tokens and user information
class SecureStorageService implements StorageService {
  static const String _tokenKey = 'auth_token';
  static const String _userDataKey = 'user_data';
  static const String _driverPreferencesKey = 'driver_preferences';

  final FlutterSecureStorage _storage;

  SecureStorageService({FlutterSecureStorage? storage})
    : _storage =
          storage ??
          const FlutterSecureStorage(
            aOptions: AndroidOptions(encryptedSharedPreferences: true),
            iOptions: IOSOptions(
              accessibility: KeychainAccessibility.first_unlock_this_device,
            ),
          );

  @override
  Future<void> storeToken(String token) async {
    try {
      await _storage.write(key: _tokenKey, value: token);
    } catch (e) {
      throw StorageException('Failed to store token: $e');
    }
  }

  @override
  Future<String?> getToken() async {
    try {
      return await _storage.read(key: _tokenKey);
    } catch (e) {
      throw StorageException('Failed to retrieve token: $e');
    }
  }

  @override
  Future<void> deleteToken() async {
    try {
      await _storage.delete(key: _tokenKey);
    } catch (e) {
      throw StorageException('Failed to delete token: $e');
    }
  }

  @override
  Future<void> storeUserData(Map<String, dynamic> userData) async {
    try {
      final jsonString = jsonEncode(userData);
      await _storage.write(key: _userDataKey, value: jsonString);
    } catch (e) {
      throw StorageException('Failed to store user data: $e');
    }
  }

  @override
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final jsonString = await _storage.read(key: _userDataKey);
      if (jsonString == null) return null;

      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw StorageException('Failed to retrieve user data: $e');
    }
  }

  @override
  Future<void> deleteUserData() async {
    try {
      await _storage.delete(key: _userDataKey);
    } catch (e) {
      throw StorageException('Failed to delete user data: $e');
    }
  }

  @override
  Future<void> storeDriverPreferences(Map<String, dynamic> preferences) async {
    try {
      final jsonString = jsonEncode(preferences);
      await _storage.write(key: _driverPreferencesKey, value: jsonString);
    } catch (e) {
      throw StorageException('Failed to store driver preferences: $e');
    }
  }

  @override
  Future<Map<String, dynamic>?> getDriverPreferences() async {
    try {
      final jsonString = await _storage.read(key: _driverPreferencesKey);
      if (jsonString == null) return null;

      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw StorageException('Failed to retrieve driver preferences: $e');
    }
  }

  @override
  Future<void> deleteDriverPreferences() async {
    try {
      await _storage.delete(key: _driverPreferencesKey);
    } catch (e) {
      throw StorageException('Failed to delete driver preferences: $e');
    }
  }

  @override
  Future<void> store(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e) {
      throw StorageException('Failed to store data for key $key: $e');
    }
  }

  @override
  Future<String?> get(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      throw StorageException('Failed to retrieve data for key $key: $e');
    }
  }

  @override
  Future<void> delete(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      throw StorageException('Failed to delete data for key $key: $e');
    }
  }

  @override
  Future<void> clearAll() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      throw StorageException('Failed to clear all data: $e');
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      throw StorageException('Failed to check if key exists: $e');
    }
  }

  @override
  Future<Set<String>> getAllKeys() async {
    try {
      final allData = await _storage.readAll();
      return allData.keys.toSet();
    } catch (e) {
      throw StorageException('Failed to get all keys: $e');
    }
  }
}

/// Exception thrown when storage operations fail
class StorageException implements Exception {
  final String message;

  const StorageException(this.message);

  @override
  String toString() => 'StorageException: $message';
}
