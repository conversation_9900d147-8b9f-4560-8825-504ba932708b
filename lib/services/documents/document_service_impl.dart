import 'dart:io';

import '../api/api_client.dart';
import '../../shared/models/models.dart';
import '../../core/errors/app_error.dart';
import 'document_service.dart';

/// Implementation of DocumentService for driver document management
/// Handles API calls for document upload, retrieval, and verification
class DocumentServiceImpl implements DocumentService {
  final ApiClient _apiClient;

  DocumentServiceImpl({required ApiClient apiClient}) : _apiClient = apiClient;

  @override
  Future<VerificationDocument> uploadDocument(
    File file,
    DocumentType documentType, {
    String? description,
  }) async {
    try {
      // Validate file before upload
      await _validateFile(file);

      // Upload file using multipart form data
      final response = await _apiClient.uploadFile(
        '/drivers/documents/upload',
        file,
        fieldName: 'document',
        data: {
          'document_type': _documentTypeToString(documentType),
          if (description != null) 'description': description,
        },
      );

      return VerificationDocument.fromJson(response['data']);
    } catch (e) {
      throw _handleDocumentError(e, 'Failed to upload document');
    }
  }

  @override
  Future<List<VerificationDocument>> getDocuments() async {
    try {
      final response = await _apiClient.get('/drivers/documents');

      final List<dynamic> documentsJson = response['data'] ?? [];
      return documentsJson
          .map((json) => VerificationDocument.fromJson(json))
          .toList();
    } catch (e) {
      throw _handleDocumentError(e, 'Failed to fetch documents');
    }
  }

  @override
  Future<void> deleteDocument(String documentId) async {
    try {
      await _apiClient.delete('/drivers/documents/$documentId');
    } catch (e) {
      throw _handleDocumentError(e, 'Failed to delete document');
    }
  }

  @override
  Future<void> submitForVerification() async {
    try {
      await _apiClient.post('/drivers/verification/request');
    } catch (e) {
      throw _handleDocumentError(e, 'Failed to submit for verification');
    }
  }

  @override
  Future<VerificationStatus> getVerificationStatus() async {
    try {
      final response = await _apiClient.get('/drivers/verification-status');
      return VerificationStatus.fromJson(response['data']);
    } catch (e) {
      throw _handleDocumentError(e, 'Failed to get verification status');
    }
  }

  @override
  Future<bool> hasAllRequiredDocuments() async {
    try {
      final documents = await getDocuments();
      final requiredTypes = [
        DocumentType.license,
        DocumentType.insurance,
        DocumentType.registration,
        DocumentType.vehiclePhoto,
      ];

      for (final requiredType in requiredTypes) {
        final hasType = documents.any(
          (doc) => doc.documentType == requiredType,
        );
        if (!hasType) return false;
      }

      return true;
    } catch (e) {
      throw _handleDocumentError(e, 'Failed to check required documents');
    }
  }

  @override
  Future<List<DocumentType>> getMissingDocuments() async {
    try {
      final documents = await getDocuments();
      final requiredTypes = [
        DocumentType.license,
        DocumentType.insurance,
        DocumentType.registration,
        DocumentType.vehiclePhoto,
      ];

      final uploadedTypes = documents.map((doc) => doc.documentType).toSet();
      return requiredTypes
          .where((type) => !uploadedTypes.contains(type))
          .toList();
    } catch (e) {
      throw _handleDocumentError(e, 'Failed to get missing documents');
    }
  }

  /// Validate file before upload
  Future<void> _validateFile(File file) async {
    // Check if file exists
    if (!await file.exists()) {
      throw const AppError.document(
        message: 'File does not exist',
        errorType: DocumentErrorType.uploadFailed,
      );
    }

    // Check file size (max 5MB)
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    final fileSize = await file.length();
    if (fileSize > maxSizeBytes) {
      throw const AppError.document(
        message: 'File size exceeds 5MB limit',
        errorType: DocumentErrorType.fileTooLarge,
      );
    }

    // Check file extension
    final allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf'];
    final fileName = file.path.toLowerCase();
    final hasValidExtension = allowedExtensions.any(
      (ext) => fileName.endsWith(ext),
    );

    if (!hasValidExtension) {
      throw const AppError.document(
        message: 'Invalid file format. Allowed: JPG, PNG, PDF',
        errorType: DocumentErrorType.invalidFormat,
      );
    }
  }

  /// Convert DocumentType enum to string for API
  String _documentTypeToString(DocumentType type) {
    switch (type) {
      case DocumentType.license:
        return 'license';
      case DocumentType.insurance:
        return 'insurance';
      case DocumentType.registration:
        return 'registration';
      case DocumentType.vehiclePhoto:
        return 'vehicle_photo';
    }
  }

  /// Handle document-related errors
  AppError _handleDocumentError(dynamic error, String defaultMessage) {
    if (error is AppError) return error;

    // Handle HTTP errors
    if (error.toString().contains('413')) {
      return const AppError.document(
        message: 'File too large for upload',
        errorType: DocumentErrorType.fileTooLarge,
      );
    }

    if (error.toString().contains('415')) {
      return const AppError.document(
        message: 'Unsupported file format',
        errorType: DocumentErrorType.invalidFormat,
      );
    }

    return AppError.document(
      message: defaultMessage,
      errorType: DocumentErrorType.uploadFailed,
    );
  }
}
