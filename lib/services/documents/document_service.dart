import 'dart:io';
import '../../shared/models/models.dart';

/// Service interface for document upload and verification management
/// Handles document operations for driver verification process
abstract class DocumentService {
  /// Upload a document for verification
  /// [file] - The document file to upload
  /// [documentType] - Type of document (license, insurance, registration, vehicle_photo)
  /// [description] - Optional description for the document
  /// Returns the uploaded document information
  Future<VerificationDocument> uploadDocument(
    File file,
    DocumentType documentType, {
    String? description,
  });

  /// Get all uploaded documents for the current driver
  /// Returns list of verification documents
  Future<List<VerificationDocument>> getDocuments();

  /// Delete a specific document
  /// [documentId] - ID of the document to delete
  Future<void> deleteDocument(String documentId);

  /// Submit documents for verification review
  /// Creates a verification request for admin review
  Future<void> submitForVerification();

  /// Get current verification status
  /// Returns the verification status with all documents
  Future<VerificationStatus> getVerificationStatus();

  /// Check if all required documents are uploaded
  /// Returns true if all required documents are present
  Future<bool> hasAllRequiredDocuments();

  /// Get required document types that are missing
  /// Returns list of missing document types
  Future<List<DocumentType>> getMissingDocuments();
}
