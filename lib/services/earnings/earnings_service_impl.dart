import '../../shared/models/models.dart';
import '../../core/errors/app_error.dart';
import '../api/api_client.dart';
import '../storage/driver_storage_helper.dart';
import 'earnings_service.dart';

/// Implementation of EarningsService
/// Handles earnings and statistics operations with API integration and caching
class EarningsServiceImpl implements EarningsService {
  final ApiClient _apiClient;
  final DriverStorageHelper _storageHelper;

  EarningsServiceImpl({
    required ApiClient apiClient,
    required DriverStorageHelper storageHelper,
  }) : _apiClient = apiClient,
       _storageHelper = storageHelper;

  @override
  Future<EarningsInfo> getEarnings() async {
    try {
      final response = await _apiClient.get('/drivers/earnings');
      final earnings = EarningsInfo.fromJson(response);

      // Cache earnings data for offline access
      await _storageHelper.storeEarningsInfo(earnings);

      return earnings;
    } catch (e) {
      // If API fails, try to return cached data
      final cachedEarnings = await _storageHelper.getEarningsInfo();
      if (cachedEarnings != null) {
        return cachedEarnings;
      }

      throw _handleError(e);
    }
  }

  @override
  Future<DriverStats> getDriverStats() async {
    try {
      final response = await _apiClient.get('/drivers/stats');
      final stats = DriverStats.fromJson(response);

      // Cache stats data for offline access
      await _storageHelper.storeDriverStats(stats);

      return stats;
    } catch (e) {
      // If API fails, try to return cached data
      final cachedStats = await _storageHelper.getDriverStats();
      if (cachedStats != null) {
        return cachedStats;
      }

      throw _handleError(e);
    }
  }

  @override
  Future<EarningsInfo> getEarningsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final queryParams = {
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
      };

      final response = await _apiClient.get(
        '/drivers/earnings',
        queryParameters: queryParams,
      );

      return EarningsInfo.fromJson(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<Map<String, dynamic>> getEarningsBreakdown() async {
    try {
      final response = await _apiClient.get('/drivers/earnings/breakdown');
      return response;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<Map<String, dynamic>> getPerformanceMetrics() async {
    try {
      final response = await _apiClient.get('/drivers/performance');
      return response;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<void> refreshEarningsData() async {
    try {
      // Refresh both earnings and stats data
      await Future.wait([getEarnings(), getDriverStats()]);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// Handle and transform errors into appropriate AppError types
  AppError _handleError(dynamic error) {
    if (error is AppError) {
      return error;
    }

    // Handle different types of errors and convert to AppError
    if (error.toString().contains('network') ||
        error.toString().contains('connection')) {
      return const AppError.network(
        message:
            'Network connection failed. Please check your internet connection.',
      );
    }

    if (error.toString().contains('401') ||
        error.toString().contains('unauthorized')) {
      return const AppError.authentication(
        message: 'Authentication failed. Please login again.',
        errorCode: '401',
      );
    }

    if (error.toString().contains('validation') ||
        error.toString().contains('400')) {
      return const AppError.validation(
        message: 'Invalid earnings data request.',
        fieldErrors: {},
      );
    }

    if (error.toString().contains('404')) {
      return const AppError.server(
        message: 'Earnings data not found.',
        statusCode: 404,
      );
    }

    if (error.toString().contains('500')) {
      return const AppError.server(
        message: 'Server error while fetching earnings data.',
        statusCode: 500,
      );
    }

    return AppError.unknown(
      message: 'An unexpected error occurred while fetching earnings data.',
      exception: error,
    );
  }
}
