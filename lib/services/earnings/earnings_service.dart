import '../../shared/models/models.dart';

/// Service interface for earnings and statistics operations
/// Provides dedicated methods for driver earnings and performance data
abstract class EarningsService {
  /// Get comprehensive earnings information
  /// Includes total, weekly, daily earnings and ride counts
  Future<EarningsInfo> getEarnings();

  /// Get driver statistics and performance metrics
  /// Includes ratings, completion rates, and ride history
  Future<DriverStats> getDriverStats();

  /// Get earnings for a specific date range
  Future<EarningsInfo> getEarningsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get detailed earnings breakdown
  /// Includes earnings by ride type, time periods, etc.
  Future<Map<String, dynamic>> getEarningsBreakdown();

  /// Get performance metrics over time
  Future<Map<String, dynamic>> getPerformanceMetrics();

  /// Refresh earnings data from server
  Future<void> refreshEarningsData();
}
