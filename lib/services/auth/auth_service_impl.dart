import '../../features/auth/domain/auth_repository.dart';
import '../../shared/models/models.dart';
import '../../core/errors/app_error.dart';
import '../api/api_client.dart';
import 'auth_service.dart';

/// Implementation of AuthService for driver authentication
/// Provides high-level authentication operations with business logic
class AuthServiceImpl implements AuthService {
  final AuthRepository _authRepository;
  final ApiClient _apiClient;

  AuthServiceImpl({
    required AuthRepository authRepository,
    required ApiClient apiClient,
  }) : _authRepository = authRepository,
       _apiClient = apiClient;

  @override
  Future<AuthResult> register(UserRegistration registration) async {
    try {
      // Ensure user_type is set to "driver" for driver app
      final driverRegistration = registration.copyWith(userType: 'driver');

      final result = await _authRepository.register(driverRegistration);

      // Set the auth token in API client for subsequent requests
      _apiClient.setAuthToken(result.accessToken);

      return result;
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      // Validate input
      if (email.trim().isEmpty) {
        throw const AppError.validation(
          message: 'Email is required',
          fieldErrors: {'email': 'Email cannot be empty'},
        );
      }

      if (password.trim().isEmpty) {
        throw const AppError.validation(
          message: 'Password is required',
          fieldErrors: {'password': 'Password cannot be empty'},
        );
      }

      final result = await _authRepository.login(email.trim(), password);

      // Set the auth token in API client for subsequent requests
      _apiClient.setAuthToken(result.accessToken);

      return result;
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  @override
  Future<void> logout() async {
    try {
      await _authRepository.logout();

      // Clear the auth token from API client
      _apiClient.clearAuthToken();
    } catch (e) {
      // Even if logout fails, clear local data
      _apiClient.clearAuthToken();
      await _authRepository.clearAuthData();
      throw _handleAuthError(e);
    }
  }

  @override
  Future<User?> getCurrentUser() async {
    try {
      // Check if authenticated first
      if (!await isAuthenticated()) {
        return null;
      }

      return await _authRepository.getCurrentUser();
    } catch (e) {
      // If getting user fails due to auth issues, clear auth data
      if (e is AppError && e is AuthenticationError) {
        await clearAuthData();
      }
      return null;
    }
  }

  @override
  Future<User> updateProfile(Map<String, dynamic> updates) async {
    try {
      // Ensure user is authenticated
      if (!await isAuthenticated()) {
        throw const AppError.authentication(
          message: 'User must be authenticated to update profile',
          errorCode: 'NOT_AUTHENTICATED',
        );
      }

      return await _authRepository.updateProfile(updates);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  @override
  Future<String> uploadProfileImage(String imagePath) async {
    try {
      // Ensure user is authenticated
      if (!await isAuthenticated()) {
        throw const AppError.authentication(
          message: 'User must be authenticated to upload profile image',
          errorCode: 'NOT_AUTHENTICATED',
        );
      }

      return await _authRepository.uploadProfileImage(imagePath);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  @override
  Future<void> deleteProfileImage() async {
    try {
      // Ensure user is authenticated
      if (!await isAuthenticated()) {
        throw const AppError.authentication(
          message: 'User must be authenticated to delete profile image',
          errorCode: 'NOT_AUTHENTICATED',
        );
      }

      await _authRepository.deleteProfileImage();
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  @override
  Future<bool> verifyToken() async {
    try {
      final isValid = await _authRepository.verifyToken();

      if (!isValid) {
        // Clear invalid token
        _apiClient.clearAuthToken();
        await _authRepository.clearAuthData();
      }

      return isValid;
    } catch (e) {
      // If verification fails, assume token is invalid
      _apiClient.clearAuthToken();
      await _authRepository.clearAuthData();
      return false;
    }
  }

  @override
  Future<AuthResult> refreshToken() async {
    try {
      final result = await _authRepository.refreshToken();

      // Update the auth token in API client
      _apiClient.setAuthToken(result.accessToken);

      return result;
    } catch (e) {
      // If refresh fails, clear auth data
      _apiClient.clearAuthToken();
      await _authRepository.clearAuthData();
      throw _handleAuthError(e);
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      return await _authRepository.isAuthenticated();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<String?> getStoredToken() async {
    try {
      return await _authRepository.getStoredToken();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      await _authRepository.clearAuthData();
      _apiClient.clearAuthToken();
    } catch (e) {
      // Ensure API client is cleared even if storage clearing fails
      _apiClient.clearAuthToken();
      throw _handleAuthError(e);
    }
  }

  @override
  Future<bool> initializeAuth() async {
    try {
      // Get stored token
      final token = await getStoredToken();

      if (token == null) {
        return false;
      }

      // Set token in API client
      _apiClient.setAuthToken(token);

      // Verify token is still valid
      final isValid = await verifyToken();

      if (!isValid) {
        // Try to refresh token
        try {
          await refreshToken();
          return true;
        } catch (e) {
          // If refresh fails, user needs to login again
          await clearAuthData();
          return false;
        }
      }

      return true;
    } catch (e) {
      // If initialization fails, clear auth data
      await clearAuthData();
      return false;
    }
  }

  @override
  Future<bool> handleTokenExpiration() async {
    try {
      // Try to refresh the token
      await refreshToken();
      return true;
    } catch (e) {
      // If refresh fails, clear auth data and require re-login
      await clearAuthData();
      return false;
    }
  }

  /// Handle and transform authentication errors
  AppError _handleAuthError(dynamic error) {
    if (error is AppError) {
      return error;
    }

    // Handle specific authentication error cases
    if (error.toString().contains('401') ||
        error.toString().contains('unauthorized') ||
        error.toString().contains('invalid_credentials')) {
      return const AppError.authentication(
        message: 'Invalid email or password. Please try again.',
        errorCode: 'INVALID_CREDENTIALS',
      );
    }

    if (error.toString().contains('403') ||
        error.toString().contains('forbidden')) {
      return const AppError.authentication(
        message: 'Access denied. Please contact support.',
        errorCode: 'ACCESS_DENIED',
      );
    }

    if (error.toString().contains('409') ||
        error.toString().contains('conflict') ||
        error.toString().contains('email_already_exists')) {
      return const AppError.validation(
        message: 'An account with this email already exists.',
        fieldErrors: {'email': 'Email is already registered'},
      );
    }

    if (error.toString().contains('network') ||
        error.toString().contains('connection')) {
      return const AppError.network(
        message:
            'Network connection failed. Please check your internet connection.',
      );
    }

    if (error.toString().contains('validation') ||
        error.toString().contains('400')) {
      return const AppError.validation(
        message: 'Please check your input and try again.',
        fieldErrors: {},
      );
    }

    if (error.toString().contains('500') ||
        error.toString().contains('server')) {
      return const AppError.server(
        message: 'Server error occurred. Please try again later.',
        statusCode: 500,
      );
    }

    return AppError.unknown(
      message: 'An unexpected error occurred during authentication.',
      exception: error,
    );
  }
}
