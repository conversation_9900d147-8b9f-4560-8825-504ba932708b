import '../../../shared/models/models.dart';

/// Abstract interface for authentication service operations
/// Provides high-level authentication methods for driver app
abstract class AuthService {
  /// Register a new driver with the provided registration data
  /// Automatically sets user_type to "driver"
  Future<AuthResult> register(UserRegistration registration);

  /// Login with email and password for drivers
  /// Automatically sets user_type to "driver"
  Future<AuthResult> login(String email, String password);

  /// Logout the current driver and clear all authentication data
  Future<void> logout();

  /// Get the current authenticated driver user
  Future<User?> getCurrentUser();

  /// Update the current driver's profile
  Future<User> updateProfile(Map<String, dynamic> updates);

  /// Upload profile image for the current driver
  Future<String> uploadProfileImage(String imagePath);

  /// Delete profile image for the current driver
  Future<void> deleteProfileImage();

  /// Verify if the current authentication token is valid
  Future<bool> verifyToken();

  /// Refresh the authentication token
  Future<AuthResult> refreshToken();

  /// Check if the driver is currently authenticated
  Future<bool> isAuthenticated();

  /// Get the stored authentication token
  Future<String?> getStoredToken();

  /// Clear all authentication data
  Future<void> clearAuthData();

  /// Initialize authentication state on app startup
  Future<bool> initializeAuth();

  /// Handle token expiration and automatic refresh
  Future<bool> handleTokenExpiration();
}
