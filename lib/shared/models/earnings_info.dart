import 'package:freezed_annotation/freezed_annotation.dart';

part 'earnings_info.freezed.dart';
part 'earnings_info.g.dart';

@freezed
class EarningsInfo with _$EarningsInfo {
  const factory EarningsInfo({
    required double totalEarnings,
    required double weeklyEarnings,
    required double pendingPayouts,
    required double todayEarnings,
    required int totalRides,
    required int weeklyRides,
    required int todayRides,
    required double averageRating,
    DateTime? lastPayoutDate,
    String? currency,
  }) = _EarningsInfo;

  factory EarningsInfo.fromJson(Map<String, dynamic> json) =>
      _$EarningsInfoFromJson(json);
}
