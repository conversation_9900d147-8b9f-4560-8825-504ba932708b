// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VehicleInfoImpl _$$VehicleInfoImplFromJson(Map<String, dynamic> json) =>
    _$VehicleInfoImpl(
      make: json['make'] as String,
      model: json['model'] as String,
      year: (json['year'] as num).toInt(),
      color: json['color'] as String,
      licensePlate: json['licensePlate'] as String,
      id: json['id'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$VehicleInfoImplToJson(_$VehicleInfoImpl instance) =>
    <String, dynamic>{
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'color': instance.color,
      'licensePlate': instance.licensePlate,
      'id': instance.id,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$VehicleInfoCreateImpl _$$VehicleInfoCreateImplFromJson(
  Map<String, dynamic> json,
) => _$VehicleInfoCreateImpl(
  make: json['make'] as String,
  model: json['model'] as String,
  year: (json['year'] as num).toInt(),
  color: json['color'] as String,
  licensePlate: json['licensePlate'] as String,
);

Map<String, dynamic> _$$VehicleInfoCreateImplToJson(
  _$VehicleInfoCreateImpl instance,
) => <String, dynamic>{
  'make': instance.make,
  'model': instance.model,
  'year': instance.year,
  'color': instance.color,
  'licensePlate': instance.licensePlate,
};

_$VehicleInfoUpdateImpl _$$VehicleInfoUpdateImplFromJson(
  Map<String, dynamic> json,
) => _$VehicleInfoUpdateImpl(
  make: json['make'] as String?,
  model: json['model'] as String?,
  year: (json['year'] as num?)?.toInt(),
  color: json['color'] as String?,
  licensePlate: json['licensePlate'] as String?,
);

Map<String, dynamic> _$$VehicleInfoUpdateImplToJson(
  _$VehicleInfoUpdateImpl instance,
) => <String, dynamic>{
  'make': instance.make,
  'model': instance.model,
  'year': instance.year,
  'color': instance.color,
  'licensePlate': instance.licensePlate,
};
