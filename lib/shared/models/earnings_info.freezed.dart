// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'earnings_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

EarningsInfo _$EarningsInfoFromJson(Map<String, dynamic> json) {
  return _EarningsInfo.fromJson(json);
}

/// @nodoc
mixin _$EarningsInfo {
  double get totalEarnings => throw _privateConstructorUsedError;
  double get weeklyEarnings => throw _privateConstructorUsedError;
  double get pendingPayouts => throw _privateConstructorUsedError;
  double get todayEarnings => throw _privateConstructorUsedError;
  int get totalRides => throw _privateConstructorUsedError;
  int get weeklyRides => throw _privateConstructorUsedError;
  int get todayRides => throw _privateConstructorUsedError;
  double get averageRating => throw _privateConstructorUsedError;
  DateTime? get lastPayoutDate => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_EarningsInfo value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_EarningsInfo value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_EarningsInfo value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this EarningsInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EarningsInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EarningsInfoCopyWith<EarningsInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EarningsInfoCopyWith<$Res> {
  factory $EarningsInfoCopyWith(
    EarningsInfo value,
    $Res Function(EarningsInfo) then,
  ) = _$EarningsInfoCopyWithImpl<$Res, EarningsInfo>;
  @useResult
  $Res call({
    double totalEarnings,
    double weeklyEarnings,
    double pendingPayouts,
    double todayEarnings,
    int totalRides,
    int weeklyRides,
    int todayRides,
    double averageRating,
    DateTime? lastPayoutDate,
    String? currency,
  });
}

/// @nodoc
class _$EarningsInfoCopyWithImpl<$Res, $Val extends EarningsInfo>
    implements $EarningsInfoCopyWith<$Res> {
  _$EarningsInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EarningsInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalEarnings = null,
    Object? weeklyEarnings = null,
    Object? pendingPayouts = null,
    Object? todayEarnings = null,
    Object? totalRides = null,
    Object? weeklyRides = null,
    Object? todayRides = null,
    Object? averageRating = null,
    Object? lastPayoutDate = freezed,
    Object? currency = freezed,
  }) {
    return _then(
      _value.copyWith(
            totalEarnings: null == totalEarnings
                ? _value.totalEarnings
                : totalEarnings // ignore: cast_nullable_to_non_nullable
                      as double,
            weeklyEarnings: null == weeklyEarnings
                ? _value.weeklyEarnings
                : weeklyEarnings // ignore: cast_nullable_to_non_nullable
                      as double,
            pendingPayouts: null == pendingPayouts
                ? _value.pendingPayouts
                : pendingPayouts // ignore: cast_nullable_to_non_nullable
                      as double,
            todayEarnings: null == todayEarnings
                ? _value.todayEarnings
                : todayEarnings // ignore: cast_nullable_to_non_nullable
                      as double,
            totalRides: null == totalRides
                ? _value.totalRides
                : totalRides // ignore: cast_nullable_to_non_nullable
                      as int,
            weeklyRides: null == weeklyRides
                ? _value.weeklyRides
                : weeklyRides // ignore: cast_nullable_to_non_nullable
                      as int,
            todayRides: null == todayRides
                ? _value.todayRides
                : todayRides // ignore: cast_nullable_to_non_nullable
                      as int,
            averageRating: null == averageRating
                ? _value.averageRating
                : averageRating // ignore: cast_nullable_to_non_nullable
                      as double,
            lastPayoutDate: freezed == lastPayoutDate
                ? _value.lastPayoutDate
                : lastPayoutDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            currency: freezed == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EarningsInfoImplCopyWith<$Res>
    implements $EarningsInfoCopyWith<$Res> {
  factory _$$EarningsInfoImplCopyWith(
    _$EarningsInfoImpl value,
    $Res Function(_$EarningsInfoImpl) then,
  ) = __$$EarningsInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    double totalEarnings,
    double weeklyEarnings,
    double pendingPayouts,
    double todayEarnings,
    int totalRides,
    int weeklyRides,
    int todayRides,
    double averageRating,
    DateTime? lastPayoutDate,
    String? currency,
  });
}

/// @nodoc
class __$$EarningsInfoImplCopyWithImpl<$Res>
    extends _$EarningsInfoCopyWithImpl<$Res, _$EarningsInfoImpl>
    implements _$$EarningsInfoImplCopyWith<$Res> {
  __$$EarningsInfoImplCopyWithImpl(
    _$EarningsInfoImpl _value,
    $Res Function(_$EarningsInfoImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EarningsInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalEarnings = null,
    Object? weeklyEarnings = null,
    Object? pendingPayouts = null,
    Object? todayEarnings = null,
    Object? totalRides = null,
    Object? weeklyRides = null,
    Object? todayRides = null,
    Object? averageRating = null,
    Object? lastPayoutDate = freezed,
    Object? currency = freezed,
  }) {
    return _then(
      _$EarningsInfoImpl(
        totalEarnings: null == totalEarnings
            ? _value.totalEarnings
            : totalEarnings // ignore: cast_nullable_to_non_nullable
                  as double,
        weeklyEarnings: null == weeklyEarnings
            ? _value.weeklyEarnings
            : weeklyEarnings // ignore: cast_nullable_to_non_nullable
                  as double,
        pendingPayouts: null == pendingPayouts
            ? _value.pendingPayouts
            : pendingPayouts // ignore: cast_nullable_to_non_nullable
                  as double,
        todayEarnings: null == todayEarnings
            ? _value.todayEarnings
            : todayEarnings // ignore: cast_nullable_to_non_nullable
                  as double,
        totalRides: null == totalRides
            ? _value.totalRides
            : totalRides // ignore: cast_nullable_to_non_nullable
                  as int,
        weeklyRides: null == weeklyRides
            ? _value.weeklyRides
            : weeklyRides // ignore: cast_nullable_to_non_nullable
                  as int,
        todayRides: null == todayRides
            ? _value.todayRides
            : todayRides // ignore: cast_nullable_to_non_nullable
                  as int,
        averageRating: null == averageRating
            ? _value.averageRating
            : averageRating // ignore: cast_nullable_to_non_nullable
                  as double,
        lastPayoutDate: freezed == lastPayoutDate
            ? _value.lastPayoutDate
            : lastPayoutDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        currency: freezed == currency
            ? _value.currency
            : currency // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EarningsInfoImpl implements _EarningsInfo {
  const _$EarningsInfoImpl({
    required this.totalEarnings,
    required this.weeklyEarnings,
    required this.pendingPayouts,
    required this.todayEarnings,
    required this.totalRides,
    required this.weeklyRides,
    required this.todayRides,
    required this.averageRating,
    this.lastPayoutDate,
    this.currency,
  });

  factory _$EarningsInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$EarningsInfoImplFromJson(json);

  @override
  final double totalEarnings;
  @override
  final double weeklyEarnings;
  @override
  final double pendingPayouts;
  @override
  final double todayEarnings;
  @override
  final int totalRides;
  @override
  final int weeklyRides;
  @override
  final int todayRides;
  @override
  final double averageRating;
  @override
  final DateTime? lastPayoutDate;
  @override
  final String? currency;

  @override
  String toString() {
    return 'EarningsInfo(totalEarnings: $totalEarnings, weeklyEarnings: $weeklyEarnings, pendingPayouts: $pendingPayouts, todayEarnings: $todayEarnings, totalRides: $totalRides, weeklyRides: $weeklyRides, todayRides: $todayRides, averageRating: $averageRating, lastPayoutDate: $lastPayoutDate, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EarningsInfoImpl &&
            (identical(other.totalEarnings, totalEarnings) ||
                other.totalEarnings == totalEarnings) &&
            (identical(other.weeklyEarnings, weeklyEarnings) ||
                other.weeklyEarnings == weeklyEarnings) &&
            (identical(other.pendingPayouts, pendingPayouts) ||
                other.pendingPayouts == pendingPayouts) &&
            (identical(other.todayEarnings, todayEarnings) ||
                other.todayEarnings == todayEarnings) &&
            (identical(other.totalRides, totalRides) ||
                other.totalRides == totalRides) &&
            (identical(other.weeklyRides, weeklyRides) ||
                other.weeklyRides == weeklyRides) &&
            (identical(other.todayRides, todayRides) ||
                other.todayRides == todayRides) &&
            (identical(other.averageRating, averageRating) ||
                other.averageRating == averageRating) &&
            (identical(other.lastPayoutDate, lastPayoutDate) ||
                other.lastPayoutDate == lastPayoutDate) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalEarnings,
    weeklyEarnings,
    pendingPayouts,
    todayEarnings,
    totalRides,
    weeklyRides,
    todayRides,
    averageRating,
    lastPayoutDate,
    currency,
  );

  /// Create a copy of EarningsInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EarningsInfoImplCopyWith<_$EarningsInfoImpl> get copyWith =>
      __$$EarningsInfoImplCopyWithImpl<_$EarningsInfoImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_EarningsInfo value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_EarningsInfo value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_EarningsInfo value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$EarningsInfoImplToJson(this);
  }
}

abstract class _EarningsInfo implements EarningsInfo {
  const factory _EarningsInfo({
    required final double totalEarnings,
    required final double weeklyEarnings,
    required final double pendingPayouts,
    required final double todayEarnings,
    required final int totalRides,
    required final int weeklyRides,
    required final int todayRides,
    required final double averageRating,
    final DateTime? lastPayoutDate,
    final String? currency,
  }) = _$EarningsInfoImpl;

  factory _EarningsInfo.fromJson(Map<String, dynamic> json) =
      _$EarningsInfoImpl.fromJson;

  @override
  double get totalEarnings;
  @override
  double get weeklyEarnings;
  @override
  double get pendingPayouts;
  @override
  double get todayEarnings;
  @override
  int get totalRides;
  @override
  int get weeklyRides;
  @override
  int get todayRides;
  @override
  double get averageRating;
  @override
  DateTime? get lastPayoutDate;
  @override
  String? get currency;

  /// Create a copy of EarningsInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EarningsInfoImplCopyWith<_$EarningsInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
