// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_stats.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

DriverStats _$DriverStatsFromJson(Map<String, dynamic> json) {
  return _DriverStats.fromJson(json);
}

/// @nodoc
mixin _$DriverStats {
  int get totalRides => throw _privateConstructorUsedError;
  double get averageRating => throw _privateConstructorUsedError;
  int get totalEarnings => throw _privateConstructorUsedError;
  int get completedRides => throw _privateConstructorUsedError;
  int get cancelledRides => throw _privateConstructorUsedError;
  double get acceptanceRate => throw _privateConstructorUsedError;
  double get completionRate => throw _privateConstructorUsedError;
  int get hoursOnline => throw _privateConstructorUsedError;
  DateTime? get lastRideDate => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverStats value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverStats value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverStats value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this DriverStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverStatsCopyWith<DriverStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverStatsCopyWith<$Res> {
  factory $DriverStatsCopyWith(
    DriverStats value,
    $Res Function(DriverStats) then,
  ) = _$DriverStatsCopyWithImpl<$Res, DriverStats>;
  @useResult
  $Res call({
    int totalRides,
    double averageRating,
    int totalEarnings,
    int completedRides,
    int cancelledRides,
    double acceptanceRate,
    double completionRate,
    int hoursOnline,
    DateTime? lastRideDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$DriverStatsCopyWithImpl<$Res, $Val extends DriverStats>
    implements $DriverStatsCopyWith<$Res> {
  _$DriverStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalRides = null,
    Object? averageRating = null,
    Object? totalEarnings = null,
    Object? completedRides = null,
    Object? cancelledRides = null,
    Object? acceptanceRate = null,
    Object? completionRate = null,
    Object? hoursOnline = null,
    Object? lastRideDate = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            totalRides: null == totalRides
                ? _value.totalRides
                : totalRides // ignore: cast_nullable_to_non_nullable
                      as int,
            averageRating: null == averageRating
                ? _value.averageRating
                : averageRating // ignore: cast_nullable_to_non_nullable
                      as double,
            totalEarnings: null == totalEarnings
                ? _value.totalEarnings
                : totalEarnings // ignore: cast_nullable_to_non_nullable
                      as int,
            completedRides: null == completedRides
                ? _value.completedRides
                : completedRides // ignore: cast_nullable_to_non_nullable
                      as int,
            cancelledRides: null == cancelledRides
                ? _value.cancelledRides
                : cancelledRides // ignore: cast_nullable_to_non_nullable
                      as int,
            acceptanceRate: null == acceptanceRate
                ? _value.acceptanceRate
                : acceptanceRate // ignore: cast_nullable_to_non_nullable
                      as double,
            completionRate: null == completionRate
                ? _value.completionRate
                : completionRate // ignore: cast_nullable_to_non_nullable
                      as double,
            hoursOnline: null == hoursOnline
                ? _value.hoursOnline
                : hoursOnline // ignore: cast_nullable_to_non_nullable
                      as int,
            lastRideDate: freezed == lastRideDate
                ? _value.lastRideDate
                : lastRideDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DriverStatsImplCopyWith<$Res>
    implements $DriverStatsCopyWith<$Res> {
  factory _$$DriverStatsImplCopyWith(
    _$DriverStatsImpl value,
    $Res Function(_$DriverStatsImpl) then,
  ) = __$$DriverStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalRides,
    double averageRating,
    int totalEarnings,
    int completedRides,
    int cancelledRides,
    double acceptanceRate,
    double completionRate,
    int hoursOnline,
    DateTime? lastRideDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$DriverStatsImplCopyWithImpl<$Res>
    extends _$DriverStatsCopyWithImpl<$Res, _$DriverStatsImpl>
    implements _$$DriverStatsImplCopyWith<$Res> {
  __$$DriverStatsImplCopyWithImpl(
    _$DriverStatsImpl _value,
    $Res Function(_$DriverStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalRides = null,
    Object? averageRating = null,
    Object? totalEarnings = null,
    Object? completedRides = null,
    Object? cancelledRides = null,
    Object? acceptanceRate = null,
    Object? completionRate = null,
    Object? hoursOnline = null,
    Object? lastRideDate = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$DriverStatsImpl(
        totalRides: null == totalRides
            ? _value.totalRides
            : totalRides // ignore: cast_nullable_to_non_nullable
                  as int,
        averageRating: null == averageRating
            ? _value.averageRating
            : averageRating // ignore: cast_nullable_to_non_nullable
                  as double,
        totalEarnings: null == totalEarnings
            ? _value.totalEarnings
            : totalEarnings // ignore: cast_nullable_to_non_nullable
                  as int,
        completedRides: null == completedRides
            ? _value.completedRides
            : completedRides // ignore: cast_nullable_to_non_nullable
                  as int,
        cancelledRides: null == cancelledRides
            ? _value.cancelledRides
            : cancelledRides // ignore: cast_nullable_to_non_nullable
                  as int,
        acceptanceRate: null == acceptanceRate
            ? _value.acceptanceRate
            : acceptanceRate // ignore: cast_nullable_to_non_nullable
                  as double,
        completionRate: null == completionRate
            ? _value.completionRate
            : completionRate // ignore: cast_nullable_to_non_nullable
                  as double,
        hoursOnline: null == hoursOnline
            ? _value.hoursOnline
            : hoursOnline // ignore: cast_nullable_to_non_nullable
                  as int,
        lastRideDate: freezed == lastRideDate
            ? _value.lastRideDate
            : lastRideDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverStatsImpl implements _DriverStats {
  const _$DriverStatsImpl({
    required this.totalRides,
    required this.averageRating,
    required this.totalEarnings,
    required this.completedRides,
    required this.cancelledRides,
    required this.acceptanceRate,
    required this.completionRate,
    required this.hoursOnline,
    this.lastRideDate,
    this.createdAt,
    this.updatedAt,
  });

  factory _$DriverStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverStatsImplFromJson(json);

  @override
  final int totalRides;
  @override
  final double averageRating;
  @override
  final int totalEarnings;
  @override
  final int completedRides;
  @override
  final int cancelledRides;
  @override
  final double acceptanceRate;
  @override
  final double completionRate;
  @override
  final int hoursOnline;
  @override
  final DateTime? lastRideDate;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'DriverStats(totalRides: $totalRides, averageRating: $averageRating, totalEarnings: $totalEarnings, completedRides: $completedRides, cancelledRides: $cancelledRides, acceptanceRate: $acceptanceRate, completionRate: $completionRate, hoursOnline: $hoursOnline, lastRideDate: $lastRideDate, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverStatsImpl &&
            (identical(other.totalRides, totalRides) ||
                other.totalRides == totalRides) &&
            (identical(other.averageRating, averageRating) ||
                other.averageRating == averageRating) &&
            (identical(other.totalEarnings, totalEarnings) ||
                other.totalEarnings == totalEarnings) &&
            (identical(other.completedRides, completedRides) ||
                other.completedRides == completedRides) &&
            (identical(other.cancelledRides, cancelledRides) ||
                other.cancelledRides == cancelledRides) &&
            (identical(other.acceptanceRate, acceptanceRate) ||
                other.acceptanceRate == acceptanceRate) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            (identical(other.hoursOnline, hoursOnline) ||
                other.hoursOnline == hoursOnline) &&
            (identical(other.lastRideDate, lastRideDate) ||
                other.lastRideDate == lastRideDate) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalRides,
    averageRating,
    totalEarnings,
    completedRides,
    cancelledRides,
    acceptanceRate,
    completionRate,
    hoursOnline,
    lastRideDate,
    createdAt,
    updatedAt,
  );

  /// Create a copy of DriverStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverStatsImplCopyWith<_$DriverStatsImpl> get copyWith =>
      __$$DriverStatsImplCopyWithImpl<_$DriverStatsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverStats value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverStats value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverStats value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverStatsImplToJson(this);
  }
}

abstract class _DriverStats implements DriverStats {
  const factory _DriverStats({
    required final int totalRides,
    required final double averageRating,
    required final int totalEarnings,
    required final int completedRides,
    required final int cancelledRides,
    required final double acceptanceRate,
    required final double completionRate,
    required final int hoursOnline,
    final DateTime? lastRideDate,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$DriverStatsImpl;

  factory _DriverStats.fromJson(Map<String, dynamic> json) =
      _$DriverStatsImpl.fromJson;

  @override
  int get totalRides;
  @override
  double get averageRating;
  @override
  int get totalEarnings;
  @override
  int get completedRides;
  @override
  int get cancelledRides;
  @override
  double get acceptanceRate;
  @override
  double get completionRate;
  @override
  int get hoursOnline;
  @override
  DateTime? get lastRideDate;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of DriverStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverStatsImplCopyWith<_$DriverStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
