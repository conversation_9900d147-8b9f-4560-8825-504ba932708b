// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'driver_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DriverProfileImpl _$$DriverProfileImplFromJson(Map<String, dynamic> json) =>
    _$DriverProfileImpl(
      userId: json['userId'] as String,
      licenseNumber: json['licenseNumber'] as String,
      vehicleInfo: json['vehicleInfo'] == null
          ? null
          : VehicleInfo.fromJson(json['vehicleInfo'] as Map<String, dynamic>),
      isVerified: json['isVerified'] as bool,
      isAvailable: json['isAvailable'] as bool,
      rating: (json['rating'] as num).toDouble(),
      totalRides: (json['totalRides'] as num).toInt(),
      currentLocation: json['currentLocation'] == null
          ? null
          : LocationInfo.fromJson(
              json['currentLocation'] as Map<String, dynamic>,
            ),
      stripeAccountId: json['stripeAccountId'] as String?,
      earnings: json['earnings'] == null
          ? null
          : EarningsInfo.fromJson(json['earnings'] as Map<String, dynamic>),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$DriverProfileImplToJson(_$DriverProfileImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'licenseNumber': instance.licenseNumber,
      'vehicleInfo': instance.vehicleInfo,
      'isVerified': instance.isVerified,
      'isAvailable': instance.isAvailable,
      'rating': instance.rating,
      'totalRides': instance.totalRides,
      'currentLocation': instance.currentLocation,
      'stripeAccountId': instance.stripeAccountId,
      'earnings': instance.earnings,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$DriverProfileCreateImpl _$$DriverProfileCreateImplFromJson(
  Map<String, dynamic> json,
) => _$DriverProfileCreateImpl(
  licenseNumber: json['licenseNumber'] as String,
  stripeAccountId: json['stripeAccountId'] as String?,
);

Map<String, dynamic> _$$DriverProfileCreateImplToJson(
  _$DriverProfileCreateImpl instance,
) => <String, dynamic>{
  'licenseNumber': instance.licenseNumber,
  'stripeAccountId': instance.stripeAccountId,
};

_$DriverProfileUpdateImpl _$$DriverProfileUpdateImplFromJson(
  Map<String, dynamic> json,
) => _$DriverProfileUpdateImpl(
  licenseNumber: json['licenseNumber'] as String?,
  stripeAccountId: json['stripeAccountId'] as String?,
);

Map<String, dynamic> _$$DriverProfileUpdateImplToJson(
  _$DriverProfileUpdateImpl instance,
) => <String, dynamic>{
  'licenseNumber': instance.licenseNumber,
  'stripeAccountId': instance.stripeAccountId,
};

_$DriverAvailabilityUpdateImpl _$$DriverAvailabilityUpdateImplFromJson(
  Map<String, dynamic> json,
) => _$DriverAvailabilityUpdateImpl(isAvailable: json['isAvailable'] as bool);

Map<String, dynamic> _$$DriverAvailabilityUpdateImplToJson(
  _$DriverAvailabilityUpdateImpl instance,
) => <String, dynamic>{'isAvailable': instance.isAvailable};
