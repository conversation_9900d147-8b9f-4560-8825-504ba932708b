// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'verification_document.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

VerificationDocument _$VerificationDocumentFromJson(Map<String, dynamic> json) {
  return _VerificationDocument.fromJson(json);
}

/// @nodoc
mixin _$VerificationDocument {
  String get id => throw _privateConstructorUsedError;
  DocumentType get documentType => throw _privateConstructorUsedError;
  String get documentUrl => throw _privateConstructorUsedError;
  DocumentStatus get status => throw _privateConstructorUsedError;
  DateTime get uploadedAt => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  DateTime? get reviewedAt => throw _privateConstructorUsedError;
  String? get reviewedBy => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VerificationDocument value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VerificationDocument value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VerificationDocument value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this VerificationDocument to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerificationDocumentCopyWith<VerificationDocument> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationDocumentCopyWith<$Res> {
  factory $VerificationDocumentCopyWith(
    VerificationDocument value,
    $Res Function(VerificationDocument) then,
  ) = _$VerificationDocumentCopyWithImpl<$Res, VerificationDocument>;
  @useResult
  $Res call({
    String id,
    DocumentType documentType,
    String documentUrl,
    DocumentStatus status,
    DateTime uploadedAt,
    String? rejectionReason,
    DateTime? reviewedAt,
    String? reviewedBy,
  });
}

/// @nodoc
class _$VerificationDocumentCopyWithImpl<
  $Res,
  $Val extends VerificationDocument
>
    implements $VerificationDocumentCopyWith<$Res> {
  _$VerificationDocumentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? documentType = null,
    Object? documentUrl = null,
    Object? status = null,
    Object? uploadedAt = null,
    Object? rejectionReason = freezed,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            documentType: null == documentType
                ? _value.documentType
                : documentType // ignore: cast_nullable_to_non_nullable
                      as DocumentType,
            documentUrl: null == documentUrl
                ? _value.documentUrl
                : documentUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as DocumentStatus,
            uploadedAt: null == uploadedAt
                ? _value.uploadedAt
                : uploadedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            rejectionReason: freezed == rejectionReason
                ? _value.rejectionReason
                : rejectionReason // ignore: cast_nullable_to_non_nullable
                      as String?,
            reviewedAt: freezed == reviewedAt
                ? _value.reviewedAt
                : reviewedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reviewedBy: freezed == reviewedBy
                ? _value.reviewedBy
                : reviewedBy // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VerificationDocumentImplCopyWith<$Res>
    implements $VerificationDocumentCopyWith<$Res> {
  factory _$$VerificationDocumentImplCopyWith(
    _$VerificationDocumentImpl value,
    $Res Function(_$VerificationDocumentImpl) then,
  ) = __$$VerificationDocumentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    DocumentType documentType,
    String documentUrl,
    DocumentStatus status,
    DateTime uploadedAt,
    String? rejectionReason,
    DateTime? reviewedAt,
    String? reviewedBy,
  });
}

/// @nodoc
class __$$VerificationDocumentImplCopyWithImpl<$Res>
    extends _$VerificationDocumentCopyWithImpl<$Res, _$VerificationDocumentImpl>
    implements _$$VerificationDocumentImplCopyWith<$Res> {
  __$$VerificationDocumentImplCopyWithImpl(
    _$VerificationDocumentImpl _value,
    $Res Function(_$VerificationDocumentImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? documentType = null,
    Object? documentUrl = null,
    Object? status = null,
    Object? uploadedAt = null,
    Object? rejectionReason = freezed,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
  }) {
    return _then(
      _$VerificationDocumentImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        documentType: null == documentType
            ? _value.documentType
            : documentType // ignore: cast_nullable_to_non_nullable
                  as DocumentType,
        documentUrl: null == documentUrl
            ? _value.documentUrl
            : documentUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as DocumentStatus,
        uploadedAt: null == uploadedAt
            ? _value.uploadedAt
            : uploadedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        rejectionReason: freezed == rejectionReason
            ? _value.rejectionReason
            : rejectionReason // ignore: cast_nullable_to_non_nullable
                  as String?,
        reviewedAt: freezed == reviewedAt
            ? _value.reviewedAt
            : reviewedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reviewedBy: freezed == reviewedBy
            ? _value.reviewedBy
            : reviewedBy // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VerificationDocumentImpl implements _VerificationDocument {
  const _$VerificationDocumentImpl({
    required this.id,
    required this.documentType,
    required this.documentUrl,
    required this.status,
    required this.uploadedAt,
    this.rejectionReason,
    this.reviewedAt,
    this.reviewedBy,
  });

  factory _$VerificationDocumentImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerificationDocumentImplFromJson(json);

  @override
  final String id;
  @override
  final DocumentType documentType;
  @override
  final String documentUrl;
  @override
  final DocumentStatus status;
  @override
  final DateTime uploadedAt;
  @override
  final String? rejectionReason;
  @override
  final DateTime? reviewedAt;
  @override
  final String? reviewedBy;

  @override
  String toString() {
    return 'VerificationDocument(id: $id, documentType: $documentType, documentUrl: $documentUrl, status: $status, uploadedAt: $uploadedAt, rejectionReason: $rejectionReason, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationDocumentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.uploadedAt, uploadedAt) ||
                other.uploadedAt == uploadedAt) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.reviewedBy, reviewedBy) ||
                other.reviewedBy == reviewedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    documentType,
    documentUrl,
    status,
    uploadedAt,
    rejectionReason,
    reviewedAt,
    reviewedBy,
  );

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationDocumentImplCopyWith<_$VerificationDocumentImpl>
  get copyWith =>
      __$$VerificationDocumentImplCopyWithImpl<_$VerificationDocumentImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VerificationDocument value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VerificationDocument value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VerificationDocument value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$VerificationDocumentImplToJson(this);
  }
}

abstract class _VerificationDocument implements VerificationDocument {
  const factory _VerificationDocument({
    required final String id,
    required final DocumentType documentType,
    required final String documentUrl,
    required final DocumentStatus status,
    required final DateTime uploadedAt,
    final String? rejectionReason,
    final DateTime? reviewedAt,
    final String? reviewedBy,
  }) = _$VerificationDocumentImpl;

  factory _VerificationDocument.fromJson(Map<String, dynamic> json) =
      _$VerificationDocumentImpl.fromJson;

  @override
  String get id;
  @override
  DocumentType get documentType;
  @override
  String get documentUrl;
  @override
  DocumentStatus get status;
  @override
  DateTime get uploadedAt;
  @override
  String? get rejectionReason;
  @override
  DateTime? get reviewedAt;
  @override
  String? get reviewedBy;

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationDocumentImplCopyWith<_$VerificationDocumentImpl>
  get copyWith => throw _privateConstructorUsedError;
}

DocumentUploadRequest _$DocumentUploadRequestFromJson(
  Map<String, dynamic> json,
) {
  return _DocumentUploadRequest.fromJson(json);
}

/// @nodoc
mixin _$DocumentUploadRequest {
  DocumentType get documentType => throw _privateConstructorUsedError;
  String get fileName => throw _privateConstructorUsedError;
  String get fileContent =>
      throw _privateConstructorUsedError; // Base64 encoded
  String? get description => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DocumentUploadRequest value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DocumentUploadRequest value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DocumentUploadRequest value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this DocumentUploadRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DocumentUploadRequestCopyWith<DocumentUploadRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentUploadRequestCopyWith<$Res> {
  factory $DocumentUploadRequestCopyWith(
    DocumentUploadRequest value,
    $Res Function(DocumentUploadRequest) then,
  ) = _$DocumentUploadRequestCopyWithImpl<$Res, DocumentUploadRequest>;
  @useResult
  $Res call({
    DocumentType documentType,
    String fileName,
    String fileContent,
    String? description,
  });
}

/// @nodoc
class _$DocumentUploadRequestCopyWithImpl<
  $Res,
  $Val extends DocumentUploadRequest
>
    implements $DocumentUploadRequestCopyWith<$Res> {
  _$DocumentUploadRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? documentType = null,
    Object? fileName = null,
    Object? fileContent = null,
    Object? description = freezed,
  }) {
    return _then(
      _value.copyWith(
            documentType: null == documentType
                ? _value.documentType
                : documentType // ignore: cast_nullable_to_non_nullable
                      as DocumentType,
            fileName: null == fileName
                ? _value.fileName
                : fileName // ignore: cast_nullable_to_non_nullable
                      as String,
            fileContent: null == fileContent
                ? _value.fileContent
                : fileContent // ignore: cast_nullable_to_non_nullable
                      as String,
            description: freezed == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DocumentUploadRequestImplCopyWith<$Res>
    implements $DocumentUploadRequestCopyWith<$Res> {
  factory _$$DocumentUploadRequestImplCopyWith(
    _$DocumentUploadRequestImpl value,
    $Res Function(_$DocumentUploadRequestImpl) then,
  ) = __$$DocumentUploadRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    DocumentType documentType,
    String fileName,
    String fileContent,
    String? description,
  });
}

/// @nodoc
class __$$DocumentUploadRequestImplCopyWithImpl<$Res>
    extends
        _$DocumentUploadRequestCopyWithImpl<$Res, _$DocumentUploadRequestImpl>
    implements _$$DocumentUploadRequestImplCopyWith<$Res> {
  __$$DocumentUploadRequestImplCopyWithImpl(
    _$DocumentUploadRequestImpl _value,
    $Res Function(_$DocumentUploadRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? documentType = null,
    Object? fileName = null,
    Object? fileContent = null,
    Object? description = freezed,
  }) {
    return _then(
      _$DocumentUploadRequestImpl(
        documentType: null == documentType
            ? _value.documentType
            : documentType // ignore: cast_nullable_to_non_nullable
                  as DocumentType,
        fileName: null == fileName
            ? _value.fileName
            : fileName // ignore: cast_nullable_to_non_nullable
                  as String,
        fileContent: null == fileContent
            ? _value.fileContent
            : fileContent // ignore: cast_nullable_to_non_nullable
                  as String,
        description: freezed == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DocumentUploadRequestImpl implements _DocumentUploadRequest {
  const _$DocumentUploadRequestImpl({
    required this.documentType,
    required this.fileName,
    required this.fileContent,
    this.description,
  });

  factory _$DocumentUploadRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$DocumentUploadRequestImplFromJson(json);

  @override
  final DocumentType documentType;
  @override
  final String fileName;
  @override
  final String fileContent;
  // Base64 encoded
  @override
  final String? description;

  @override
  String toString() {
    return 'DocumentUploadRequest(documentType: $documentType, fileName: $fileName, fileContent: $fileContent, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentUploadRequestImpl &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.fileContent, fileContent) ||
                other.fileContent == fileContent) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    documentType,
    fileName,
    fileContent,
    description,
  );

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentUploadRequestImplCopyWith<_$DocumentUploadRequestImpl>
  get copyWith =>
      __$$DocumentUploadRequestImplCopyWithImpl<_$DocumentUploadRequestImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DocumentUploadRequest value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DocumentUploadRequest value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DocumentUploadRequest value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$DocumentUploadRequestImplToJson(this);
  }
}

abstract class _DocumentUploadRequest implements DocumentUploadRequest {
  const factory _DocumentUploadRequest({
    required final DocumentType documentType,
    required final String fileName,
    required final String fileContent,
    final String? description,
  }) = _$DocumentUploadRequestImpl;

  factory _DocumentUploadRequest.fromJson(Map<String, dynamic> json) =
      _$DocumentUploadRequestImpl.fromJson;

  @override
  DocumentType get documentType;
  @override
  String get fileName;
  @override
  String get fileContent; // Base64 encoded
  @override
  String? get description;

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentUploadRequestImplCopyWith<_$DocumentUploadRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

VerificationStatus _$VerificationStatusFromJson(Map<String, dynamic> json) {
  return _VerificationStatus.fromJson(json);
}

/// @nodoc
mixin _$VerificationStatus {
  String get status => throw _privateConstructorUsedError;
  List<VerificationDocument> get documents =>
      throw _privateConstructorUsedError;
  bool get isComplete => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  DateTime? get submittedAt => throw _privateConstructorUsedError;
  DateTime? get reviewedAt => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VerificationStatus value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VerificationStatus value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VerificationStatus value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this VerificationStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerificationStatusCopyWith<VerificationStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationStatusCopyWith<$Res> {
  factory $VerificationStatusCopyWith(
    VerificationStatus value,
    $Res Function(VerificationStatus) then,
  ) = _$VerificationStatusCopyWithImpl<$Res, VerificationStatus>;
  @useResult
  $Res call({
    String status,
    List<VerificationDocument> documents,
    bool isComplete,
    String? message,
    DateTime? submittedAt,
    DateTime? reviewedAt,
  });
}

/// @nodoc
class _$VerificationStatusCopyWithImpl<$Res, $Val extends VerificationStatus>
    implements $VerificationStatusCopyWith<$Res> {
  _$VerificationStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? documents = null,
    Object? isComplete = null,
    Object? message = freezed,
    Object? submittedAt = freezed,
    Object? reviewedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            status: null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                      as String,
            documents: null == documents
                ? _value.documents
                : documents // ignore: cast_nullable_to_non_nullable
                      as List<VerificationDocument>,
            isComplete: null == isComplete
                ? _value.isComplete
                : isComplete // ignore: cast_nullable_to_non_nullable
                      as bool,
            message: freezed == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String?,
            submittedAt: freezed == submittedAt
                ? _value.submittedAt
                : submittedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            reviewedAt: freezed == reviewedAt
                ? _value.reviewedAt
                : reviewedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VerificationStatusImplCopyWith<$Res>
    implements $VerificationStatusCopyWith<$Res> {
  factory _$$VerificationStatusImplCopyWith(
    _$VerificationStatusImpl value,
    $Res Function(_$VerificationStatusImpl) then,
  ) = __$$VerificationStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String status,
    List<VerificationDocument> documents,
    bool isComplete,
    String? message,
    DateTime? submittedAt,
    DateTime? reviewedAt,
  });
}

/// @nodoc
class __$$VerificationStatusImplCopyWithImpl<$Res>
    extends _$VerificationStatusCopyWithImpl<$Res, _$VerificationStatusImpl>
    implements _$$VerificationStatusImplCopyWith<$Res> {
  __$$VerificationStatusImplCopyWithImpl(
    _$VerificationStatusImpl _value,
    $Res Function(_$VerificationStatusImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? documents = null,
    Object? isComplete = null,
    Object? message = freezed,
    Object? submittedAt = freezed,
    Object? reviewedAt = freezed,
  }) {
    return _then(
      _$VerificationStatusImpl(
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as String,
        documents: null == documents
            ? _value._documents
            : documents // ignore: cast_nullable_to_non_nullable
                  as List<VerificationDocument>,
        isComplete: null == isComplete
            ? _value.isComplete
            : isComplete // ignore: cast_nullable_to_non_nullable
                  as bool,
        message: freezed == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
        submittedAt: freezed == submittedAt
            ? _value.submittedAt
            : submittedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        reviewedAt: freezed == reviewedAt
            ? _value.reviewedAt
            : reviewedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VerificationStatusImpl implements _VerificationStatus {
  const _$VerificationStatusImpl({
    required this.status,
    required final List<VerificationDocument> documents,
    required this.isComplete,
    this.message,
    this.submittedAt,
    this.reviewedAt,
  }) : _documents = documents;

  factory _$VerificationStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerificationStatusImplFromJson(json);

  @override
  final String status;
  final List<VerificationDocument> _documents;
  @override
  List<VerificationDocument> get documents {
    if (_documents is EqualUnmodifiableListView) return _documents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_documents);
  }

  @override
  final bool isComplete;
  @override
  final String? message;
  @override
  final DateTime? submittedAt;
  @override
  final DateTime? reviewedAt;

  @override
  String toString() {
    return 'VerificationStatus(status: $status, documents: $documents, isComplete: $isComplete, message: $message, submittedAt: $submittedAt, reviewedAt: $reviewedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationStatusImpl &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(
              other._documents,
              _documents,
            ) &&
            (identical(other.isComplete, isComplete) ||
                other.isComplete == isComplete) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.submittedAt, submittedAt) ||
                other.submittedAt == submittedAt) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    status,
    const DeepCollectionEquality().hash(_documents),
    isComplete,
    message,
    submittedAt,
    reviewedAt,
  );

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationStatusImplCopyWith<_$VerificationStatusImpl> get copyWith =>
      __$$VerificationStatusImplCopyWithImpl<_$VerificationStatusImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VerificationStatus value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VerificationStatus value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VerificationStatus value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$VerificationStatusImplToJson(this);
  }
}

abstract class _VerificationStatus implements VerificationStatus {
  const factory _VerificationStatus({
    required final String status,
    required final List<VerificationDocument> documents,
    required final bool isComplete,
    final String? message,
    final DateTime? submittedAt,
    final DateTime? reviewedAt,
  }) = _$VerificationStatusImpl;

  factory _VerificationStatus.fromJson(Map<String, dynamic> json) =
      _$VerificationStatusImpl.fromJson;

  @override
  String get status;
  @override
  List<VerificationDocument> get documents;
  @override
  bool get isComplete;
  @override
  String? get message;
  @override
  DateTime? get submittedAt;
  @override
  DateTime? get reviewedAt;

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationStatusImplCopyWith<_$VerificationStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
