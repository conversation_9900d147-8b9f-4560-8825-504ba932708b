// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verification_document.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VerificationDocumentImpl _$$VerificationDocumentImplFromJson(
  Map<String, dynamic> json,
) => _$VerificationDocumentImpl(
  id: json['id'] as String,
  documentType: $enumDecode(_$DocumentTypeEnumMap, json['documentType']),
  documentUrl: json['documentUrl'] as String,
  status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
  uploadedAt: DateTime.parse(json['uploadedAt'] as String),
  rejectionReason: json['rejectionReason'] as String?,
  reviewedAt: json['reviewedAt'] == null
      ? null
      : DateTime.parse(json['reviewedAt'] as String),
  reviewedBy: json['reviewedBy'] as String?,
);

Map<String, dynamic> _$$VerificationDocumentImplToJson(
  _$VerificationDocumentImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'documentType': _$DocumentTypeEnumMap[instance.documentType]!,
  'documentUrl': instance.documentUrl,
  'status': _$DocumentStatusEnumMap[instance.status]!,
  'uploadedAt': instance.uploadedAt.toIso8601String(),
  'rejectionReason': instance.rejectionReason,
  'reviewedAt': instance.reviewedAt?.toIso8601String(),
  'reviewedBy': instance.reviewedBy,
};

const _$DocumentTypeEnumMap = {
  DocumentType.license: 'license',
  DocumentType.insurance: 'insurance',
  DocumentType.registration: 'registration',
  DocumentType.vehiclePhoto: 'vehicle_photo',
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.pending: 'pending',
  DocumentStatus.approved: 'approved',
  DocumentStatus.rejected: 'rejected',
};

_$DocumentUploadRequestImpl _$$DocumentUploadRequestImplFromJson(
  Map<String, dynamic> json,
) => _$DocumentUploadRequestImpl(
  documentType: $enumDecode(_$DocumentTypeEnumMap, json['documentType']),
  fileName: json['fileName'] as String,
  fileContent: json['fileContent'] as String,
  description: json['description'] as String?,
);

Map<String, dynamic> _$$DocumentUploadRequestImplToJson(
  _$DocumentUploadRequestImpl instance,
) => <String, dynamic>{
  'documentType': _$DocumentTypeEnumMap[instance.documentType]!,
  'fileName': instance.fileName,
  'fileContent': instance.fileContent,
  'description': instance.description,
};

_$VerificationStatusImpl _$$VerificationStatusImplFromJson(
  Map<String, dynamic> json,
) => _$VerificationStatusImpl(
  status: json['status'] as String,
  documents: (json['documents'] as List<dynamic>)
      .map((e) => VerificationDocument.fromJson(e as Map<String, dynamic>))
      .toList(),
  isComplete: json['isComplete'] as bool,
  message: json['message'] as String?,
  submittedAt: json['submittedAt'] == null
      ? null
      : DateTime.parse(json['submittedAt'] as String),
  reviewedAt: json['reviewedAt'] == null
      ? null
      : DateTime.parse(json['reviewedAt'] as String),
);

Map<String, dynamic> _$$VerificationStatusImplToJson(
  _$VerificationStatusImpl instance,
) => <String, dynamic>{
  'status': instance.status,
  'documents': instance.documents,
  'isComplete': instance.isComplete,
  'message': instance.message,
  'submittedAt': instance.submittedAt?.toIso8601String(),
  'reviewedAt': instance.reviewedAt?.toIso8601String(),
};
