// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'driver_stats.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DriverStatsImpl _$$DriverStatsImplFromJson(Map<String, dynamic> json) =>
    _$DriverStatsImpl(
      totalRides: (json['totalRides'] as num).toInt(),
      averageRating: (json['averageRating'] as num).toDouble(),
      totalEarnings: (json['totalEarnings'] as num).toInt(),
      completedRides: (json['completedRides'] as num).toInt(),
      cancelledRides: (json['cancelledRides'] as num).toInt(),
      acceptanceRate: (json['acceptanceRate'] as num).toDouble(),
      completionRate: (json['completionRate'] as num).toDouble(),
      hoursOnline: (json['hoursOnline'] as num).toInt(),
      lastRideDate: json['lastRideDate'] == null
          ? null
          : DateTime.parse(json['lastRideDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$DriverStatsImplToJson(_$DriverStatsImpl instance) =>
    <String, dynamic>{
      'totalRides': instance.totalRides,
      'averageRating': instance.averageRating,
      'totalEarnings': instance.totalEarnings,
      'completedRides': instance.completedRides,
      'cancelledRides': instance.cancelledRides,
      'acceptanceRate': instance.acceptanceRate,
      'completionRate': instance.completionRate,
      'hoursOnline': instance.hoursOnline,
      'lastRideDate': instance.lastRideDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
