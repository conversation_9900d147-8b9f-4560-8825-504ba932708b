import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_registration.freezed.dart';
part 'user_registration.g.dart';

@freezed
class UserRegistration with _$UserRegistration {
  const factory UserRegistration({
    required String email,
    required String password,
    required String name,
    required String phone,
    @Default('driver') String userType,
  }) = _UserRegistration;

  factory UserRegistration.fromJson(Map<String, dynamic> json) =>
      _$UserRegistrationFromJson(json);
}
