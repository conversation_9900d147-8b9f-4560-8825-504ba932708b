// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vehicle_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

VehicleInfo _$VehicleInfoFromJson(Map<String, dynamic> json) {
  return _VehicleInfo.fromJson(json);
}

/// @nodoc
mixin _$VehicleInfo {
  String get make => throw _privateConstructorUsedError;
  String get model => throw _privateConstructorUsedError;
  int get year => throw _privateConstructorUsedError;
  String get color => throw _privateConstructorUsedError;
  String get licensePlate => throw _privateConstructorUsedError;
  String? get id => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VehicleInfo value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VehicleInfo value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VehicleInfo value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this VehicleInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VehicleInfoCopyWith<VehicleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleInfoCopyWith<$Res> {
  factory $VehicleInfoCopyWith(
    VehicleInfo value,
    $Res Function(VehicleInfo) then,
  ) = _$VehicleInfoCopyWithImpl<$Res, VehicleInfo>;
  @useResult
  $Res call({
    String make,
    String model,
    int year,
    String color,
    String licensePlate,
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$VehicleInfoCopyWithImpl<$Res, $Val extends VehicleInfo>
    implements $VehicleInfoCopyWith<$Res> {
  _$VehicleInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
    Object? id = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            make: null == make
                ? _value.make
                : make // ignore: cast_nullable_to_non_nullable
                      as String,
            model: null == model
                ? _value.model
                : model // ignore: cast_nullable_to_non_nullable
                      as String,
            year: null == year
                ? _value.year
                : year // ignore: cast_nullable_to_non_nullable
                      as int,
            color: null == color
                ? _value.color
                : color // ignore: cast_nullable_to_non_nullable
                      as String,
            licensePlate: null == licensePlate
                ? _value.licensePlate
                : licensePlate // ignore: cast_nullable_to_non_nullable
                      as String,
            id: freezed == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VehicleInfoImplCopyWith<$Res>
    implements $VehicleInfoCopyWith<$Res> {
  factory _$$VehicleInfoImplCopyWith(
    _$VehicleInfoImpl value,
    $Res Function(_$VehicleInfoImpl) then,
  ) = __$$VehicleInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String make,
    String model,
    int year,
    String color,
    String licensePlate,
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$VehicleInfoImplCopyWithImpl<$Res>
    extends _$VehicleInfoCopyWithImpl<$Res, _$VehicleInfoImpl>
    implements _$$VehicleInfoImplCopyWith<$Res> {
  __$$VehicleInfoImplCopyWithImpl(
    _$VehicleInfoImpl _value,
    $Res Function(_$VehicleInfoImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
    Object? id = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$VehicleInfoImpl(
        make: null == make
            ? _value.make
            : make // ignore: cast_nullable_to_non_nullable
                  as String,
        model: null == model
            ? _value.model
            : model // ignore: cast_nullable_to_non_nullable
                  as String,
        year: null == year
            ? _value.year
            : year // ignore: cast_nullable_to_non_nullable
                  as int,
        color: null == color
            ? _value.color
            : color // ignore: cast_nullable_to_non_nullable
                  as String,
        licensePlate: null == licensePlate
            ? _value.licensePlate
            : licensePlate // ignore: cast_nullable_to_non_nullable
                  as String,
        id: freezed == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleInfoImpl implements _VehicleInfo {
  const _$VehicleInfoImpl({
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    required this.licensePlate,
    this.id,
    this.createdAt,
    this.updatedAt,
  });

  factory _$VehicleInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleInfoImplFromJson(json);

  @override
  final String make;
  @override
  final String model;
  @override
  final int year;
  @override
  final String color;
  @override
  final String licensePlate;
  @override
  final String? id;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'VehicleInfo(make: $make, model: $model, year: $year, color: $color, licensePlate: $licensePlate, id: $id, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleInfoImpl &&
            (identical(other.make, make) || other.make == make) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.licensePlate, licensePlate) ||
                other.licensePlate == licensePlate) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    make,
    model,
    year,
    color,
    licensePlate,
    id,
    createdAt,
    updatedAt,
  );

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleInfoImplCopyWith<_$VehicleInfoImpl> get copyWith =>
      __$$VehicleInfoImplCopyWithImpl<_$VehicleInfoImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VehicleInfo value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VehicleInfo value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VehicleInfo value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleInfoImplToJson(this);
  }
}

abstract class _VehicleInfo implements VehicleInfo {
  const factory _VehicleInfo({
    required final String make,
    required final String model,
    required final int year,
    required final String color,
    required final String licensePlate,
    final String? id,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$VehicleInfoImpl;

  factory _VehicleInfo.fromJson(Map<String, dynamic> json) =
      _$VehicleInfoImpl.fromJson;

  @override
  String get make;
  @override
  String get model;
  @override
  int get year;
  @override
  String get color;
  @override
  String get licensePlate;
  @override
  String? get id;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VehicleInfoImplCopyWith<_$VehicleInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VehicleInfoCreate _$VehicleInfoCreateFromJson(Map<String, dynamic> json) {
  return _VehicleInfoCreate.fromJson(json);
}

/// @nodoc
mixin _$VehicleInfoCreate {
  String get make => throw _privateConstructorUsedError;
  String get model => throw _privateConstructorUsedError;
  int get year => throw _privateConstructorUsedError;
  String get color => throw _privateConstructorUsedError;
  String get licensePlate => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VehicleInfoCreate value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VehicleInfoCreate value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VehicleInfoCreate value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this VehicleInfoCreate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VehicleInfoCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VehicleInfoCreateCopyWith<VehicleInfoCreate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleInfoCreateCopyWith<$Res> {
  factory $VehicleInfoCreateCopyWith(
    VehicleInfoCreate value,
    $Res Function(VehicleInfoCreate) then,
  ) = _$VehicleInfoCreateCopyWithImpl<$Res, VehicleInfoCreate>;
  @useResult
  $Res call({
    String make,
    String model,
    int year,
    String color,
    String licensePlate,
  });
}

/// @nodoc
class _$VehicleInfoCreateCopyWithImpl<$Res, $Val extends VehicleInfoCreate>
    implements $VehicleInfoCreateCopyWith<$Res> {
  _$VehicleInfoCreateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VehicleInfoCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
  }) {
    return _then(
      _value.copyWith(
            make: null == make
                ? _value.make
                : make // ignore: cast_nullable_to_non_nullable
                      as String,
            model: null == model
                ? _value.model
                : model // ignore: cast_nullable_to_non_nullable
                      as String,
            year: null == year
                ? _value.year
                : year // ignore: cast_nullable_to_non_nullable
                      as int,
            color: null == color
                ? _value.color
                : color // ignore: cast_nullable_to_non_nullable
                      as String,
            licensePlate: null == licensePlate
                ? _value.licensePlate
                : licensePlate // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VehicleInfoCreateImplCopyWith<$Res>
    implements $VehicleInfoCreateCopyWith<$Res> {
  factory _$$VehicleInfoCreateImplCopyWith(
    _$VehicleInfoCreateImpl value,
    $Res Function(_$VehicleInfoCreateImpl) then,
  ) = __$$VehicleInfoCreateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String make,
    String model,
    int year,
    String color,
    String licensePlate,
  });
}

/// @nodoc
class __$$VehicleInfoCreateImplCopyWithImpl<$Res>
    extends _$VehicleInfoCreateCopyWithImpl<$Res, _$VehicleInfoCreateImpl>
    implements _$$VehicleInfoCreateImplCopyWith<$Res> {
  __$$VehicleInfoCreateImplCopyWithImpl(
    _$VehicleInfoCreateImpl _value,
    $Res Function(_$VehicleInfoCreateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VehicleInfoCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
  }) {
    return _then(
      _$VehicleInfoCreateImpl(
        make: null == make
            ? _value.make
            : make // ignore: cast_nullable_to_non_nullable
                  as String,
        model: null == model
            ? _value.model
            : model // ignore: cast_nullable_to_non_nullable
                  as String,
        year: null == year
            ? _value.year
            : year // ignore: cast_nullable_to_non_nullable
                  as int,
        color: null == color
            ? _value.color
            : color // ignore: cast_nullable_to_non_nullable
                  as String,
        licensePlate: null == licensePlate
            ? _value.licensePlate
            : licensePlate // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleInfoCreateImpl implements _VehicleInfoCreate {
  const _$VehicleInfoCreateImpl({
    required this.make,
    required this.model,
    required this.year,
    required this.color,
    required this.licensePlate,
  });

  factory _$VehicleInfoCreateImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleInfoCreateImplFromJson(json);

  @override
  final String make;
  @override
  final String model;
  @override
  final int year;
  @override
  final String color;
  @override
  final String licensePlate;

  @override
  String toString() {
    return 'VehicleInfoCreate(make: $make, model: $model, year: $year, color: $color, licensePlate: $licensePlate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleInfoCreateImpl &&
            (identical(other.make, make) || other.make == make) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.licensePlate, licensePlate) ||
                other.licensePlate == licensePlate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, make, model, year, color, licensePlate);

  /// Create a copy of VehicleInfoCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleInfoCreateImplCopyWith<_$VehicleInfoCreateImpl> get copyWith =>
      __$$VehicleInfoCreateImplCopyWithImpl<_$VehicleInfoCreateImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VehicleInfoCreate value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VehicleInfoCreate value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VehicleInfoCreate value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleInfoCreateImplToJson(this);
  }
}

abstract class _VehicleInfoCreate implements VehicleInfoCreate {
  const factory _VehicleInfoCreate({
    required final String make,
    required final String model,
    required final int year,
    required final String color,
    required final String licensePlate,
  }) = _$VehicleInfoCreateImpl;

  factory _VehicleInfoCreate.fromJson(Map<String, dynamic> json) =
      _$VehicleInfoCreateImpl.fromJson;

  @override
  String get make;
  @override
  String get model;
  @override
  int get year;
  @override
  String get color;
  @override
  String get licensePlate;

  /// Create a copy of VehicleInfoCreate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VehicleInfoCreateImplCopyWith<_$VehicleInfoCreateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VehicleInfoUpdate _$VehicleInfoUpdateFromJson(Map<String, dynamic> json) {
  return _VehicleInfoUpdate.fromJson(json);
}

/// @nodoc
mixin _$VehicleInfoUpdate {
  String? get make => throw _privateConstructorUsedError;
  String? get model => throw _privateConstructorUsedError;
  int? get year => throw _privateConstructorUsedError;
  String? get color => throw _privateConstructorUsedError;
  String? get licensePlate => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VehicleInfoUpdate value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VehicleInfoUpdate value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VehicleInfoUpdate value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this VehicleInfoUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VehicleInfoUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VehicleInfoUpdateCopyWith<VehicleInfoUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleInfoUpdateCopyWith<$Res> {
  factory $VehicleInfoUpdateCopyWith(
    VehicleInfoUpdate value,
    $Res Function(VehicleInfoUpdate) then,
  ) = _$VehicleInfoUpdateCopyWithImpl<$Res, VehicleInfoUpdate>;
  @useResult
  $Res call({
    String? make,
    String? model,
    int? year,
    String? color,
    String? licensePlate,
  });
}

/// @nodoc
class _$VehicleInfoUpdateCopyWithImpl<$Res, $Val extends VehicleInfoUpdate>
    implements $VehicleInfoUpdateCopyWith<$Res> {
  _$VehicleInfoUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VehicleInfoUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = freezed,
    Object? model = freezed,
    Object? year = freezed,
    Object? color = freezed,
    Object? licensePlate = freezed,
  }) {
    return _then(
      _value.copyWith(
            make: freezed == make
                ? _value.make
                : make // ignore: cast_nullable_to_non_nullable
                      as String?,
            model: freezed == model
                ? _value.model
                : model // ignore: cast_nullable_to_non_nullable
                      as String?,
            year: freezed == year
                ? _value.year
                : year // ignore: cast_nullable_to_non_nullable
                      as int?,
            color: freezed == color
                ? _value.color
                : color // ignore: cast_nullable_to_non_nullable
                      as String?,
            licensePlate: freezed == licensePlate
                ? _value.licensePlate
                : licensePlate // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VehicleInfoUpdateImplCopyWith<$Res>
    implements $VehicleInfoUpdateCopyWith<$Res> {
  factory _$$VehicleInfoUpdateImplCopyWith(
    _$VehicleInfoUpdateImpl value,
    $Res Function(_$VehicleInfoUpdateImpl) then,
  ) = __$$VehicleInfoUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? make,
    String? model,
    int? year,
    String? color,
    String? licensePlate,
  });
}

/// @nodoc
class __$$VehicleInfoUpdateImplCopyWithImpl<$Res>
    extends _$VehicleInfoUpdateCopyWithImpl<$Res, _$VehicleInfoUpdateImpl>
    implements _$$VehicleInfoUpdateImplCopyWith<$Res> {
  __$$VehicleInfoUpdateImplCopyWithImpl(
    _$VehicleInfoUpdateImpl _value,
    $Res Function(_$VehicleInfoUpdateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VehicleInfoUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = freezed,
    Object? model = freezed,
    Object? year = freezed,
    Object? color = freezed,
    Object? licensePlate = freezed,
  }) {
    return _then(
      _$VehicleInfoUpdateImpl(
        make: freezed == make
            ? _value.make
            : make // ignore: cast_nullable_to_non_nullable
                  as String?,
        model: freezed == model
            ? _value.model
            : model // ignore: cast_nullable_to_non_nullable
                  as String?,
        year: freezed == year
            ? _value.year
            : year // ignore: cast_nullable_to_non_nullable
                  as int?,
        color: freezed == color
            ? _value.color
            : color // ignore: cast_nullable_to_non_nullable
                  as String?,
        licensePlate: freezed == licensePlate
            ? _value.licensePlate
            : licensePlate // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleInfoUpdateImpl implements _VehicleInfoUpdate {
  const _$VehicleInfoUpdateImpl({
    this.make,
    this.model,
    this.year,
    this.color,
    this.licensePlate,
  });

  factory _$VehicleInfoUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleInfoUpdateImplFromJson(json);

  @override
  final String? make;
  @override
  final String? model;
  @override
  final int? year;
  @override
  final String? color;
  @override
  final String? licensePlate;

  @override
  String toString() {
    return 'VehicleInfoUpdate(make: $make, model: $model, year: $year, color: $color, licensePlate: $licensePlate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleInfoUpdateImpl &&
            (identical(other.make, make) || other.make == make) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.licensePlate, licensePlate) ||
                other.licensePlate == licensePlate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, make, model, year, color, licensePlate);

  /// Create a copy of VehicleInfoUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleInfoUpdateImplCopyWith<_$VehicleInfoUpdateImpl> get copyWith =>
      __$$VehicleInfoUpdateImplCopyWithImpl<_$VehicleInfoUpdateImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VehicleInfoUpdate value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VehicleInfoUpdate value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VehicleInfoUpdate value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleInfoUpdateImplToJson(this);
  }
}

abstract class _VehicleInfoUpdate implements VehicleInfoUpdate {
  const factory _VehicleInfoUpdate({
    final String? make,
    final String? model,
    final int? year,
    final String? color,
    final String? licensePlate,
  }) = _$VehicleInfoUpdateImpl;

  factory _VehicleInfoUpdate.fromJson(Map<String, dynamic> json) =
      _$VehicleInfoUpdateImpl.fromJson;

  @override
  String? get make;
  @override
  String? get model;
  @override
  int? get year;
  @override
  String? get color;
  @override
  String? get licensePlate;

  /// Create a copy of VehicleInfoUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VehicleInfoUpdateImplCopyWith<_$VehicleInfoUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
