import 'package:freezed_annotation/freezed_annotation.dart';

part 'location_update.freezed.dart';
part 'location_update.g.dart';

@freezed
class LocationUpdate with _$LocationUpdate {
  const factory LocationUpdate({
    required double latitude,
    required double longitude,
    double? heading,
    double? speed,
    double? accuracy,
    DateTime? timestamp,
  }) = _LocationUpdate;

  factory LocationUpdate.fromJson(Map<String, dynamic> json) =>
      _$LocationUpdateFromJson(json);
}
