import 'package:freezed_annotation/freezed_annotation.dart';

part 'vehicle_info.freezed.dart';
part 'vehicle_info.g.dart';

@freezed
class VehicleInfo with _$VehicleInfo {
  const factory VehicleInfo({
    required String make,
    required String model,
    required int year,
    required String color,
    required String licensePlate,
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _VehicleInfo;

  factory VehicleInfo.fromJson(Map<String, dynamic> json) =>
      _$VehicleInfoFromJson(json);
}

@freezed
class VehicleInfoCreate with _$VehicleInfoCreate {
  const factory VehicleInfoCreate({
    required String make,
    required String model,
    required int year,
    required String color,
    required String licensePlate,
  }) = _VehicleInfoCreate;

  factory VehicleInfoCreate.fromJson(Map<String, dynamic> json) =>
      _$VehicleInfoCreateFromJson(json);
}

@freezed
class VehicleInfoUpdate with _$VehicleInfoUpdate {
  const factory VehicleInfoUpdate({
    String? make,
    String? model,
    int? year,
    String? color,
    String? licensePlate,
  }) = _VehicleInfoUpdate;

  factory VehicleInfoUpdate.fromJson(Map<String, dynamic> json) =>
      _$VehicleInfoUpdateFromJson(json);
}
