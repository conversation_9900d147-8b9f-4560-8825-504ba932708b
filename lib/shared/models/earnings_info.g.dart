// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'earnings_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EarningsInfoImpl _$$EarningsInfoImplFromJson(Map<String, dynamic> json) =>
    _$EarningsInfoImpl(
      totalEarnings: (json['totalEarnings'] as num).toDouble(),
      weeklyEarnings: (json['weeklyEarnings'] as num).toDouble(),
      pendingPayouts: (json['pendingPayouts'] as num).toDouble(),
      todayEarnings: (json['todayEarnings'] as num).toDouble(),
      totalRides: (json['totalRides'] as num).toInt(),
      weeklyRides: (json['weeklyRides'] as num).toInt(),
      todayRides: (json['todayRides'] as num).toInt(),
      averageRating: (json['averageRating'] as num).toDouble(),
      lastPayoutDate: json['lastPayoutDate'] == null
          ? null
          : DateTime.parse(json['lastPayoutDate'] as String),
      currency: json['currency'] as String?,
    );

Map<String, dynamic> _$$EarningsInfoImplToJson(_$EarningsInfoImpl instance) =>
    <String, dynamic>{
      'totalEarnings': instance.totalEarnings,
      'weeklyEarnings': instance.weeklyEarnings,
      'pendingPayouts': instance.pendingPayouts,
      'todayEarnings': instance.todayEarnings,
      'totalRides': instance.totalRides,
      'weeklyRides': instance.weeklyRides,
      'todayRides': instance.todayRides,
      'averageRating': instance.averageRating,
      'lastPayoutDate': instance.lastPayoutDate?.toIso8601String(),
      'currency': instance.currency,
    };
