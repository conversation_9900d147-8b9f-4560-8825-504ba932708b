import 'package:freezed_annotation/freezed_annotation.dart';

part 'driver_stats.freezed.dart';
part 'driver_stats.g.dart';

@freezed
class DriverStats with _$DriverStats {
  const factory DriverStats({
    required int totalRides,
    required double averageRating,
    required int totalEarnings,
    required int completedRides,
    required int cancelledRides,
    required double acceptanceRate,
    required double completionRate,
    required int hoursOnline,
    DateTime? lastRideDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _DriverStats;

  factory DriverStats.fromJson(Map<String, dynamic> json) =>
      _$DriverStatsFromJson(json);
}
