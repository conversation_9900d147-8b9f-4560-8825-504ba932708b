import 'package:freezed_annotation/freezed_annotation.dart';
import 'vehicle_info.dart';
import 'location_info.dart';
import 'earnings_info.dart';

part 'driver_profile.freezed.dart';
part 'driver_profile.g.dart';

@freezed
class DriverProfile with _$DriverProfile {
  const factory DriverProfile({
    required String userId,
    required String licenseNumber,
    VehicleInfo? vehicleInfo,
    required bool isVerified,
    required bool isAvailable,
    required double rating,
    required int totalRides,
    LocationInfo? currentLocation,
    String? stripeAccountId,
    EarningsInfo? earnings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _DriverProfile;

  factory DriverProfile.fromJson(Map<String, dynamic> json) =>
      _$DriverProfileFromJson(json);
}

@freezed
class DriverProfileCreate with _$DriverProfileCreate {
  const factory DriverProfileCreate({
    required String licenseNumber,
    String? stripeAccountId,
  }) = _DriverProfileCreate;

  factory DriverProfileCreate.fromJson(Map<String, dynamic> json) =>
      _$DriverProfileCreateFromJson(json);
}

@freezed
class DriverProfileUpdate with _$DriverProfileUpdate {
  const factory DriverProfileUpdate({
    String? licenseNumber,
    String? stripeAccountId,
  }) = _DriverProfileUpdate;

  factory DriverProfileUpdate.fromJson(Map<String, dynamic> json) =>
      _$DriverProfileUpdateFromJson(json);
}

@freezed
class DriverAvailabilityUpdate with _$DriverAvailabilityUpdate {
  const factory DriverAvailabilityUpdate({required bool isAvailable}) =
      _DriverAvailabilityUpdate;

  factory DriverAvailabilityUpdate.fromJson(Map<String, dynamic> json) =>
      _$DriverAvailabilityUpdateFromJson(json);
}
