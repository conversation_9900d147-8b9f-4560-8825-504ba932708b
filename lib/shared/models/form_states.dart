import 'package:freezed_annotation/freezed_annotation.dart';

part 'form_states.freezed.dart';

/// Form state for driver profile setup and editing
@freezed
class DriverProfileFormState with _$DriverProfileFormState {
  const factory DriverProfileFormState({
    @Default('') String licenseNumber,
    @Default('') String stripeAccountId,
    @Default(false) bool isLoading,
    @Default(false) bool isValid,
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,
    @Default(false) bool hasBeenSubmitted,
  }) = _DriverProfileFormState;

  const DriverProfileFormState._();

  /// Check if the form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Check if the form is ready for submission
  bool get canSubmit => isValid && !isLoading && !hasErrors;

  /// Get error for a specific field
  String? getFieldError(String fieldName) => fieldErrors[fieldName];

  /// Check if a specific field has an error
  bool hasFieldError(String fieldName) => fieldErrors.containsKey(fieldName);
}

/// Form state for vehicle information setup and editing
@freezed
class VehicleFormState with _$VehicleFormState {
  const factory VehicleFormState({
    @Default('') String make,
    @Default('') String model,
    @Default('') String year,
    @Default('') String color,
    @Default('') String licensePlate,
    @Default(false) bool isLoading,
    @Default(false) bool isValid,
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,
    @Default(false) bool hasBeenSubmitted,
    @Default(false) bool isUpdateMode,
  }) = _VehicleFormState;

  const VehicleFormState._();

  /// Check if the form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Check if the form is ready for submission
  bool get canSubmit => isValid && !isLoading && !hasErrors;

  /// Get error for a specific field
  String? getFieldError(String fieldName) => fieldErrors[fieldName];

  /// Check if a specific field has an error
  bool hasFieldError(String fieldName) => fieldErrors.containsKey(fieldName);

  /// Get parsed year as integer
  int? get parsedYear {
    final yearInt = int.tryParse(year);
    return yearInt;
  }

  /// Check if all required fields are filled
  bool get allFieldsFilled =>
      make.isNotEmpty &&
      model.isNotEmpty &&
      year.isNotEmpty &&
      color.isNotEmpty &&
      licensePlate.isNotEmpty;
}

/// Generic form validation state
@freezed
class FormValidationState with _$FormValidationState {
  const factory FormValidationState({
    @Default(true) bool isValid,
    @Default({}) Map<String, String> fieldErrors,
    String? generalError,
  }) = _FormValidationState;

  const FormValidationState._();

  /// Check if the form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || generalError != null;

  /// Get error for a specific field
  String? getFieldError(String fieldName) => fieldErrors[fieldName];

  /// Check if a specific field has an error
  bool hasFieldError(String fieldName) => fieldErrors.containsKey(fieldName);
}
