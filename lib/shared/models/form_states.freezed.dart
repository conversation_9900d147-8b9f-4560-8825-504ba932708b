// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'form_states.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$DriverProfileFormState {
  String get licenseNumber => throw _privateConstructorUsedError;
  String get stripeAccountId => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isValid => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;
  bool get hasBeenSubmitted => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverProfileFormState value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverProfileFormState value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverProfileFormState value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Create a copy of DriverProfileFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverProfileFormStateCopyWith<DriverProfileFormState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileFormStateCopyWith<$Res> {
  factory $DriverProfileFormStateCopyWith(
    DriverProfileFormState value,
    $Res Function(DriverProfileFormState) then,
  ) = _$DriverProfileFormStateCopyWithImpl<$Res, DriverProfileFormState>;
  @useResult
  $Res call({
    String licenseNumber,
    String stripeAccountId,
    bool isLoading,
    bool isValid,
    String? errorMessage,
    Map<String, String> fieldErrors,
    bool hasBeenSubmitted,
  });
}

/// @nodoc
class _$DriverProfileFormStateCopyWithImpl<
  $Res,
  $Val extends DriverProfileFormState
>
    implements $DriverProfileFormStateCopyWith<$Res> {
  _$DriverProfileFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfileFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licenseNumber = null,
    Object? stripeAccountId = null,
    Object? isLoading = null,
    Object? isValid = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? hasBeenSubmitted = null,
  }) {
    return _then(
      _value.copyWith(
            licenseNumber: null == licenseNumber
                ? _value.licenseNumber
                : licenseNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            stripeAccountId: null == stripeAccountId
                ? _value.stripeAccountId
                : stripeAccountId // ignore: cast_nullable_to_non_nullable
                      as String,
            isLoading: null == isLoading
                ? _value.isLoading
                : isLoading // ignore: cast_nullable_to_non_nullable
                      as bool,
            isValid: null == isValid
                ? _value.isValid
                : isValid // ignore: cast_nullable_to_non_nullable
                      as bool,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            fieldErrors: null == fieldErrors
                ? _value.fieldErrors
                : fieldErrors // ignore: cast_nullable_to_non_nullable
                      as Map<String, String>,
            hasBeenSubmitted: null == hasBeenSubmitted
                ? _value.hasBeenSubmitted
                : hasBeenSubmitted // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DriverProfileFormStateImplCopyWith<$Res>
    implements $DriverProfileFormStateCopyWith<$Res> {
  factory _$$DriverProfileFormStateImplCopyWith(
    _$DriverProfileFormStateImpl value,
    $Res Function(_$DriverProfileFormStateImpl) then,
  ) = __$$DriverProfileFormStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String licenseNumber,
    String stripeAccountId,
    bool isLoading,
    bool isValid,
    String? errorMessage,
    Map<String, String> fieldErrors,
    bool hasBeenSubmitted,
  });
}

/// @nodoc
class __$$DriverProfileFormStateImplCopyWithImpl<$Res>
    extends
        _$DriverProfileFormStateCopyWithImpl<$Res, _$DriverProfileFormStateImpl>
    implements _$$DriverProfileFormStateImplCopyWith<$Res> {
  __$$DriverProfileFormStateImplCopyWithImpl(
    _$DriverProfileFormStateImpl _value,
    $Res Function(_$DriverProfileFormStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverProfileFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licenseNumber = null,
    Object? stripeAccountId = null,
    Object? isLoading = null,
    Object? isValid = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? hasBeenSubmitted = null,
  }) {
    return _then(
      _$DriverProfileFormStateImpl(
        licenseNumber: null == licenseNumber
            ? _value.licenseNumber
            : licenseNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        stripeAccountId: null == stripeAccountId
            ? _value.stripeAccountId
            : stripeAccountId // ignore: cast_nullable_to_non_nullable
                  as String,
        isLoading: null == isLoading
            ? _value.isLoading
            : isLoading // ignore: cast_nullable_to_non_nullable
                  as bool,
        isValid: null == isValid
            ? _value.isValid
            : isValid // ignore: cast_nullable_to_non_nullable
                  as bool,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        fieldErrors: null == fieldErrors
            ? _value._fieldErrors
            : fieldErrors // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>,
        hasBeenSubmitted: null == hasBeenSubmitted
            ? _value.hasBeenSubmitted
            : hasBeenSubmitted // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc

class _$DriverProfileFormStateImpl extends _DriverProfileFormState {
  const _$DriverProfileFormStateImpl({
    this.licenseNumber = '',
    this.stripeAccountId = '',
    this.isLoading = false,
    this.isValid = false,
    this.errorMessage,
    final Map<String, String> fieldErrors = const {},
    this.hasBeenSubmitted = false,
  }) : _fieldErrors = fieldErrors,
       super._();

  @override
  @JsonKey()
  final String licenseNumber;
  @override
  @JsonKey()
  final String stripeAccountId;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isValid;
  @override
  final String? errorMessage;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  @override
  @JsonKey()
  final bool hasBeenSubmitted;

  @override
  String toString() {
    return 'DriverProfileFormState(licenseNumber: $licenseNumber, stripeAccountId: $stripeAccountId, isLoading: $isLoading, isValid: $isValid, errorMessage: $errorMessage, fieldErrors: $fieldErrors, hasBeenSubmitted: $hasBeenSubmitted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileFormStateImpl &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.stripeAccountId, stripeAccountId) ||
                other.stripeAccountId == stripeAccountId) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(
              other._fieldErrors,
              _fieldErrors,
            ) &&
            (identical(other.hasBeenSubmitted, hasBeenSubmitted) ||
                other.hasBeenSubmitted == hasBeenSubmitted));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    licenseNumber,
    stripeAccountId,
    isLoading,
    isValid,
    errorMessage,
    const DeepCollectionEquality().hash(_fieldErrors),
    hasBeenSubmitted,
  );

  /// Create a copy of DriverProfileFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileFormStateImplCopyWith<_$DriverProfileFormStateImpl>
  get copyWith =>
      __$$DriverProfileFormStateImplCopyWithImpl<_$DriverProfileFormStateImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverProfileFormState value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverProfileFormState value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverProfileFormState value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _DriverProfileFormState extends DriverProfileFormState {
  const factory _DriverProfileFormState({
    final String licenseNumber,
    final String stripeAccountId,
    final bool isLoading,
    final bool isValid,
    final String? errorMessage,
    final Map<String, String> fieldErrors,
    final bool hasBeenSubmitted,
  }) = _$DriverProfileFormStateImpl;
  const _DriverProfileFormState._() : super._();

  @override
  String get licenseNumber;
  @override
  String get stripeAccountId;
  @override
  bool get isLoading;
  @override
  bool get isValid;
  @override
  String? get errorMessage;
  @override
  Map<String, String> get fieldErrors;
  @override
  bool get hasBeenSubmitted;

  /// Create a copy of DriverProfileFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileFormStateImplCopyWith<_$DriverProfileFormStateImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$VehicleFormState {
  String get make => throw _privateConstructorUsedError;
  String get model => throw _privateConstructorUsedError;
  String get year => throw _privateConstructorUsedError;
  String get color => throw _privateConstructorUsedError;
  String get licensePlate => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isValid => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;
  bool get hasBeenSubmitted => throw _privateConstructorUsedError;
  bool get isUpdateMode => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VehicleFormState value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VehicleFormState value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VehicleFormState value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Create a copy of VehicleFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VehicleFormStateCopyWith<VehicleFormState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleFormStateCopyWith<$Res> {
  factory $VehicleFormStateCopyWith(
    VehicleFormState value,
    $Res Function(VehicleFormState) then,
  ) = _$VehicleFormStateCopyWithImpl<$Res, VehicleFormState>;
  @useResult
  $Res call({
    String make,
    String model,
    String year,
    String color,
    String licensePlate,
    bool isLoading,
    bool isValid,
    String? errorMessage,
    Map<String, String> fieldErrors,
    bool hasBeenSubmitted,
    bool isUpdateMode,
  });
}

/// @nodoc
class _$VehicleFormStateCopyWithImpl<$Res, $Val extends VehicleFormState>
    implements $VehicleFormStateCopyWith<$Res> {
  _$VehicleFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VehicleFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
    Object? isLoading = null,
    Object? isValid = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? hasBeenSubmitted = null,
    Object? isUpdateMode = null,
  }) {
    return _then(
      _value.copyWith(
            make: null == make
                ? _value.make
                : make // ignore: cast_nullable_to_non_nullable
                      as String,
            model: null == model
                ? _value.model
                : model // ignore: cast_nullable_to_non_nullable
                      as String,
            year: null == year
                ? _value.year
                : year // ignore: cast_nullable_to_non_nullable
                      as String,
            color: null == color
                ? _value.color
                : color // ignore: cast_nullable_to_non_nullable
                      as String,
            licensePlate: null == licensePlate
                ? _value.licensePlate
                : licensePlate // ignore: cast_nullable_to_non_nullable
                      as String,
            isLoading: null == isLoading
                ? _value.isLoading
                : isLoading // ignore: cast_nullable_to_non_nullable
                      as bool,
            isValid: null == isValid
                ? _value.isValid
                : isValid // ignore: cast_nullable_to_non_nullable
                      as bool,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            fieldErrors: null == fieldErrors
                ? _value.fieldErrors
                : fieldErrors // ignore: cast_nullable_to_non_nullable
                      as Map<String, String>,
            hasBeenSubmitted: null == hasBeenSubmitted
                ? _value.hasBeenSubmitted
                : hasBeenSubmitted // ignore: cast_nullable_to_non_nullable
                      as bool,
            isUpdateMode: null == isUpdateMode
                ? _value.isUpdateMode
                : isUpdateMode // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$VehicleFormStateImplCopyWith<$Res>
    implements $VehicleFormStateCopyWith<$Res> {
  factory _$$VehicleFormStateImplCopyWith(
    _$VehicleFormStateImpl value,
    $Res Function(_$VehicleFormStateImpl) then,
  ) = __$$VehicleFormStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String make,
    String model,
    String year,
    String color,
    String licensePlate,
    bool isLoading,
    bool isValid,
    String? errorMessage,
    Map<String, String> fieldErrors,
    bool hasBeenSubmitted,
    bool isUpdateMode,
  });
}

/// @nodoc
class __$$VehicleFormStateImplCopyWithImpl<$Res>
    extends _$VehicleFormStateCopyWithImpl<$Res, _$VehicleFormStateImpl>
    implements _$$VehicleFormStateImplCopyWith<$Res> {
  __$$VehicleFormStateImplCopyWithImpl(
    _$VehicleFormStateImpl _value,
    $Res Function(_$VehicleFormStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VehicleFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
    Object? isLoading = null,
    Object? isValid = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? hasBeenSubmitted = null,
    Object? isUpdateMode = null,
  }) {
    return _then(
      _$VehicleFormStateImpl(
        make: null == make
            ? _value.make
            : make // ignore: cast_nullable_to_non_nullable
                  as String,
        model: null == model
            ? _value.model
            : model // ignore: cast_nullable_to_non_nullable
                  as String,
        year: null == year
            ? _value.year
            : year // ignore: cast_nullable_to_non_nullable
                  as String,
        color: null == color
            ? _value.color
            : color // ignore: cast_nullable_to_non_nullable
                  as String,
        licensePlate: null == licensePlate
            ? _value.licensePlate
            : licensePlate // ignore: cast_nullable_to_non_nullable
                  as String,
        isLoading: null == isLoading
            ? _value.isLoading
            : isLoading // ignore: cast_nullable_to_non_nullable
                  as bool,
        isValid: null == isValid
            ? _value.isValid
            : isValid // ignore: cast_nullable_to_non_nullable
                  as bool,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        fieldErrors: null == fieldErrors
            ? _value._fieldErrors
            : fieldErrors // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>,
        hasBeenSubmitted: null == hasBeenSubmitted
            ? _value.hasBeenSubmitted
            : hasBeenSubmitted // ignore: cast_nullable_to_non_nullable
                  as bool,
        isUpdateMode: null == isUpdateMode
            ? _value.isUpdateMode
            : isUpdateMode // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc

class _$VehicleFormStateImpl extends _VehicleFormState {
  const _$VehicleFormStateImpl({
    this.make = '',
    this.model = '',
    this.year = '',
    this.color = '',
    this.licensePlate = '',
    this.isLoading = false,
    this.isValid = false,
    this.errorMessage,
    final Map<String, String> fieldErrors = const {},
    this.hasBeenSubmitted = false,
    this.isUpdateMode = false,
  }) : _fieldErrors = fieldErrors,
       super._();

  @override
  @JsonKey()
  final String make;
  @override
  @JsonKey()
  final String model;
  @override
  @JsonKey()
  final String year;
  @override
  @JsonKey()
  final String color;
  @override
  @JsonKey()
  final String licensePlate;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isValid;
  @override
  final String? errorMessage;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  @override
  @JsonKey()
  final bool hasBeenSubmitted;
  @override
  @JsonKey()
  final bool isUpdateMode;

  @override
  String toString() {
    return 'VehicleFormState(make: $make, model: $model, year: $year, color: $color, licensePlate: $licensePlate, isLoading: $isLoading, isValid: $isValid, errorMessage: $errorMessage, fieldErrors: $fieldErrors, hasBeenSubmitted: $hasBeenSubmitted, isUpdateMode: $isUpdateMode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleFormStateImpl &&
            (identical(other.make, make) || other.make == make) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.licensePlate, licensePlate) ||
                other.licensePlate == licensePlate) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(
              other._fieldErrors,
              _fieldErrors,
            ) &&
            (identical(other.hasBeenSubmitted, hasBeenSubmitted) ||
                other.hasBeenSubmitted == hasBeenSubmitted) &&
            (identical(other.isUpdateMode, isUpdateMode) ||
                other.isUpdateMode == isUpdateMode));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    make,
    model,
    year,
    color,
    licensePlate,
    isLoading,
    isValid,
    errorMessage,
    const DeepCollectionEquality().hash(_fieldErrors),
    hasBeenSubmitted,
    isUpdateMode,
  );

  /// Create a copy of VehicleFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleFormStateImplCopyWith<_$VehicleFormStateImpl> get copyWith =>
      __$$VehicleFormStateImplCopyWithImpl<_$VehicleFormStateImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VehicleFormState value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VehicleFormState value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VehicleFormState value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _VehicleFormState extends VehicleFormState {
  const factory _VehicleFormState({
    final String make,
    final String model,
    final String year,
    final String color,
    final String licensePlate,
    final bool isLoading,
    final bool isValid,
    final String? errorMessage,
    final Map<String, String> fieldErrors,
    final bool hasBeenSubmitted,
    final bool isUpdateMode,
  }) = _$VehicleFormStateImpl;
  const _VehicleFormState._() : super._();

  @override
  String get make;
  @override
  String get model;
  @override
  String get year;
  @override
  String get color;
  @override
  String get licensePlate;
  @override
  bool get isLoading;
  @override
  bool get isValid;
  @override
  String? get errorMessage;
  @override
  Map<String, String> get fieldErrors;
  @override
  bool get hasBeenSubmitted;
  @override
  bool get isUpdateMode;

  /// Create a copy of VehicleFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VehicleFormStateImplCopyWith<_$VehicleFormStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$FormValidationState {
  bool get isValid => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;
  String? get generalError => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_FormValidationState value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_FormValidationState value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_FormValidationState value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Create a copy of FormValidationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FormValidationStateCopyWith<FormValidationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FormValidationStateCopyWith<$Res> {
  factory $FormValidationStateCopyWith(
    FormValidationState value,
    $Res Function(FormValidationState) then,
  ) = _$FormValidationStateCopyWithImpl<$Res, FormValidationState>;
  @useResult
  $Res call({
    bool isValid,
    Map<String, String> fieldErrors,
    String? generalError,
  });
}

/// @nodoc
class _$FormValidationStateCopyWithImpl<$Res, $Val extends FormValidationState>
    implements $FormValidationStateCopyWith<$Res> {
  _$FormValidationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FormValidationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isValid = null,
    Object? fieldErrors = null,
    Object? generalError = freezed,
  }) {
    return _then(
      _value.copyWith(
            isValid: null == isValid
                ? _value.isValid
                : isValid // ignore: cast_nullable_to_non_nullable
                      as bool,
            fieldErrors: null == fieldErrors
                ? _value.fieldErrors
                : fieldErrors // ignore: cast_nullable_to_non_nullable
                      as Map<String, String>,
            generalError: freezed == generalError
                ? _value.generalError
                : generalError // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FormValidationStateImplCopyWith<$Res>
    implements $FormValidationStateCopyWith<$Res> {
  factory _$$FormValidationStateImplCopyWith(
    _$FormValidationStateImpl value,
    $Res Function(_$FormValidationStateImpl) then,
  ) = __$$FormValidationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    bool isValid,
    Map<String, String> fieldErrors,
    String? generalError,
  });
}

/// @nodoc
class __$$FormValidationStateImplCopyWithImpl<$Res>
    extends _$FormValidationStateCopyWithImpl<$Res, _$FormValidationStateImpl>
    implements _$$FormValidationStateImplCopyWith<$Res> {
  __$$FormValidationStateImplCopyWithImpl(
    _$FormValidationStateImpl _value,
    $Res Function(_$FormValidationStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FormValidationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isValid = null,
    Object? fieldErrors = null,
    Object? generalError = freezed,
  }) {
    return _then(
      _$FormValidationStateImpl(
        isValid: null == isValid
            ? _value.isValid
            : isValid // ignore: cast_nullable_to_non_nullable
                  as bool,
        fieldErrors: null == fieldErrors
            ? _value._fieldErrors
            : fieldErrors // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>,
        generalError: freezed == generalError
            ? _value.generalError
            : generalError // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$FormValidationStateImpl extends _FormValidationState {
  const _$FormValidationStateImpl({
    this.isValid = true,
    final Map<String, String> fieldErrors = const {},
    this.generalError,
  }) : _fieldErrors = fieldErrors,
       super._();

  @override
  @JsonKey()
  final bool isValid;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  @override
  final String? generalError;

  @override
  String toString() {
    return 'FormValidationState(isValid: $isValid, fieldErrors: $fieldErrors, generalError: $generalError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FormValidationStateImpl &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            const DeepCollectionEquality().equals(
              other._fieldErrors,
              _fieldErrors,
            ) &&
            (identical(other.generalError, generalError) ||
                other.generalError == generalError));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    isValid,
    const DeepCollectionEquality().hash(_fieldErrors),
    generalError,
  );

  /// Create a copy of FormValidationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FormValidationStateImplCopyWith<_$FormValidationStateImpl> get copyWith =>
      __$$FormValidationStateImplCopyWithImpl<_$FormValidationStateImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_FormValidationState value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_FormValidationState value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_FormValidationState value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _FormValidationState extends FormValidationState {
  const factory _FormValidationState({
    final bool isValid,
    final Map<String, String> fieldErrors,
    final String? generalError,
  }) = _$FormValidationStateImpl;
  const _FormValidationState._() : super._();

  @override
  bool get isValid;
  @override
  Map<String, String> get fieldErrors;
  @override
  String? get generalError;

  /// Create a copy of FormValidationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FormValidationStateImplCopyWith<_$FormValidationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
