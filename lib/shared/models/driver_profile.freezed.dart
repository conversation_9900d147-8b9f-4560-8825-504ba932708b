// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

DriverProfile _$DriverProfileFromJson(Map<String, dynamic> json) {
  return _DriverProfile.fromJson(json);
}

/// @nodoc
mixin _$DriverProfile {
  String get userId => throw _privateConstructorUsedError;
  String get licenseNumber => throw _privateConstructorUsedError;
  VehicleInfo? get vehicleInfo => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isAvailable => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  int get totalRides => throw _privateConstructorUsedError;
  LocationInfo? get currentLocation => throw _privateConstructorUsedError;
  String? get stripeAccountId => throw _privateConstructorUsedError;
  EarningsInfo? get earnings => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverProfile value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverProfile value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverProfile value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this DriverProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverProfileCopyWith<DriverProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileCopyWith<$Res> {
  factory $DriverProfileCopyWith(
    DriverProfile value,
    $Res Function(DriverProfile) then,
  ) = _$DriverProfileCopyWithImpl<$Res, DriverProfile>;
  @useResult
  $Res call({
    String userId,
    String licenseNumber,
    VehicleInfo? vehicleInfo,
    bool isVerified,
    bool isAvailable,
    double rating,
    int totalRides,
    LocationInfo? currentLocation,
    String? stripeAccountId,
    EarningsInfo? earnings,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  $VehicleInfoCopyWith<$Res>? get vehicleInfo;
  $LocationInfoCopyWith<$Res>? get currentLocation;
  $EarningsInfoCopyWith<$Res>? get earnings;
}

/// @nodoc
class _$DriverProfileCopyWithImpl<$Res, $Val extends DriverProfile>
    implements $DriverProfileCopyWith<$Res> {
  _$DriverProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? licenseNumber = null,
    Object? vehicleInfo = freezed,
    Object? isVerified = null,
    Object? isAvailable = null,
    Object? rating = null,
    Object? totalRides = null,
    Object? currentLocation = freezed,
    Object? stripeAccountId = freezed,
    Object? earnings = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            licenseNumber: null == licenseNumber
                ? _value.licenseNumber
                : licenseNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            vehicleInfo: freezed == vehicleInfo
                ? _value.vehicleInfo
                : vehicleInfo // ignore: cast_nullable_to_non_nullable
                      as VehicleInfo?,
            isVerified: null == isVerified
                ? _value.isVerified
                : isVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            isAvailable: null == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool,
            rating: null == rating
                ? _value.rating
                : rating // ignore: cast_nullable_to_non_nullable
                      as double,
            totalRides: null == totalRides
                ? _value.totalRides
                : totalRides // ignore: cast_nullable_to_non_nullable
                      as int,
            currentLocation: freezed == currentLocation
                ? _value.currentLocation
                : currentLocation // ignore: cast_nullable_to_non_nullable
                      as LocationInfo?,
            stripeAccountId: freezed == stripeAccountId
                ? _value.stripeAccountId
                : stripeAccountId // ignore: cast_nullable_to_non_nullable
                      as String?,
            earnings: freezed == earnings
                ? _value.earnings
                : earnings // ignore: cast_nullable_to_non_nullable
                      as EarningsInfo?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VehicleInfoCopyWith<$Res>? get vehicleInfo {
    if (_value.vehicleInfo == null) {
      return null;
    }

    return $VehicleInfoCopyWith<$Res>(_value.vehicleInfo!, (value) {
      return _then(_value.copyWith(vehicleInfo: value) as $Val);
    });
  }

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationInfoCopyWith<$Res>? get currentLocation {
    if (_value.currentLocation == null) {
      return null;
    }

    return $LocationInfoCopyWith<$Res>(_value.currentLocation!, (value) {
      return _then(_value.copyWith(currentLocation: value) as $Val);
    });
  }

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EarningsInfoCopyWith<$Res>? get earnings {
    if (_value.earnings == null) {
      return null;
    }

    return $EarningsInfoCopyWith<$Res>(_value.earnings!, (value) {
      return _then(_value.copyWith(earnings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DriverProfileImplCopyWith<$Res>
    implements $DriverProfileCopyWith<$Res> {
  factory _$$DriverProfileImplCopyWith(
    _$DriverProfileImpl value,
    $Res Function(_$DriverProfileImpl) then,
  ) = __$$DriverProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String userId,
    String licenseNumber,
    VehicleInfo? vehicleInfo,
    bool isVerified,
    bool isAvailable,
    double rating,
    int totalRides,
    LocationInfo? currentLocation,
    String? stripeAccountId,
    EarningsInfo? earnings,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  @override
  $VehicleInfoCopyWith<$Res>? get vehicleInfo;
  @override
  $LocationInfoCopyWith<$Res>? get currentLocation;
  @override
  $EarningsInfoCopyWith<$Res>? get earnings;
}

/// @nodoc
class __$$DriverProfileImplCopyWithImpl<$Res>
    extends _$DriverProfileCopyWithImpl<$Res, _$DriverProfileImpl>
    implements _$$DriverProfileImplCopyWith<$Res> {
  __$$DriverProfileImplCopyWithImpl(
    _$DriverProfileImpl _value,
    $Res Function(_$DriverProfileImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? licenseNumber = null,
    Object? vehicleInfo = freezed,
    Object? isVerified = null,
    Object? isAvailable = null,
    Object? rating = null,
    Object? totalRides = null,
    Object? currentLocation = freezed,
    Object? stripeAccountId = freezed,
    Object? earnings = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$DriverProfileImpl(
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        licenseNumber: null == licenseNumber
            ? _value.licenseNumber
            : licenseNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        vehicleInfo: freezed == vehicleInfo
            ? _value.vehicleInfo
            : vehicleInfo // ignore: cast_nullable_to_non_nullable
                  as VehicleInfo?,
        isVerified: null == isVerified
            ? _value.isVerified
            : isVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        isAvailable: null == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool,
        rating: null == rating
            ? _value.rating
            : rating // ignore: cast_nullable_to_non_nullable
                  as double,
        totalRides: null == totalRides
            ? _value.totalRides
            : totalRides // ignore: cast_nullable_to_non_nullable
                  as int,
        currentLocation: freezed == currentLocation
            ? _value.currentLocation
            : currentLocation // ignore: cast_nullable_to_non_nullable
                  as LocationInfo?,
        stripeAccountId: freezed == stripeAccountId
            ? _value.stripeAccountId
            : stripeAccountId // ignore: cast_nullable_to_non_nullable
                  as String?,
        earnings: freezed == earnings
            ? _value.earnings
            : earnings // ignore: cast_nullable_to_non_nullable
                  as EarningsInfo?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverProfileImpl implements _DriverProfile {
  const _$DriverProfileImpl({
    required this.userId,
    required this.licenseNumber,
    this.vehicleInfo,
    required this.isVerified,
    required this.isAvailable,
    required this.rating,
    required this.totalRides,
    this.currentLocation,
    this.stripeAccountId,
    this.earnings,
    this.createdAt,
    this.updatedAt,
  });

  factory _$DriverProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverProfileImplFromJson(json);

  @override
  final String userId;
  @override
  final String licenseNumber;
  @override
  final VehicleInfo? vehicleInfo;
  @override
  final bool isVerified;
  @override
  final bool isAvailable;
  @override
  final double rating;
  @override
  final int totalRides;
  @override
  final LocationInfo? currentLocation;
  @override
  final String? stripeAccountId;
  @override
  final EarningsInfo? earnings;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'DriverProfile(userId: $userId, licenseNumber: $licenseNumber, vehicleInfo: $vehicleInfo, isVerified: $isVerified, isAvailable: $isAvailable, rating: $rating, totalRides: $totalRides, currentLocation: $currentLocation, stripeAccountId: $stripeAccountId, earnings: $earnings, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.vehicleInfo, vehicleInfo) ||
                other.vehicleInfo == vehicleInfo) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalRides, totalRides) ||
                other.totalRides == totalRides) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.stripeAccountId, stripeAccountId) ||
                other.stripeAccountId == stripeAccountId) &&
            (identical(other.earnings, earnings) ||
                other.earnings == earnings) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    licenseNumber,
    vehicleInfo,
    isVerified,
    isAvailable,
    rating,
    totalRides,
    currentLocation,
    stripeAccountId,
    earnings,
    createdAt,
    updatedAt,
  );

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileImplCopyWith<_$DriverProfileImpl> get copyWith =>
      __$$DriverProfileImplCopyWithImpl<_$DriverProfileImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverProfile value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverProfile value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverProfile value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverProfileImplToJson(this);
  }
}

abstract class _DriverProfile implements DriverProfile {
  const factory _DriverProfile({
    required final String userId,
    required final String licenseNumber,
    final VehicleInfo? vehicleInfo,
    required final bool isVerified,
    required final bool isAvailable,
    required final double rating,
    required final int totalRides,
    final LocationInfo? currentLocation,
    final String? stripeAccountId,
    final EarningsInfo? earnings,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$DriverProfileImpl;

  factory _DriverProfile.fromJson(Map<String, dynamic> json) =
      _$DriverProfileImpl.fromJson;

  @override
  String get userId;
  @override
  String get licenseNumber;
  @override
  VehicleInfo? get vehicleInfo;
  @override
  bool get isVerified;
  @override
  bool get isAvailable;
  @override
  double get rating;
  @override
  int get totalRides;
  @override
  LocationInfo? get currentLocation;
  @override
  String? get stripeAccountId;
  @override
  EarningsInfo? get earnings;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileImplCopyWith<_$DriverProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DriverProfileCreate _$DriverProfileCreateFromJson(Map<String, dynamic> json) {
  return _DriverProfileCreate.fromJson(json);
}

/// @nodoc
mixin _$DriverProfileCreate {
  String get licenseNumber => throw _privateConstructorUsedError;
  String? get stripeAccountId => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverProfileCreate value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverProfileCreate value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverProfileCreate value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this DriverProfileCreate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverProfileCreateCopyWith<DriverProfileCreate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileCreateCopyWith<$Res> {
  factory $DriverProfileCreateCopyWith(
    DriverProfileCreate value,
    $Res Function(DriverProfileCreate) then,
  ) = _$DriverProfileCreateCopyWithImpl<$Res, DriverProfileCreate>;
  @useResult
  $Res call({String licenseNumber, String? stripeAccountId});
}

/// @nodoc
class _$DriverProfileCreateCopyWithImpl<$Res, $Val extends DriverProfileCreate>
    implements $DriverProfileCreateCopyWith<$Res> {
  _$DriverProfileCreateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? licenseNumber = null, Object? stripeAccountId = freezed}) {
    return _then(
      _value.copyWith(
            licenseNumber: null == licenseNumber
                ? _value.licenseNumber
                : licenseNumber // ignore: cast_nullable_to_non_nullable
                      as String,
            stripeAccountId: freezed == stripeAccountId
                ? _value.stripeAccountId
                : stripeAccountId // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DriverProfileCreateImplCopyWith<$Res>
    implements $DriverProfileCreateCopyWith<$Res> {
  factory _$$DriverProfileCreateImplCopyWith(
    _$DriverProfileCreateImpl value,
    $Res Function(_$DriverProfileCreateImpl) then,
  ) = __$$DriverProfileCreateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String licenseNumber, String? stripeAccountId});
}

/// @nodoc
class __$$DriverProfileCreateImplCopyWithImpl<$Res>
    extends _$DriverProfileCreateCopyWithImpl<$Res, _$DriverProfileCreateImpl>
    implements _$$DriverProfileCreateImplCopyWith<$Res> {
  __$$DriverProfileCreateImplCopyWithImpl(
    _$DriverProfileCreateImpl _value,
    $Res Function(_$DriverProfileCreateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? licenseNumber = null, Object? stripeAccountId = freezed}) {
    return _then(
      _$DriverProfileCreateImpl(
        licenseNumber: null == licenseNumber
            ? _value.licenseNumber
            : licenseNumber // ignore: cast_nullable_to_non_nullable
                  as String,
        stripeAccountId: freezed == stripeAccountId
            ? _value.stripeAccountId
            : stripeAccountId // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverProfileCreateImpl implements _DriverProfileCreate {
  const _$DriverProfileCreateImpl({
    required this.licenseNumber,
    this.stripeAccountId,
  });

  factory _$DriverProfileCreateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverProfileCreateImplFromJson(json);

  @override
  final String licenseNumber;
  @override
  final String? stripeAccountId;

  @override
  String toString() {
    return 'DriverProfileCreate(licenseNumber: $licenseNumber, stripeAccountId: $stripeAccountId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileCreateImpl &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.stripeAccountId, stripeAccountId) ||
                other.stripeAccountId == stripeAccountId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, licenseNumber, stripeAccountId);

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileCreateImplCopyWith<_$DriverProfileCreateImpl> get copyWith =>
      __$$DriverProfileCreateImplCopyWithImpl<_$DriverProfileCreateImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverProfileCreate value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverProfileCreate value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverProfileCreate value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverProfileCreateImplToJson(this);
  }
}

abstract class _DriverProfileCreate implements DriverProfileCreate {
  const factory _DriverProfileCreate({
    required final String licenseNumber,
    final String? stripeAccountId,
  }) = _$DriverProfileCreateImpl;

  factory _DriverProfileCreate.fromJson(Map<String, dynamic> json) =
      _$DriverProfileCreateImpl.fromJson;

  @override
  String get licenseNumber;
  @override
  String? get stripeAccountId;

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileCreateImplCopyWith<_$DriverProfileCreateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DriverProfileUpdate _$DriverProfileUpdateFromJson(Map<String, dynamic> json) {
  return _DriverProfileUpdate.fromJson(json);
}

/// @nodoc
mixin _$DriverProfileUpdate {
  String? get licenseNumber => throw _privateConstructorUsedError;
  String? get stripeAccountId => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverProfileUpdate value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverProfileUpdate value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverProfileUpdate value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this DriverProfileUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverProfileUpdateCopyWith<DriverProfileUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileUpdateCopyWith<$Res> {
  factory $DriverProfileUpdateCopyWith(
    DriverProfileUpdate value,
    $Res Function(DriverProfileUpdate) then,
  ) = _$DriverProfileUpdateCopyWithImpl<$Res, DriverProfileUpdate>;
  @useResult
  $Res call({String? licenseNumber, String? stripeAccountId});
}

/// @nodoc
class _$DriverProfileUpdateCopyWithImpl<$Res, $Val extends DriverProfileUpdate>
    implements $DriverProfileUpdateCopyWith<$Res> {
  _$DriverProfileUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licenseNumber = freezed,
    Object? stripeAccountId = freezed,
  }) {
    return _then(
      _value.copyWith(
            licenseNumber: freezed == licenseNumber
                ? _value.licenseNumber
                : licenseNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            stripeAccountId: freezed == stripeAccountId
                ? _value.stripeAccountId
                : stripeAccountId // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DriverProfileUpdateImplCopyWith<$Res>
    implements $DriverProfileUpdateCopyWith<$Res> {
  factory _$$DriverProfileUpdateImplCopyWith(
    _$DriverProfileUpdateImpl value,
    $Res Function(_$DriverProfileUpdateImpl) then,
  ) = __$$DriverProfileUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? licenseNumber, String? stripeAccountId});
}

/// @nodoc
class __$$DriverProfileUpdateImplCopyWithImpl<$Res>
    extends _$DriverProfileUpdateCopyWithImpl<$Res, _$DriverProfileUpdateImpl>
    implements _$$DriverProfileUpdateImplCopyWith<$Res> {
  __$$DriverProfileUpdateImplCopyWithImpl(
    _$DriverProfileUpdateImpl _value,
    $Res Function(_$DriverProfileUpdateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licenseNumber = freezed,
    Object? stripeAccountId = freezed,
  }) {
    return _then(
      _$DriverProfileUpdateImpl(
        licenseNumber: freezed == licenseNumber
            ? _value.licenseNumber
            : licenseNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        stripeAccountId: freezed == stripeAccountId
            ? _value.stripeAccountId
            : stripeAccountId // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverProfileUpdateImpl implements _DriverProfileUpdate {
  const _$DriverProfileUpdateImpl({this.licenseNumber, this.stripeAccountId});

  factory _$DriverProfileUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverProfileUpdateImplFromJson(json);

  @override
  final String? licenseNumber;
  @override
  final String? stripeAccountId;

  @override
  String toString() {
    return 'DriverProfileUpdate(licenseNumber: $licenseNumber, stripeAccountId: $stripeAccountId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileUpdateImpl &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.stripeAccountId, stripeAccountId) ||
                other.stripeAccountId == stripeAccountId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, licenseNumber, stripeAccountId);

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileUpdateImplCopyWith<_$DriverProfileUpdateImpl> get copyWith =>
      __$$DriverProfileUpdateImplCopyWithImpl<_$DriverProfileUpdateImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverProfileUpdate value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverProfileUpdate value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverProfileUpdate value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverProfileUpdateImplToJson(this);
  }
}

abstract class _DriverProfileUpdate implements DriverProfileUpdate {
  const factory _DriverProfileUpdate({
    final String? licenseNumber,
    final String? stripeAccountId,
  }) = _$DriverProfileUpdateImpl;

  factory _DriverProfileUpdate.fromJson(Map<String, dynamic> json) =
      _$DriverProfileUpdateImpl.fromJson;

  @override
  String? get licenseNumber;
  @override
  String? get stripeAccountId;

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileUpdateImplCopyWith<_$DriverProfileUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DriverAvailabilityUpdate _$DriverAvailabilityUpdateFromJson(
  Map<String, dynamic> json,
) {
  return _DriverAvailabilityUpdate.fromJson(json);
}

/// @nodoc
mixin _$DriverAvailabilityUpdate {
  bool get isAvailable => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverAvailabilityUpdate value) $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverAvailabilityUpdate value)? $default,
  ) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverAvailabilityUpdate value)? $default, {
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this DriverAvailabilityUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverAvailabilityUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverAvailabilityUpdateCopyWith<DriverAvailabilityUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverAvailabilityUpdateCopyWith<$Res> {
  factory $DriverAvailabilityUpdateCopyWith(
    DriverAvailabilityUpdate value,
    $Res Function(DriverAvailabilityUpdate) then,
  ) = _$DriverAvailabilityUpdateCopyWithImpl<$Res, DriverAvailabilityUpdate>;
  @useResult
  $Res call({bool isAvailable});
}

/// @nodoc
class _$DriverAvailabilityUpdateCopyWithImpl<
  $Res,
  $Val extends DriverAvailabilityUpdate
>
    implements $DriverAvailabilityUpdateCopyWith<$Res> {
  _$DriverAvailabilityUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverAvailabilityUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? isAvailable = null}) {
    return _then(
      _value.copyWith(
            isAvailable: null == isAvailable
                ? _value.isAvailable
                : isAvailable // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DriverAvailabilityUpdateImplCopyWith<$Res>
    implements $DriverAvailabilityUpdateCopyWith<$Res> {
  factory _$$DriverAvailabilityUpdateImplCopyWith(
    _$DriverAvailabilityUpdateImpl value,
    $Res Function(_$DriverAvailabilityUpdateImpl) then,
  ) = __$$DriverAvailabilityUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isAvailable});
}

/// @nodoc
class __$$DriverAvailabilityUpdateImplCopyWithImpl<$Res>
    extends
        _$DriverAvailabilityUpdateCopyWithImpl<
          $Res,
          _$DriverAvailabilityUpdateImpl
        >
    implements _$$DriverAvailabilityUpdateImplCopyWith<$Res> {
  __$$DriverAvailabilityUpdateImplCopyWithImpl(
    _$DriverAvailabilityUpdateImpl _value,
    $Res Function(_$DriverAvailabilityUpdateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverAvailabilityUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? isAvailable = null}) {
    return _then(
      _$DriverAvailabilityUpdateImpl(
        isAvailable: null == isAvailable
            ? _value.isAvailable
            : isAvailable // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverAvailabilityUpdateImpl implements _DriverAvailabilityUpdate {
  const _$DriverAvailabilityUpdateImpl({required this.isAvailable});

  factory _$DriverAvailabilityUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverAvailabilityUpdateImplFromJson(json);

  @override
  final bool isAvailable;

  @override
  String toString() {
    return 'DriverAvailabilityUpdate(isAvailable: $isAvailable)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverAvailabilityUpdateImpl &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, isAvailable);

  /// Create a copy of DriverAvailabilityUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverAvailabilityUpdateImplCopyWith<_$DriverAvailabilityUpdateImpl>
  get copyWith =>
      __$$DriverAvailabilityUpdateImplCopyWithImpl<
        _$DriverAvailabilityUpdateImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DriverAvailabilityUpdate value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DriverAvailabilityUpdate value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DriverAvailabilityUpdate value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverAvailabilityUpdateImplToJson(this);
  }
}

abstract class _DriverAvailabilityUpdate implements DriverAvailabilityUpdate {
  const factory _DriverAvailabilityUpdate({required final bool isAvailable}) =
      _$DriverAvailabilityUpdateImpl;

  factory _DriverAvailabilityUpdate.fromJson(Map<String, dynamic> json) =
      _$DriverAvailabilityUpdateImpl.fromJson;

  @override
  bool get isAvailable;

  /// Create a copy of DriverAvailabilityUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverAvailabilityUpdateImplCopyWith<_$DriverAvailabilityUpdateImpl>
  get copyWith => throw _privateConstructorUsedError;
}
