import 'package:freezed_annotation/freezed_annotation.dart';

part 'verification_document.freezed.dart';
part 'verification_document.g.dart';

enum DocumentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('approved')
  approved,
  @JsonValue('rejected')
  rejected,
}

enum DocumentType {
  @JsonValue('license')
  license,
  @JsonValue('insurance')
  insurance,
  @JsonValue('registration')
  registration,
  @JsonValue('vehicle_photo')
  vehiclePhoto,
}

@freezed
class VerificationDocument with _$VerificationDocument {
  const factory VerificationDocument({
    required String id,
    required DocumentType documentType,
    required String documentUrl,
    required DocumentStatus status,
    required DateTime uploadedAt,
    String? rejectionReason,
    DateTime? reviewedAt,
    String? reviewedBy,
  }) = _VerificationDocument;

  factory VerificationDocument.fromJson(Map<String, dynamic> json) =>
      _$VerificationDocumentFromJson(json);
}

@freezed
class DocumentUploadRequest with _$DocumentUploadRequest {
  const factory DocumentUploadRequest({
    required DocumentType documentType,
    required String fileName,
    required String fileContent, // Base64 encoded
    String? description,
  }) = _DocumentUploadRequest;

  factory DocumentUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentUploadRequestFromJson(json);
}

@freezed
class VerificationStatus with _$VerificationStatus {
  const factory VerificationStatus({
    required String status,
    required List<VerificationDocument> documents,
    required bool isComplete,
    String? message,
    DateTime? submittedAt,
    DateTime? reviewedAt,
  }) = _VerificationStatus;

  factory VerificationStatus.fromJson(Map<String, dynamic> json) =>
      _$VerificationStatusFromJson(json);
}
