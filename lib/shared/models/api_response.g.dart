// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ApiSuccessImpl<T> _$$ApiSuccessImplFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => _$ApiSuccessImpl<T>(
  data: fromJsonT(json['data']),
  message: json['message'] as String?,
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ApiSuccessImplToJson<T>(
  _$ApiSuccessImpl<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'data': toJsonT(instance.data),
  'message': instance.message,
  'runtimeType': instance.$type,
};

_$ApiErrorImpl<T> _$$ApiErrorImplFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => _$ApiErrorImpl<T>(
  message: json['message'] as String,
  errorCode: json['errorCode'] as String?,
  statusCode: (json['statusCode'] as num?)?.toInt(),
  details: json['details'] as Map<String, dynamic>?,
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ApiErrorImplToJson<T>(
  _$ApiErrorImpl<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'message': instance.message,
  'errorCode': instance.errorCode,
  'statusCode': instance.statusCode,
  'details': instance.details,
  'runtimeType': instance.$type,
};

_$ApiErrorResponseImpl _$$ApiErrorResponseImplFromJson(
  Map<String, dynamic> json,
) => _$ApiErrorResponseImpl(
  message: json['message'] as String,
  errorCode: json['errorCode'] as String?,
  statusCode: (json['statusCode'] as num?)?.toInt(),
  details: json['details'] as Map<String, dynamic>?,
  errors: (json['errors'] as List<dynamic>?)?.map((e) => e as String).toList(),
);

Map<String, dynamic> _$$ApiErrorResponseImplToJson(
  _$ApiErrorResponseImpl instance,
) => <String, dynamic>{
  'message': instance.message,
  'errorCode': instance.errorCode,
  'statusCode': instance.statusCode,
  'details': instance.details,
  'errors': instance.errors,
};
