import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/accessibility_utils.dart';

/// Design system components based on design.json specifications
/// Provides reusable UI components for the driver app

/// Card component with design system styling
class DSCard extends StatelessWidget {
  const DSCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.borderRadius,
    this.backgroundColor,
    this.onTap,
  });

  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? elevation;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final card = Card(
      elevation: elevation ?? 2,
      color: backgroundColor ?? AppColors.white,
      shadowColor: AppColors.shadowMedium,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16),
        child: child,
      ),
    );

    if (onTap != null) {
      return Semantics(
        button: true,
        hint: 'Double tap to interact',
        child: AccessibleTouchTarget(
          onTap: onTap,
          child: InkWell(
            onTap: onTap,
            borderRadius: borderRadius ?? BorderRadius.circular(12),
            child: card,
          ),
        ),
      );
    }

    return card;
  }
}

/// List item component with design system styling
class DSListItem extends StatelessWidget {
  const DSListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.height,
    this.padding,
    this.showBorder = true,
  });

  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final double? height;
  final EdgeInsets? padding;
  final bool showBorder;

  @override
  Widget build(BuildContext context) {
    final effectiveHeight = height ?? 72;
    final accessibleHeight =
        effectiveHeight < AccessibilityUtils.minTouchTargetSize
        ? AccessibilityUtils.minTouchTargetSize
        : effectiveHeight;

    final semanticLabel = AccessibilityUtils.createSemanticLabel(
      label: title,
      value: subtitle,
    );

    return Semantics(
      label: semanticLabel,
      button: onTap != null,
      hint: onTap != null ? 'Double tap to select' : null,
      child: Container(
        height: accessibleHeight,
        decoration: BoxDecoration(
          color: AppColors.white,
          border: showBorder
              ? const Border(
                  bottom: BorderSide(color: AppColors.mediumGray, width: 1),
                )
              : null,
        ),
        child: ListTile(
          title: Text(
            title,
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          subtitle: subtitle != null
              ? Text(
                  subtitle!,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                )
              : null,
          leading: leading,
          trailing: trailing,
          onTap: onTap,
          contentPadding: padding ?? const EdgeInsets.symmetric(horizontal: 16),
          minVerticalPadding: 8,
        ),
      ),
    );
  }
}

/// Badge component for notifications and status indicators
class DSBadge extends StatelessWidget {
  const DSBadge({
    super.key,
    required this.text,
    this.backgroundColor,
    this.textColor,
    this.size = BadgeSize.medium,
  });

  final String text;
  final Color? backgroundColor;
  final Color? textColor;
  final BadgeSize size;

  @override
  Widget build(BuildContext context) {
    final badgeSize = _getBadgeSize();
    final fontSize = _getFontSize();

    return Container(
      constraints: BoxConstraints(minWidth: badgeSize, minHeight: badgeSize),
      padding: EdgeInsets.symmetric(
        horizontal: size == BadgeSize.small ? 6 : 8,
        vertical: size == BadgeSize.small ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.error,
        borderRadius: BorderRadius.circular(badgeSize / 2),
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: textColor ?? AppColors.white,
            height: 1.0,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  double _getBadgeSize() {
    switch (size) {
      case BadgeSize.small:
        return 16;
      case BadgeSize.medium:
        return 20;
      case BadgeSize.large:
        return 24;
    }
  }

  double _getFontSize() {
    switch (size) {
      case BadgeSize.small:
        return 10;
      case BadgeSize.medium:
        return 12;
      case BadgeSize.large:
        return 14;
    }
  }
}

enum BadgeSize { small, medium, large }

/// Status badge for driver-specific statuses
class DSStatusBadge extends StatelessWidget {
  const DSStatusBadge({
    super.key,
    required this.status,
    required this.text,
    this.size = BadgeSize.medium,
  });

  final StatusType status;
  final String text;
  final BadgeSize size;

  @override
  Widget build(BuildContext context) {
    return DSBadge(
      text: text,
      backgroundColor: _getStatusColor(),
      textColor: AppColors.white,
      size: size,
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case StatusType.success:
        return AppColors.success;
      case StatusType.warning:
        return AppColors.warning;
      case StatusType.error:
        return AppColors.error;
      case StatusType.info:
        return AppColors.info;
      case StatusType.online:
        return AppColors.onlineStatus;
      case StatusType.offline:
        return AppColors.offlineStatus;
      case StatusType.pending:
        return AppColors.documentPending;
      case StatusType.approved:
        return AppColors.documentApproved;
      case StatusType.rejected:
        return AppColors.documentRejected;
    }
  }
}

enum StatusType {
  success,
  warning,
  error,
  info,
  online,
  offline,
  pending,
  approved,
  rejected,
}

/// Rating stars component
class DSRatingStars extends StatelessWidget {
  const DSRatingStars({
    super.key,
    required this.rating,
    this.maxRating = 5,
    this.size = 16,
    this.spacing = 2,
    this.activeColor,
    this.inactiveColor,
    this.allowHalfRating = true,
  });

  final double rating;
  final int maxRating;
  final double size;
  final double spacing;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool allowHalfRating;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(maxRating, (index) {
        final starRating = index + 1;
        final isActive = rating >= starRating;
        final isHalf = allowHalfRating && rating > index && rating < starRating;

        return Padding(
          padding: EdgeInsets.only(right: index < maxRating - 1 ? spacing : 0),
          child: Icon(
            isHalf ? Icons.star_half : Icons.star,
            size: size,
            color: (isActive || isHalf)
                ? (activeColor ?? AppColors.yellow)
                : (inactiveColor ?? AppColors.mediumGray),
          ),
        );
      }),
    );
  }
}

/// Modal overlay component
class DSModalOverlay extends StatelessWidget {
  const DSModalOverlay({
    super.key,
    required this.child,
    this.onDismiss,
    this.backgroundColor,
  });

  final Widget child;
  final VoidCallback? onDismiss;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: backgroundColor ?? AppColors.overlay,
      child: GestureDetector(
        onTap: onDismiss,
        child: Center(
          child: GestureDetector(
            onTap: () {}, // Prevent dismissal when tapping modal content
            child: Container(
              constraints: const BoxConstraints(maxWidth: 320),
              margin: const EdgeInsets.all(24),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}

/// Bottom sheet component
class DSBottomSheet extends StatelessWidget {
  const DSBottomSheet({
    super.key,
    required this.child,
    this.showHandle = true,
    this.padding,
  });

  final Widget child;
  final bool showHandle;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark,
            blurRadius: 20,
            offset: Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showHandle) ...[
            const SizedBox(height: 12),
            Container(
              width: 36,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.mediumGray,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
          ],
          Padding(padding: padding ?? const EdgeInsets.all(24), child: child),
        ],
      ),
    );
  }
}

/// Driver-specific components
class DriverComponents {
  /// Availability status indicator
  static Widget availabilityIndicator({
    required bool isOnline,
    required String text,
    double size = 12,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: isOnline ? AppColors.onlineStatus : AppColors.offlineStatus,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          text,
          style: AppTextStyles.availabilityStatus.copyWith(
            color: isOnline ? AppColors.onlineStatus : AppColors.offlineStatus,
          ),
        ),
      ],
    );
  }

  /// Earnings display component
  static Widget earningsDisplay({
    required String amount,
    required String label,
    TextStyle? amountStyle,
    TextStyle? labelStyle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(amount, style: amountStyle ?? AppTextStyles.earningsAmount),
        const SizedBox(height: 4),
        Text(
          label,
          style:
              labelStyle ??
              AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
        ),
      ],
    );
  }

  /// Document status indicator
  static Widget documentStatusIndicator({
    required String status,
    required String documentType,
  }) {
    final statusType = _getDocumentStatusType(status);

    return Row(
      children: [
        DSStatusBadge(
          status: statusType,
          text: status.toUpperCase(),
          size: BadgeSize.small,
        ),
        const SizedBox(width: 8),
        Expanded(child: Text(documentType, style: AppTextStyles.bodyMedium)),
      ],
    );
  }

  static StatusType _getDocumentStatusType(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return StatusType.approved;
      case 'rejected':
        return StatusType.rejected;
      case 'pending':
      default:
        return StatusType.pending;
    }
  }
}
