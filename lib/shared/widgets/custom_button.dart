import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/accessibility_utils.dart';

/// Button style variants
enum ButtonVariant { primary, secondary, ghost, danger }

/// Button size variants
enum ButtonSize { small, medium, large }

/// Custom button widget with loading states for driver actions
class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.width,
    this.height,
    this.borderRadius,
  });

  /// Button text
  final String text;

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Button style variant
  final ButtonVariant variant;

  /// Button size
  final ButtonSize size;

  /// Whether button is in loading state
  final bool isLoading;

  /// Whether button is enabled
  final bool isEnabled;

  /// Optional icon to show before text
  final Widget? icon;

  /// Custom width (if null, uses size-based width)
  final double? width;

  /// Custom height (if null, uses size-based height)
  final double? height;

  /// Custom border radius
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    final isDisabled = !isEnabled || isLoading || onPressed == null;
    final buttonHeight = height ?? _getHeight();

    // Ensure minimum touch target size for accessibility
    final accessibleHeight =
        buttonHeight < AccessibilityUtils.minTouchTargetSize
        ? AccessibilityUtils.minTouchTargetSize
        : buttonHeight;

    return Semantics(
      button: true,
      enabled: !isDisabled,
      label: isLoading ? 'Loading, please wait' : text,
      hint: isDisabled ? 'Button is disabled' : 'Double tap to activate',
      child: SizedBox(
        width: width ?? _getWidth(),
        height: accessibleHeight,
        child: _buildButton(context, isDisabled),
      ),
    );
  }

  Widget _buildButton(BuildContext context, bool isDisabled) {
    switch (variant) {
      case ButtonVariant.primary:
        return _buildPrimaryButton(context, isDisabled);
      case ButtonVariant.secondary:
        return _buildSecondaryButton(context, isDisabled);
      case ButtonVariant.ghost:
        return _buildGhostButton(context, isDisabled);
      case ButtonVariant.danger:
        return _buildDangerButton(context, isDisabled);
    }
  }

  Widget _buildPrimaryButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? AppColors.surface : AppColors.primary,
        foregroundColor: isDisabled ? AppColors.textSecondary : Colors.white,
        elevation: isDisabled ? 0 : 1,
        shadowColor: AppColors.shadowLight,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(Colors.white),
    );
  }

  Widget _buildSecondaryButton(BuildContext context, bool isDisabled) {
    return OutlinedButton(
      onPressed: isDisabled ? null : onPressed,
      style: OutlinedButton.styleFrom(
        backgroundColor: isDisabled ? AppColors.surface : AppColors.lightGray,
        foregroundColor: isDisabled
            ? AppColors.textSecondary
            : AppColors.textPrimary,
        side: BorderSide(
          color: isDisabled ? AppColors.border : AppColors.border,
          width: 1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(
        isDisabled ? AppColors.textSecondary : AppColors.textPrimary,
      ),
    );
  }

  Widget _buildGhostButton(BuildContext context, bool isDisabled) {
    return TextButton(
      onPressed: isDisabled ? null : onPressed,
      style: TextButton.styleFrom(
        backgroundColor: Colors.transparent,
        foregroundColor: isDisabled ? AppColors.textSecondary : AppColors.blue,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(
        isDisabled ? AppColors.textSecondary : AppColors.blue,
      ),
    );
  }

  Widget _buildDangerButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? AppColors.surface : AppColors.error,
        foregroundColor: isDisabled ? AppColors.textSecondary : Colors.white,
        elevation: isDisabled ? 0 : 1,
        shadowColor: AppColors.shadowLight,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        padding: _getPadding(),
      ),
      child: _buildButtonContent(Colors.white),
    );
  }

  Widget _buildButtonContent(Color textColor) {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: _getLoadingSize(),
            height: _getLoadingSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(textColor),
            ),
          ),
          const SizedBox(width: 8),
          Text('Loading...', style: _getTextStyle().copyWith(color: textColor)),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: 8),
          Text(text, style: _getTextStyle().copyWith(color: textColor)),
        ],
      );
    }

    return Text(text, style: _getTextStyle().copyWith(color: textColor));
  }

  double? _getWidth() {
    switch (size) {
      case ButtonSize.small:
        return null; // Let content determine width
      case ButtonSize.medium:
        return null; // Let content determine width
      case ButtonSize.large:
        return double.infinity;
    }
  }

  double _getHeight() {
    switch (size) {
      case ButtonSize.small:
        return 36;
      case ButtonSize.medium:
        return 48;
      case ButtonSize.large:
        return 56;
    }
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 16);
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case ButtonSize.small:
        return AppTextStyles.bodySmall.copyWith(fontWeight: FontWeight.w600);
      case ButtonSize.medium:
        return AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600);
      case ButtonSize.large:
        return AppTextStyles.bodyLarge.copyWith(fontWeight: FontWeight.w600);
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }
}

/// Specialized button for driver actions with predefined styling
class DriverActionButton extends StatelessWidget {
  const DriverActionButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.isPrimary = true,
    this.isDanger = false,
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final Widget? icon;
  final bool isPrimary;
  final bool isDanger;

  @override
  Widget build(BuildContext context) {
    ButtonVariant variant;
    if (isDanger) {
      variant = ButtonVariant.danger;
    } else if (isPrimary) {
      variant = ButtonVariant.primary;
    } else {
      variant = ButtonVariant.secondary;
    }

    return CustomButton(
      text: text,
      onPressed: onPressed,
      variant: variant,
      size: ButtonSize.large,
      isLoading: isLoading,
      isEnabled: isEnabled,
      icon: icon,
    );
  }
}

/// Form submit button with built-in loading and validation states
class FormSubmitButton extends StatelessWidget {
  const FormSubmitButton({
    super.key,
    required this.text,
    required this.onPressed,
    required this.isValid,
    this.isLoading = false,
    this.loadingText = 'Submitting...',
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isValid;
  final bool isLoading;
  final String loadingText;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      text: isLoading ? loadingText : text,
      onPressed: isValid && !isLoading ? onPressed : null,
      variant: ButtonVariant.primary,
      size: ButtonSize.large,
      isLoading: isLoading,
      isEnabled: isValid,
    );
  }
}
