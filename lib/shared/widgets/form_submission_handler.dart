import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/errors/app_error.dart';
import 'error_display_widget.dart';

/// Widget that handles form submission states and error display
class FormSubmissionHandler extends StatelessWidget {
  const FormSubmissionHandler({
    super.key,
    required this.child,
    this.isLoading = false,
    this.error,
    this.onRetry,
    this.loadingMessage = 'Processing...',
    this.showLoadingOverlay = false,
  });

  /// Child widget to display when not loading or in error state
  final Widget child;

  /// Whether the form is currently being submitted
  final bool isLoading;

  /// Error to display if submission failed
  final AppError? error;

  /// Callback to retry the operation
  final VoidCallback? onRetry;

  /// Message to show while loading
  final String loadingMessage;

  /// Whether to show loading as an overlay or replace content
  final bool showLoadingOverlay;

  @override
  Widget build(BuildContext context) {
    if (error != null) {
      return _buildErrorState();
    }

    if (isLoading) {
      if (showLoadingOverlay) {
        return Stack(children: [child, _buildLoadingOverlay()]);
      } else {
        return _buildLoadingState();
      }
    }

    return child;
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          const SizedBox(height: 16),
          Text(
            loadingMessage,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: AppColors.overlay,
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowMedium,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              const SizedBox(height: 16),
              Text(
                loadingMessage,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return ErrorDisplayWidget(error: error!, onRetry: onRetry);
  }
}

/// Widget for displaying form field errors in a consistent way
class FormFieldError extends StatelessWidget {
  const FormFieldError({
    super.key,
    required this.error,
    this.padding = const EdgeInsets.only(top: 4, left: 12),
  });

  /// Error message to display
  final String error;

  /// Padding around the error text
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Row(
        children: [
          Icon(Icons.error_outline, size: 16, color: AppColors.error),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              error,
              style: AppTextStyles.bodySmall.copyWith(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying form success messages
class FormSuccessMessage extends StatelessWidget {
  const FormSuccessMessage({
    super.key,
    required this.message,
    this.padding = const EdgeInsets.all(16),
    this.onDismiss,
  });

  /// Success message to display
  final String message;

  /// Padding around the message
  final EdgeInsets padding;

  /// Callback when message is dismissed
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.check_circle_outline, color: AppColors.success, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onDismiss,
              child: Icon(Icons.close, color: AppColors.success, size: 18),
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget for displaying form validation summary
class FormValidationSummary extends StatelessWidget {
  const FormValidationSummary({
    super.key,
    required this.errors,
    this.title = 'Please fix the following errors:',
    this.padding = const EdgeInsets.all(16),
  });

  /// Map of field names to error messages
  final Map<String, String> errors;

  /// Title for the error summary
  final String title;

  /// Padding around the summary
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    if (errors.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: padding,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.error_outline, color: AppColors.error, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...errors.entries.map(
            (entry) => Padding(
              padding: const EdgeInsets.only(left: 28, bottom: 4),
              child: Text(
                '• ${entry.value}',
                style: AppTextStyles.bodySmall.copyWith(color: AppColors.error),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
