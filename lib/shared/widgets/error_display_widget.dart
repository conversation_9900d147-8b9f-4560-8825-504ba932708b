import 'package:flutter/material.dart';
import '../../core/errors/app_error.dart';
import '../../core/errors/error_handler.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/constants/app_constants.dart';

/// Error display widget for showing errors to users
class ErrorDisplayWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showRetryButton;
  final bool showDismissButton;
  final EdgeInsets? padding;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showRetryButton = true,
    this.showDismissButton = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final severity = ErrorHandler.getErrorSeverity(error);
    final isRetryable = ErrorHandler.isRetryable(error);
    final message = ErrorHandler.getUserFriendlyMessage(error);

    return Container(
      padding: padding ?? const EdgeInsets.all(AppConstants.spacingMd),
      margin: const EdgeInsets.all(AppConstants.spacingMd),
      decoration: BoxDecoration(
        color: _getBackgroundColor(severity),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(color: _getBorderColor(severity), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getErrorIcon(severity),
                color: _getIconColor(severity),
                size: 24,
              ),
              const SizedBox(width: AppConstants.spacingSm),
              Expanded(
                child: Text(
                  _getErrorTitle(severity),
                  style: AppTextStyles.labelLarge.copyWith(
                    color: _getTextColor(severity),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (showDismissButton && onDismiss != null)
                IconButton(
                  onPressed: onDismiss,
                  icon: const Icon(Icons.close),
                  iconSize: 20,
                  color: _getTextColor(severity),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
            ],
          ),
          const SizedBox(height: AppConstants.spacingSm),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: _getTextColor(severity),
            ),
          ),
          if (showRetryButton && isRetryable && onRetry != null) ...[
            const SizedBox(height: AppConstants.spacingMd),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: onRetry,
                style: OutlinedButton.styleFrom(
                  foregroundColor: _getIconColor(severity),
                  side: BorderSide(color: _getIconColor(severity)),
                ),
                child: const Text('Try Again'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getBackgroundColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return AppColors.info.withValues(alpha: 0.1);
      case ErrorSeverity.warning:
        return AppColors.warning.withValues(alpha: 0.1);
      case ErrorSeverity.error:
      case ErrorSeverity.critical:
        return AppColors.error.withValues(alpha: 0.1);
    }
  }

  Color _getBorderColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return AppColors.info.withValues(alpha: 0.3);
      case ErrorSeverity.warning:
        return AppColors.warning.withValues(alpha: 0.3);
      case ErrorSeverity.error:
      case ErrorSeverity.critical:
        return AppColors.error.withValues(alpha: 0.3);
    }
  }

  Color _getIconColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return AppColors.info;
      case ErrorSeverity.warning:
        return AppColors.warning;
      case ErrorSeverity.error:
      case ErrorSeverity.critical:
        return AppColors.error;
    }
  }

  Color _getTextColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return AppColors.info;
      case ErrorSeverity.warning:
        return AppColors.warning;
      case ErrorSeverity.error:
      case ErrorSeverity.critical:
        return AppColors.error;
    }
  }

  IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return Icons.info_outline;
      case ErrorSeverity.warning:
        return Icons.warning_amber_outlined;
      case ErrorSeverity.error:
      case ErrorSeverity.critical:
        return Icons.error_outline;
    }
  }

  String _getErrorTitle(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return 'Information';
      case ErrorSeverity.warning:
        return 'Warning';
      case ErrorSeverity.error:
        return 'Error';
      case ErrorSeverity.critical:
        return 'Critical Error';
    }
  }
}

/// Compact error display for inline use
class CompactErrorWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;

  const CompactErrorWidget({super.key, required this.error, this.onRetry});

  @override
  Widget build(BuildContext context) {
    final message = ErrorHandler.getUserFriendlyMessage(error);
    final isRetryable = ErrorHandler.isRetryable(error);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.spacingMd,
        vertical: AppConstants.spacingSm,
      ),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(
          color: AppColors.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppColors.error, size: 16),
          const SizedBox(width: AppConstants.spacingSm),
          Expanded(
            child: Text(
              message,
              style: AppTextStyles.bodySmall.copyWith(color: AppColors.error),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (isRetryable && onRetry != null) ...[
            const SizedBox(width: AppConstants.spacingSm),
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.spacingSm,
                ),
                minimumSize: const Size(0, 32),
              ),
              child: const Text('Retry'),
            ),
          ],
        ],
      ),
    );
  }
}

/// Driver-specific error widgets
class DriverErrorWidgets {
  /// Location permission error widget
  static Widget locationPermissionError({
    required VoidCallback onOpenSettings,
    VoidCallback? onDismiss,
  }) {
    return ErrorDisplayWidget(
      error: const AppError.location(
        message:
            'Location access is required to go online and accept rides. Please enable location permissions in your device settings.',
        errorType: LocationErrorType.permissionDenied,
      ),
      onRetry: onOpenSettings,
      onDismiss: onDismiss,
      showRetryButton: true,
      showDismissButton: onDismiss != null,
    );
  }

  /// Document upload error widget
  static Widget documentUploadError({
    required AppError error,
    required VoidCallback onRetry,
    VoidCallback? onDismiss,
  }) {
    return ErrorDisplayWidget(
      error: error,
      onRetry: onRetry,
      onDismiss: onDismiss,
      showRetryButton: true,
      showDismissButton: onDismiss != null,
    );
  }

  /// Network connectivity error widget
  static Widget networkError({
    required VoidCallback onRetry,
    VoidCallback? onDismiss,
  }) {
    return ErrorDisplayWidget(
      error: const AppError.network(
        message:
            'No internet connection. Please check your network settings and try again.',
        details: 'Network unavailable',
      ),
      onRetry: onRetry,
      onDismiss: onDismiss,
      showRetryButton: true,
      showDismissButton: onDismiss != null,
    );
  }

  /// Authentication error widget
  static Widget authenticationError({
    required VoidCallback onLogin,
    VoidCallback? onDismiss,
  }) {
    return ErrorDisplayWidget(
      error: const AppError.authentication(
        message: 'Your session has expired. Please log in again to continue.',
        errorCode: 'session_expired',
      ),
      onRetry: onLogin,
      onDismiss: onDismiss,
      showRetryButton: true,
      showDismissButton: onDismiss != null,
    );
  }
}
