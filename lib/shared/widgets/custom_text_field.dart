import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/accessibility_utils.dart';

/// Custom text field widget with validation states for driver forms
class CustomTextField extends StatefulWidget {
  const CustomTextField({
    super.key,
    required this.label,
    this.hint,
    this.value,
    this.onChanged,
    this.validator,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.inputFormatters,
    this.onTap,
    this.onSubmitted,
    this.focusNode,
    this.controller,
    this.autofocus = false,
    this.showCounter = false,
  });

  /// Label text for the field
  final String label;

  /// Hint text for the field
  final String? hint;

  /// Initial value for the field
  final String? value;

  /// Callback when text changes
  final ValueChanged<String>? onChanged;

  /// Validation function
  final String? Function(String?)? validator;

  /// Error text to display
  final String? errorText;

  /// Icon to show at the beginning of the field
  final Widget? prefixIcon;

  /// Icon to show at the end of the field
  final Widget? suffixIcon;

  /// Keyboard type
  final TextInputType? keyboardType;

  /// Text input action
  final TextInputAction? textInputAction;

  /// Text capitalization
  final TextCapitalization textCapitalization;

  /// Whether to obscure text (for passwords)
  final bool obscureText;

  /// Whether the field is enabled
  final bool enabled;

  /// Whether the field is read-only
  final bool readOnly;

  /// Maximum number of lines
  final int maxLines;

  /// Maximum length of text
  final int? maxLength;

  /// Input formatters
  final List<TextInputFormatter>? inputFormatters;

  /// Callback when field is tapped
  final VoidCallback? onTap;

  /// Callback when field is submitted
  final ValueChanged<String>? onSubmitted;

  /// Focus node for the field
  final FocusNode? focusNode;

  /// Text editing controller
  final TextEditingController? controller;

  /// Whether to autofocus
  final bool autofocus;

  /// Whether to show character counter
  final bool showCounter;

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _controller =
        widget.controller ?? TextEditingController(text: widget.value);
    _focusNode = widget.focusNode ?? FocusNode();

    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final hasError = widget.errorText != null && widget.errorText!.isNotEmpty;

    // Create accessible semantic label
    final semanticLabel = AccessibilityUtils.createSemanticLabel(
      label: widget.label,
      value: _controller.text.isNotEmpty ? _controller.text : null,
      hint: widget.hint,
      state: hasError ? 'Error: ${widget.errorText}' : null,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Semantics(
          label: semanticLabel,
          textField: true,
          enabled: widget.enabled,
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              minHeight: AccessibilityUtils.minTouchTargetSize,
            ),
            child: TextFormField(
              controller: _controller,
              focusNode: _focusNode,
              onChanged: widget.onChanged,
              validator: widget.validator,
              keyboardType: widget.keyboardType,
              textInputAction: widget.textInputAction,
              textCapitalization: widget.textCapitalization,
              obscureText: widget.obscureText,
              enabled: widget.enabled,
              readOnly: widget.readOnly,
              maxLines: widget.maxLines,
              maxLength: widget.showCounter ? widget.maxLength : null,
              inputFormatters: widget.inputFormatters,
              onTap: widget.onTap,
              onFieldSubmitted: widget.onSubmitted,
              autofocus: widget.autofocus,
              style: AppTextStyles.bodyMedium.copyWith(
                color: widget.enabled
                    ? AppColors.textPrimary
                    : AppColors.textSecondary,
              ),
              decoration: InputDecoration(
                labelText: widget.label,
                hintText: widget.hint,
                prefixIcon: widget.prefixIcon,
                suffixIcon: widget.suffixIcon,
                errorText: widget.errorText,
                counterText: widget.showCounter ? null : '',

                // Border styling - aligned with design.json specifications
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.border, width: 1),
                ),

                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: hasError ? AppColors.error : AppColors.border,
                    width: 1,
                  ),
                ),

                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: hasError ? AppColors.error : AppColors.borderFocus,
                    width: 2,
                  ),
                ),

                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.error, width: 1),
                ),

                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.error, width: 2),
                ),

                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: AppColors.border.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),

                // Label styling
                labelStyle: AppTextStyles.bodyMedium.copyWith(
                  color: hasError
                      ? AppColors.error
                      : _isFocused
                      ? AppColors.primary
                      : AppColors.textSecondary,
                ),

                // Hint styling
                hintStyle: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),

                // Error styling
                errorStyle: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.error,
                ),

                // Content padding
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),

                // Fill color
                filled: true,
                fillColor: widget.enabled
                    ? Colors.transparent
                    : AppColors.surface.withValues(alpha: 0.5),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Custom text field specifically for driver forms with built-in validation
class DriverTextField extends StatelessWidget {
  const DriverTextField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    required this.validator,
    this.hint,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.inputFormatters,
    this.onTap,
    this.onSubmitted,
    this.focusNode,
    this.autofocus = false,
    this.showCounter = false,
  });

  final String label;
  final String value;
  final ValueChanged<String> onChanged;
  final String? Function(String?) validator;
  final String? hint;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final TextCapitalization textCapitalization;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int maxLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final VoidCallback? onTap;
  final ValueChanged<String>? onSubmitted;
  final FocusNode? focusNode;
  final bool autofocus;
  final bool showCounter;

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      label: label,
      value: value,
      onChanged: onChanged,
      validator: validator,
      hint: hint,
      errorText: errorText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      textCapitalization: textCapitalization,
      obscureText: obscureText,
      enabled: enabled,
      readOnly: readOnly,
      maxLines: maxLines,
      maxLength: maxLength,
      inputFormatters: inputFormatters,
      onTap: onTap,
      onSubmitted: onSubmitted,
      focusNode: focusNode,
      autofocus: autofocus,
      showCounter: showCounter,
    );
  }
}
