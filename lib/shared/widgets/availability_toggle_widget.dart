import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/accessibility_utils.dart';
import '../../features/driver/presentation/providers/driver_profile_state.dart';
import '../../features/driver/presentation/providers/driver_availability_notifier.dart';

/// A comprehensive availability toggle widget with clear online/offline indicators
/// and confirmation dialogs for state changes
class AvailabilityToggleWidget extends ConsumerWidget {
  final bool showConfirmationDialog;
  final bool showStatusText;
  final bool isCompact;

  const AvailabilityToggleWidget({
    super.key,
    this.showConfirmationDialog = true,
    this.showStatusText = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final availabilityState = ref.watch(driverAvailabilityProvider);

    if (isCompact) {
      return _buildCompactToggle(context, ref, availabilityState);
    }

    return _buildFullToggle(context, ref, availabilityState);
  }

  Widget _buildFullToggle(
    BuildContext context,
    WidgetRef ref,
    DriverAvailabilityState state,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Availability Status',
                  style: AppTextStyles.heading3.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                _buildToggleSwitch(context, ref, state),
              ],
            ),
            if (showStatusText) ...[
              const SizedBox(height: 12),
              _buildStatusDisplay(state),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompactToggle(
    BuildContext context,
    WidgetRef ref,
    DriverAvailabilityState state,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getStatusColor(state).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(state).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildStatusIcon(state),
          const SizedBox(width: 8),
          if (showStatusText) ...[
            Text(
              _getStatusText(state),
              style: AppTextStyles.bodyMedium.copyWith(
                color: _getStatusColor(state),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 12),
          ],
          _buildToggleSwitch(context, ref, state),
        ],
      ),
    );
  }

  Widget _buildToggleSwitch(
    BuildContext context,
    WidgetRef ref,
    DriverAvailabilityState state,
  ) {
    final isOnline = state is DriverAvailabilityOnline;
    final isChanging =
        state is DriverAvailabilityGoingOnline ||
        state is DriverAvailabilityGoingOffline;

    final semanticLabel = AccessibilityUtils.getAvailabilityLabel(isOnline);
    final semanticHint = isChanging
        ? 'Availability is changing, please wait'
        : 'Double tap to ${isOnline ? 'go offline' : 'go online'}';

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      toggled: isOnline,
      enabled: !isChanging,
      child: AccessibleTouchTarget(
        onTap: isChanging ? null : () => _handleToggle(context, ref, !isOnline),
        child: Switch(
          value: isOnline,
          onChanged: isChanging
              ? null
              : (value) => _handleToggle(context, ref, value),
          activeColor: AppColors.success,
          inactiveThumbColor: AppColors.textSecondary,
          inactiveTrackColor: AppColors.textSecondary.withValues(alpha: 0.3),
        ),
      ),
    );
  }

  Widget _buildStatusDisplay(DriverAvailabilityState state) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getStatusColor(state).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildStatusIcon(state),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _getStatusDescription(state),
              style: AppTextStyles.bodySmall.copyWith(
                color: _getStatusColor(state),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (state is DriverAvailabilityError) ...[
            const SizedBox(width: 8),
            _buildRetryButton(state),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusIcon(DriverAvailabilityState state) {
    return state.when(
      offline: () =>
          Icon(Icons.offline_bolt, color: AppColors.textSecondary, size: 16),
      goingOnline: () => SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.warning),
        ),
      ),
      online: (location) =>
          Icon(Icons.online_prediction, color: AppColors.success, size: 16),
      goingOffline: () => SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.warning),
        ),
      ),
      error: (message) =>
          Icon(Icons.error_outline, color: AppColors.error, size: 16),
    );
  }

  String _getStatusText(DriverAvailabilityState state) {
    return state.when(
      offline: () => 'Offline',
      goingOnline: () => 'Going Online...',
      online: (location) => 'Online',
      goingOffline: () => 'Going Offline...',
      error: (message) => 'Error',
    );
  }

  String _getStatusDescription(DriverAvailabilityState state) {
    return state.when(
      offline: () => 'You are offline and not receiving ride requests',
      goingOnline: () =>
          'Connecting to the network and enabling location tracking...',
      online: (location) => 'You are online and available for ride requests',
      goingOffline: () =>
          'Disconnecting from the network and stopping location tracking...',
      error: (message) => 'Error: $message',
    );
  }

  Color _getStatusColor(DriverAvailabilityState state) {
    return state.when(
      offline: () => AppColors.textSecondary,
      goingOnline: () => AppColors.warning,
      online: (location) => AppColors.success,
      goingOffline: () => AppColors.warning,
      error: (message) => AppColors.error,
    );
  }

  Future<void> _handleToggle(
    BuildContext context,
    WidgetRef ref,
    bool value,
  ) async {
    if (showConfirmationDialog) {
      final confirmed = await _showConfirmationDialog(context, value);
      if (!confirmed) return;
    }

    if (value) {
      await ref.read(driverAvailabilityProvider.notifier).goOnline();
    } else {
      await ref.read(driverAvailabilityProvider.notifier).goOffline();
    }
  }

  Widget _buildRetryButton(DriverAvailabilityError state) {
    return Consumer(
      builder: (context, ref, child) {
        return InkWell(
          onTap: () async {
            // Clear error and attempt to handle location service recovery
            ref.read(driverAvailabilityProvider.notifier).clearError();
            await ref
                .read(driverAvailabilityProvider.notifier)
                .handleLocationServiceRecovery();
          },
          borderRadius: BorderRadius.circular(4),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: AppColors.error.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.refresh, size: 12, color: AppColors.error),
                const SizedBox(width: 4),
                Text(
                  'Retry',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<bool> _showConfirmationDialog(
    BuildContext context,
    bool goingOnline,
  ) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              goingOnline ? 'Go Online?' : 'Go Offline?',
              style: AppTextStyles.heading3,
            ),
            content: Text(
              goingOnline
                  ? 'You will start receiving ride requests and your location will be tracked.'
                  : 'You will stop receiving ride requests and location tracking will be disabled.',
              style: AppTextStyles.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(
                  'Cancel',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: goingOnline
                      ? AppColors.success
                      : AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: Text(
                  goingOnline ? 'Go Online' : 'Go Offline',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }
}

/// A simple availability status indicator without toggle functionality
class AvailabilityStatusIndicator extends ConsumerWidget {
  final bool showIcon;
  final bool showText;

  const AvailabilityStatusIndicator({
    super.key,
    this.showIcon = true,
    this.showText = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final availabilityState = ref.watch(driverAvailabilityProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(availabilityState).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(availabilityState).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            _buildStatusIcon(availabilityState),
            if (showText) const SizedBox(width: 6),
          ],
          if (showText)
            Text(
              _getStatusText(availabilityState),
              style: AppTextStyles.bodySmall.copyWith(
                color: _getStatusColor(availabilityState),
                fontWeight: FontWeight.w600,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusIcon(DriverAvailabilityState state) {
    return state.when(
      offline: () =>
          Icon(Icons.offline_bolt, color: AppColors.textSecondary, size: 14),
      goingOnline: () => SizedBox(
        width: 14,
        height: 14,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.warning),
        ),
      ),
      online: (location) =>
          Icon(Icons.online_prediction, color: AppColors.success, size: 14),
      goingOffline: () => SizedBox(
        width: 14,
        height: 14,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.warning),
        ),
      ),
      error: (message) =>
          Icon(Icons.error_outline, color: AppColors.error, size: 14),
    );
  }

  String _getStatusText(DriverAvailabilityState state) {
    return state.when(
      offline: () => 'Offline',
      goingOnline: () => 'Going Online',
      online: (location) => 'Online',
      goingOffline: () => 'Going Offline',
      error: (message) => 'Error',
    );
  }

  Color _getStatusColor(DriverAvailabilityState state) {
    return state.when(
      offline: () => AppColors.textSecondary,
      goingOnline: () => AppColors.warning,
      online: (location) => AppColors.success,
      goingOffline: () => AppColors.warning,
      error: (message) => AppColors.error,
    );
  }
}
