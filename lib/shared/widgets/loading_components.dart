import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

/// Loading and feedback components for driver operations
/// Based on design.json specifications with smooth animations

/// Primary loading indicator with app branding
class DSLoadingIndicator extends StatelessWidget {
  const DSLoadingIndicator({
    super.key,
    this.size = 24,
    this.color,
    this.strokeWidth = 3,
  });

  final double size;
  final Color? color;
  final double strokeWidth;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(color ?? AppColors.primary),
      ),
    );
  }
}

/// Full screen loading overlay
class DSLoadingOverlay extends StatelessWidget {
  const DSLoadingOverlay({
    super.key,
    this.message,
    this.backgroundColor,
    this.showMessage = true,
  });

  final String? message;
  final Color? backgroundColor;
  final bool showMessage;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: backgroundColor ?? AppColors.overlay,
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowMedium,
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const DSLoadingIndicator(size: 32),
              if (showMessage) ...[
                const SizedBox(height: 16),
                Text(
                  message ?? 'Loading...',
                  style: AppTextStyles.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Skeleton loading component for list items
class DSSkeletonLoader extends StatefulWidget {
  const DSSkeletonLoader({
    super.key,
    this.width,
    this.height = 16,
    this.borderRadius = 4,
  });

  final double? width;
  final double height;
  final double borderRadius;

  @override
  State<DSSkeletonLoader> createState() => _DSSkeletonLoaderState();
}

class _DSSkeletonLoaderState extends State<DSSkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: AppColors.lightGray.withValues(alpha: _animation.value),
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
        );
      },
    );
  }
}

/// Skeleton screen for driver profile loading
class DSProfileSkeletonScreen extends StatelessWidget {
  const DSProfileSkeletonScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header skeleton
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.lightGray,
                  borderRadius: BorderRadius.circular(40),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DSSkeletonLoader(width: 120, height: 20),
                    const SizedBox(height: 8),
                    DSSkeletonLoader(width: 80, height: 16),
                    const SizedBox(height: 8),
                    DSSkeletonLoader(width: 100, height: 16),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 32),

          // Stats skeleton
          Row(
            children: [
              Expanded(child: _buildStatSkeleton()),
              const SizedBox(width: 16),
              Expanded(child: _buildStatSkeleton()),
              const SizedBox(width: 16),
              Expanded(child: _buildStatSkeleton()),
            ],
          ),
          const SizedBox(height: 32),

          // List items skeleton
          ...List.generate(4, (index) => _buildListItemSkeleton()),
        ],
      ),
    );
  }

  Widget _buildStatSkeleton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightGray,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          DSSkeletonLoader(width: 40, height: 24),
          const SizedBox(height: 8),
          DSSkeletonLoader(width: 60, height: 14),
        ],
      ),
    );
  }

  Widget _buildListItemSkeleton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.lightGray,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DSSkeletonLoader(width: double.infinity, height: 16),
                const SizedBox(height: 8),
                DSSkeletonLoader(width: 120, height: 14),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Success message component with animation
class DSSuccessMessage extends StatefulWidget {
  const DSSuccessMessage({
    super.key,
    required this.message,
    this.onDismiss,
    this.duration = const Duration(seconds: 3),
    this.showIcon = true,
  });

  final String message;
  final VoidCallback? onDismiss;
  final Duration duration;
  final bool showIcon;

  @override
  State<DSSuccessMessage> createState() => _DSSuccessMessageState();
}

class _DSSuccessMessageState extends State<DSSuccessMessage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _animationController.forward();

    // Auto dismiss after duration
    Future.delayed(widget.duration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _dismiss() {
    _animationController.reverse().then((_) {
      if (widget.onDismiss != null) {
        widget.onDismiss!();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.success,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.success.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  if (widget.showIcon) ...[
                    Icon(Icons.check_circle, color: AppColors.white, size: 24),
                    const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: Text(
                      widget.message,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: _dismiss,
                    child: Icon(Icons.close, color: AppColors.white, size: 20),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Error message component with retry option
class DSErrorMessage extends StatelessWidget {
  const DSErrorMessage({
    super.key,
    required this.message,
    this.onRetry,
    this.onDismiss,
    this.showIcon = true,
  });

  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showIcon;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.error.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (showIcon) ...[
                Icon(Icons.error, color: AppColors.white, size: 24),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Text(
                  message,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (onDismiss != null)
                GestureDetector(
                  onTap: onDismiss,
                  child: Icon(Icons.close, color: AppColors.white, size: 20),
                ),
            ],
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: onRetry,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.white,
                  side: BorderSide(color: AppColors.white),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text('Try Again'),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Driver-specific loading components
class DriverLoadingComponents {
  /// Location loading indicator
  static Widget locationLoading({String? message}) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              DSLoadingIndicator(size: 32),
              Icon(Icons.location_on, color: AppColors.primary, size: 16),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            message ?? 'Getting your location...',
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Document upload progress indicator
  static Widget documentUploadProgress({
    required double progress,
    String? message,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 48,
                height: 48,
                child: CircularProgressIndicator(
                  value: progress,
                  strokeWidth: 4,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  backgroundColor: AppColors.lightGray,
                ),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            message ?? 'Uploading document...',
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Availability toggle loading
  static Widget availabilityToggleLoading({required bool isGoingOnline}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        DSLoadingIndicator(size: 16),
        const SizedBox(width: 8),
        Text(
          isGoingOnline ? 'Going online...' : 'Going offline...',
          style: AppTextStyles.bodyMedium,
        ),
      ],
    );
  }
}

/// Animated transitions for driver workflows
class DSAnimatedTransitions {
  /// Slide in from bottom animation
  static Widget slideInFromBottom({
    required Widget child,
    required AnimationController controller,
    Duration delay = Duration.zero,
  }) {
    final animation = Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: controller,
            curve: Interval(
              delay.inMilliseconds / controller.duration!.inMilliseconds,
              1.0,
              curve: Curves.easeOutCubic,
            ),
          ),
        );

    return SlideTransition(position: animation, child: child);
  }

  /// Fade in animation
  static Widget fadeIn({
    required Widget child,
    required AnimationController controller,
    Duration delay = Duration.zero,
  }) {
    final animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: Interval(
          delay.inMilliseconds / controller.duration!.inMilliseconds,
          1.0,
          curve: Curves.easeIn,
        ),
      ),
    );

    return FadeTransition(opacity: animation, child: child);
  }

  /// Scale in animation
  static Widget scaleIn({
    required Widget child,
    required AnimationController controller,
    Duration delay = Duration.zero,
  }) {
    final animation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: Interval(
          delay.inMilliseconds / controller.duration!.inMilliseconds,
          1.0,
          curve: Curves.elasticOut,
        ),
      ),
    );

    return ScaleTransition(scale: animation, child: child);
  }
}
