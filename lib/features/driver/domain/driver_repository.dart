import '../../../shared/models/models.dart';

/// Repository interface for driver-specific operations
/// Defines the contract for driver profile and vehicle management
abstract class DriverRepository {
  /// Driver Profile Operations
  Future<DriverProfile> getDriverProfile();
  Future<DriverProfile> createDriverProfile(DriverProfileCreate profileData);
  Future<DriverProfile> updateDriverProfile(DriverProfileUpdate profileData);

  /// Vehicle Operations
  Future<VehicleInfo> getVehicleInfo();
  Future<VehicleInfo> addVehicleInfo(VehicleInfoCreate vehicleData);
  Future<VehicleInfo> updateVehicleInfo(VehicleInfoUpdate vehicleData);

  /// Availability Management
  Future<void> updateAvailability(bool isAvailable);
  Future<bool> getAvailabilityStatus();

  /// Location Operations
  Future<void> updateLocation(LocationUpdate locationData);
  Future<LocationInfo?> getLastKnownLocation();

  /// Earnings and Statistics
  Future<EarningsInfo> getEarnings();
  Future<DriverStats> getDriverStats();

  /// Verification Status
  Future<VerificationStatus> getVerificationStatus();

  /// Cache Management
  Future<void> clearDriverCache();
  Future<bool> hasCompleteProfile();
}
