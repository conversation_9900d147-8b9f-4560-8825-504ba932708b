import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/utils/form_validators.dart';

/// Provider for driver profile form state
final driverProfileFormProvider =
    StateNotifierProvider<DriverProfileFormNotifier, DriverProfileFormState>(
      (ref) => DriverProfileFormNotifier(),
    );

/// Provider for vehicle form state
final vehicleFormProvider =
    StateNotifierProvider<VehicleFormNotifier, VehicleFormState>(
      (ref) => VehicleFormNotifier(),
    );

/// Notifier for driver profile form state management
class DriverProfileFormNotifier extends StateNotifier<DriverProfileFormState> {
  DriverProfileFormNotifier() : super(const DriverProfileFormState());

  /// Update license number field
  void updateLicenseNumber(String value) {
    state = state.copyWith(
      licenseNumber: value,
      fieldErrors: _removeFieldError('licenseNumber'),
    );
    _validateForm();
  }

  /// Update Stripe account ID field
  void updateStripeAccountId(String value) {
    state = state.copyWith(
      stripeAccountId: value,
      fieldErrors: _removeFieldError('stripeAccountId'),
    );
    _validateForm();
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set general error message
  void setError(String? errorMessage) {
    state = state.copyWith(errorMessage: errorMessage);
  }

  /// Set field-specific error
  void setFieldError(String fieldName, String error) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    updatedErrors[fieldName] = error;
    state = state.copyWith(fieldErrors: updatedErrors);
    _validateForm();
  }

  /// Clear all errors
  void clearErrors() {
    state = state.copyWith(errorMessage: null, fieldErrors: {});
    _validateForm();
  }

  /// Mark form as submitted
  void markAsSubmitted() {
    state = state.copyWith(hasBeenSubmitted: true);
  }

  /// Reset form to initial state
  void reset() {
    state = const DriverProfileFormState();
  }

  /// Load existing data into form
  void loadExistingData(DriverProfile profile) {
    state = state.copyWith(
      licenseNumber: profile.licenseNumber,
      stripeAccountId: profile.stripeAccountId ?? '',
    );
    _validateForm();
  }

  /// Validate entire form
  void validateForm() {
    _validateForm();
  }

  /// Get create data from form
  DriverProfileCreate getCreateData() {
    return DriverProfileCreate(
      licenseNumber: state.licenseNumber.trim(),
      stripeAccountId: state.stripeAccountId.trim().isEmpty
          ? null
          : state.stripeAccountId.trim(),
    );
  }

  /// Get update data from form
  DriverProfileUpdate getUpdateData() {
    return DriverProfileUpdate(
      licenseNumber: state.licenseNumber.trim(),
      stripeAccountId: state.stripeAccountId.trim().isEmpty
          ? null
          : state.stripeAccountId.trim(),
    );
  }

  /// Submit form with driver profile data
  Future<void> submitForm(DriverProfileCreate profileData) async {
    setLoading(true);
    markAsSubmitted();

    try {
      // This would typically call a repository or service
      // For now, we'll simulate the submission
      await Future.delayed(const Duration(seconds: 2));

      // Clear any previous errors on successful submission
      clearErrors();
    } catch (e) {
      setError('Failed to create driver profile: ${e.toString()}');
    } finally {
      setLoading(false);
    }
  }

  /// Clear general error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Private method to validate the entire form
  void _validateForm() {
    final errors = <String, String>{};

    // Validate license number
    final licenseError = FormValidators.validateLicenseNumber(
      state.licenseNumber,
    );
    if (licenseError != null) {
      errors['licenseNumber'] = licenseError;
    }

    // Validate Stripe account ID (optional)
    final stripeError = FormValidators.validateStripeAccountId(
      state.stripeAccountId,
    );
    if (stripeError != null) {
      errors['stripeAccountId'] = stripeError;
    }

    // Update state with validation results
    state = state.copyWith(
      fieldErrors: errors,
      isValid: errors.isEmpty && state.licenseNumber.trim().isNotEmpty,
    );
  }

  /// Remove error for a specific field
  Map<String, String> _removeFieldError(String fieldName) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    updatedErrors.remove(fieldName);
    return updatedErrors;
  }
}

/// Notifier for vehicle form state management
class VehicleFormNotifier extends StateNotifier<VehicleFormState> {
  VehicleFormNotifier() : super(const VehicleFormState());

  /// Update make field
  void updateMake(String value) {
    state = state.copyWith(make: value, fieldErrors: _removeFieldError('make'));
    _validateForm();
  }

  /// Update model field
  void updateModel(String value) {
    state = state.copyWith(
      model: value,
      fieldErrors: _removeFieldError('model'),
    );
    _validateForm();
  }

  /// Update year field
  void updateYear(String value) {
    state = state.copyWith(year: value, fieldErrors: _removeFieldError('year'));
    _validateForm();
  }

  /// Update color field
  void updateColor(String value) {
    state = state.copyWith(
      color: value,
      fieldErrors: _removeFieldError('color'),
    );
    _validateForm();
  }

  /// Update license plate field
  void updateLicensePlate(String value) {
    state = state.copyWith(
      licensePlate: value.toUpperCase(),
      fieldErrors: _removeFieldError('licensePlate'),
    );
    _validateForm();
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set general error message
  void setError(String? errorMessage) {
    state = state.copyWith(errorMessage: errorMessage);
  }

  /// Set field-specific error
  void setFieldError(String fieldName, String error) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    updatedErrors[fieldName] = error;
    state = state.copyWith(fieldErrors: updatedErrors);
    _validateForm();
  }

  /// Clear all errors
  void clearErrors() {
    state = state.copyWith(errorMessage: null, fieldErrors: {});
    _validateForm();
  }

  /// Clear general error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Mark form as submitted
  void markAsSubmitted() {
    state = state.copyWith(hasBeenSubmitted: true);
  }

  /// Set update mode
  void setUpdateMode(bool isUpdateMode) {
    state = state.copyWith(isUpdateMode: isUpdateMode);
  }

  /// Reset form to initial state
  void reset() {
    state = const VehicleFormState();
  }

  /// Load existing data into form
  void loadExistingData(VehicleInfo vehicleInfo) {
    state = state.copyWith(
      make: vehicleInfo.make,
      model: vehicleInfo.model,
      year: vehicleInfo.year.toString(),
      color: vehicleInfo.color,
      licensePlate: vehicleInfo.licensePlate,
      isUpdateMode: true,
    );
    _validateForm();
  }

  /// Validate entire form
  void validateForm() {
    _validateForm();
  }

  /// Get create data from form
  VehicleInfoCreate getCreateData() {
    return VehicleInfoCreate(
      make: state.make.trim(),
      model: state.model.trim(),
      year: state.parsedYear!,
      color: state.color.trim(),
      licensePlate: state.licensePlate.trim().toUpperCase(),
    );
  }

  /// Get update data from form
  VehicleInfoUpdate getUpdateData() {
    return VehicleInfoUpdate(
      make: state.make.trim(),
      model: state.model.trim(),
      year: state.parsedYear,
      color: state.color.trim(),
      licensePlate: state.licensePlate.trim().toUpperCase(),
    );
  }

  /// Private method to validate the entire form
  void _validateForm() {
    final errors = <String, String>{};

    // Validate make
    final makeError = FormValidators.validateVehicleMake(state.make);
    if (makeError != null) {
      errors['make'] = makeError;
    }

    // Validate model
    final modelError = FormValidators.validateVehicleModel(state.model);
    if (modelError != null) {
      errors['model'] = modelError;
    }

    // Validate year
    final yearError = FormValidators.validateVehicleYear(state.year);
    if (yearError != null) {
      errors['year'] = yearError;
    }

    // Validate color
    final colorError = FormValidators.validateVehicleColor(state.color);
    if (colorError != null) {
      errors['color'] = colorError;
    }

    // Validate license plate
    final plateError = FormValidators.validateLicensePlate(state.licensePlate);
    if (plateError != null) {
      errors['licensePlate'] = plateError;
    }

    // Update state with validation results
    state = state.copyWith(
      fieldErrors: errors,
      isValid: errors.isEmpty && state.allFieldsFilled,
    );
  }

  /// Remove error for a specific field
  Map<String, String> _removeFieldError(String fieldName) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    updatedErrors.remove(fieldName);
    return updatedErrors;
  }
}

/// Provider for real-time form validation
final formValidationProvider = Provider.family<FormValidationState, String>((
  ref,
  formType,
) {
  switch (formType) {
    case 'driverProfile':
      final formState = ref.watch(driverProfileFormProvider);
      return FormValidationState(
        isValid: formState.isValid,
        fieldErrors: formState.fieldErrors,
        generalError: formState.errorMessage,
      );
    case 'vehicle':
      final formState = ref.watch(vehicleFormProvider);
      return FormValidationState(
        isValid: formState.isValid,
        fieldErrors: formState.fieldErrors,
        generalError: formState.errorMessage,
      );
    default:
      return const FormValidationState();
  }
});
