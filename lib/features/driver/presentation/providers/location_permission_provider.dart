import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../services/location/location_service.dart';
import '../../../../core/di/service_locator.dart';

/// Provider for location permission status
final locationPermissionProvider = FutureProvider<LocationPermission>((
  ref,
) async {
  final locationService = getIt<LocationService>();
  return await locationService.checkLocationPermission();
});

/// Provider for requesting location permission
final requestLocationPermissionProvider = FutureProvider.family<bool, void>((
  ref,
  _,
) async {
  final locationService = getIt<LocationService>();
  return await locationService.requestLocationPermission();
});

/// Provider for checking if location services are enabled
final locationServiceEnabledProvider = FutureProvider<bool>((ref) async {
  final locationService = getIt<LocationService>();
  return await locationService.isLocationServiceEnabled();
});

/// Provider that combines permission and service status
final locationAvailabilityProvider = FutureProvider<LocationAvailability>((
  ref,
) async {
  final locationService = getIt<LocationService>();

  final permission = await locationService.checkLocationPermission();
  final serviceEnabled = await locationService.isLocationServiceEnabled();

  return LocationAvailability(
    permission: permission,
    serviceEnabled: serviceEnabled,
    isAvailable:
        serviceEnabled &&
        (permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always),
  );
});

/// Data class for location availability status
class LocationAvailability {
  final LocationPermission permission;
  final bool serviceEnabled;
  final bool isAvailable;

  const LocationAvailability({
    required this.permission,
    required this.serviceEnabled,
    required this.isAvailable,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationAvailability &&
          runtimeType == other.runtimeType &&
          permission == other.permission &&
          serviceEnabled == other.serviceEnabled &&
          isAvailable == other.isAvailable;

  @override
  int get hashCode =>
      permission.hashCode ^ serviceEnabled.hashCode ^ isAvailable.hashCode;

  @override
  String toString() {
    return 'LocationAvailability{permission: $permission, serviceEnabled: $serviceEnabled, isAvailable: $isAvailable}';
  }
}
