// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'earnings_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$EarningsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )
    loaded,
    required TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )
    error,
    required TResult Function(
      EarningsInfo currentEarnings,
      DriverStats currentStats,
    )
    refreshing,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult? Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult? Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EarningsInitial value) initial,
    required TResult Function(EarningsLoading value) loading,
    required TResult Function(EarningsLoaded value) loaded,
    required TResult Function(EarningsError value) error,
    required TResult Function(EarningsRefreshing value) refreshing,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EarningsInitial value)? initial,
    TResult? Function(EarningsLoading value)? loading,
    TResult? Function(EarningsLoaded value)? loaded,
    TResult? Function(EarningsError value)? error,
    TResult? Function(EarningsRefreshing value)? refreshing,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EarningsInitial value)? initial,
    TResult Function(EarningsLoading value)? loading,
    TResult Function(EarningsLoaded value)? loaded,
    TResult Function(EarningsError value)? error,
    TResult Function(EarningsRefreshing value)? refreshing,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EarningsStateCopyWith<$Res> {
  factory $EarningsStateCopyWith(
    EarningsState value,
    $Res Function(EarningsState) then,
  ) = _$EarningsStateCopyWithImpl<$Res, EarningsState>;
}

/// @nodoc
class _$EarningsStateCopyWithImpl<$Res, $Val extends EarningsState>
    implements $EarningsStateCopyWith<$Res> {
  _$EarningsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$EarningsInitialImplCopyWith<$Res> {
  factory _$$EarningsInitialImplCopyWith(
    _$EarningsInitialImpl value,
    $Res Function(_$EarningsInitialImpl) then,
  ) = __$$EarningsInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EarningsInitialImplCopyWithImpl<$Res>
    extends _$EarningsStateCopyWithImpl<$Res, _$EarningsInitialImpl>
    implements _$$EarningsInitialImplCopyWith<$Res> {
  __$$EarningsInitialImplCopyWithImpl(
    _$EarningsInitialImpl _value,
    $Res Function(_$EarningsInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EarningsInitialImpl implements EarningsInitial {
  const _$EarningsInitialImpl();

  @override
  String toString() {
    return 'EarningsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EarningsInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )
    loaded,
    required TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )
    error,
    required TResult Function(
      EarningsInfo currentEarnings,
      DriverStats currentStats,
    )
    refreshing,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult? Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult? Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EarningsInitial value) initial,
    required TResult Function(EarningsLoading value) loading,
    required TResult Function(EarningsLoaded value) loaded,
    required TResult Function(EarningsError value) error,
    required TResult Function(EarningsRefreshing value) refreshing,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EarningsInitial value)? initial,
    TResult? Function(EarningsLoading value)? loading,
    TResult? Function(EarningsLoaded value)? loaded,
    TResult? Function(EarningsError value)? error,
    TResult? Function(EarningsRefreshing value)? refreshing,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EarningsInitial value)? initial,
    TResult Function(EarningsLoading value)? loading,
    TResult Function(EarningsLoaded value)? loaded,
    TResult Function(EarningsError value)? error,
    TResult Function(EarningsRefreshing value)? refreshing,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class EarningsInitial implements EarningsState {
  const factory EarningsInitial() = _$EarningsInitialImpl;
}

/// @nodoc
abstract class _$$EarningsLoadingImplCopyWith<$Res> {
  factory _$$EarningsLoadingImplCopyWith(
    _$EarningsLoadingImpl value,
    $Res Function(_$EarningsLoadingImpl) then,
  ) = __$$EarningsLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EarningsLoadingImplCopyWithImpl<$Res>
    extends _$EarningsStateCopyWithImpl<$Res, _$EarningsLoadingImpl>
    implements _$$EarningsLoadingImplCopyWith<$Res> {
  __$$EarningsLoadingImplCopyWithImpl(
    _$EarningsLoadingImpl _value,
    $Res Function(_$EarningsLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EarningsLoadingImpl implements EarningsLoading {
  const _$EarningsLoadingImpl();

  @override
  String toString() {
    return 'EarningsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EarningsLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )
    loaded,
    required TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )
    error,
    required TResult Function(
      EarningsInfo currentEarnings,
      DriverStats currentStats,
    )
    refreshing,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult? Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult? Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EarningsInitial value) initial,
    required TResult Function(EarningsLoading value) loading,
    required TResult Function(EarningsLoaded value) loaded,
    required TResult Function(EarningsError value) error,
    required TResult Function(EarningsRefreshing value) refreshing,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EarningsInitial value)? initial,
    TResult? Function(EarningsLoading value)? loading,
    TResult? Function(EarningsLoaded value)? loaded,
    TResult? Function(EarningsError value)? error,
    TResult? Function(EarningsRefreshing value)? refreshing,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EarningsInitial value)? initial,
    TResult Function(EarningsLoading value)? loading,
    TResult Function(EarningsLoaded value)? loaded,
    TResult Function(EarningsError value)? error,
    TResult Function(EarningsRefreshing value)? refreshing,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class EarningsLoading implements EarningsState {
  const factory EarningsLoading() = _$EarningsLoadingImpl;
}

/// @nodoc
abstract class _$$EarningsLoadedImplCopyWith<$Res> {
  factory _$$EarningsLoadedImplCopyWith(
    _$EarningsLoadedImpl value,
    $Res Function(_$EarningsLoadedImpl) then,
  ) = __$$EarningsLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({EarningsInfo earnings, DriverStats stats, DateTime? lastUpdated});

  $EarningsInfoCopyWith<$Res> get earnings;
  $DriverStatsCopyWith<$Res> get stats;
}

/// @nodoc
class __$$EarningsLoadedImplCopyWithImpl<$Res>
    extends _$EarningsStateCopyWithImpl<$Res, _$EarningsLoadedImpl>
    implements _$$EarningsLoadedImplCopyWith<$Res> {
  __$$EarningsLoadedImplCopyWithImpl(
    _$EarningsLoadedImpl _value,
    $Res Function(_$EarningsLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? earnings = null,
    Object? stats = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _$EarningsLoadedImpl(
        earnings: null == earnings
            ? _value.earnings
            : earnings // ignore: cast_nullable_to_non_nullable
                  as EarningsInfo,
        stats: null == stats
            ? _value.stats
            : stats // ignore: cast_nullable_to_non_nullable
                  as DriverStats,
        lastUpdated: freezed == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EarningsInfoCopyWith<$Res> get earnings {
    return $EarningsInfoCopyWith<$Res>(_value.earnings, (value) {
      return _then(_value.copyWith(earnings: value));
    });
  }

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DriverStatsCopyWith<$Res> get stats {
    return $DriverStatsCopyWith<$Res>(_value.stats, (value) {
      return _then(_value.copyWith(stats: value));
    });
  }
}

/// @nodoc

class _$EarningsLoadedImpl implements EarningsLoaded {
  const _$EarningsLoadedImpl({
    required this.earnings,
    required this.stats,
    this.lastUpdated,
  });

  @override
  final EarningsInfo earnings;
  @override
  final DriverStats stats;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'EarningsState.loaded(earnings: $earnings, stats: $stats, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EarningsLoadedImpl &&
            (identical(other.earnings, earnings) ||
                other.earnings == earnings) &&
            (identical(other.stats, stats) || other.stats == stats) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @override
  int get hashCode => Object.hash(runtimeType, earnings, stats, lastUpdated);

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EarningsLoadedImplCopyWith<_$EarningsLoadedImpl> get copyWith =>
      __$$EarningsLoadedImplCopyWithImpl<_$EarningsLoadedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )
    loaded,
    required TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )
    error,
    required TResult Function(
      EarningsInfo currentEarnings,
      DriverStats currentStats,
    )
    refreshing,
  }) {
    return loaded(earnings, stats, lastUpdated);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult? Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult? Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
  }) {
    return loaded?.call(earnings, stats, lastUpdated);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(earnings, stats, lastUpdated);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EarningsInitial value) initial,
    required TResult Function(EarningsLoading value) loading,
    required TResult Function(EarningsLoaded value) loaded,
    required TResult Function(EarningsError value) error,
    required TResult Function(EarningsRefreshing value) refreshing,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EarningsInitial value)? initial,
    TResult? Function(EarningsLoading value)? loading,
    TResult? Function(EarningsLoaded value)? loaded,
    TResult? Function(EarningsError value)? error,
    TResult? Function(EarningsRefreshing value)? refreshing,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EarningsInitial value)? initial,
    TResult Function(EarningsLoading value)? loading,
    TResult Function(EarningsLoaded value)? loaded,
    TResult Function(EarningsError value)? error,
    TResult Function(EarningsRefreshing value)? refreshing,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class EarningsLoaded implements EarningsState {
  const factory EarningsLoaded({
    required final EarningsInfo earnings,
    required final DriverStats stats,
    final DateTime? lastUpdated,
  }) = _$EarningsLoadedImpl;

  EarningsInfo get earnings;
  DriverStats get stats;
  DateTime? get lastUpdated;

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EarningsLoadedImplCopyWith<_$EarningsLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EarningsErrorImplCopyWith<$Res> {
  factory _$$EarningsErrorImplCopyWith(
    _$EarningsErrorImpl value,
    $Res Function(_$EarningsErrorImpl) then,
  ) = __$$EarningsErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    String message,
    EarningsInfo? cachedEarnings,
    DriverStats? cachedStats,
  });

  $EarningsInfoCopyWith<$Res>? get cachedEarnings;
  $DriverStatsCopyWith<$Res>? get cachedStats;
}

/// @nodoc
class __$$EarningsErrorImplCopyWithImpl<$Res>
    extends _$EarningsStateCopyWithImpl<$Res, _$EarningsErrorImpl>
    implements _$$EarningsErrorImplCopyWith<$Res> {
  __$$EarningsErrorImplCopyWithImpl(
    _$EarningsErrorImpl _value,
    $Res Function(_$EarningsErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? cachedEarnings = freezed,
    Object? cachedStats = freezed,
  }) {
    return _then(
      _$EarningsErrorImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        cachedEarnings: freezed == cachedEarnings
            ? _value.cachedEarnings
            : cachedEarnings // ignore: cast_nullable_to_non_nullable
                  as EarningsInfo?,
        cachedStats: freezed == cachedStats
            ? _value.cachedStats
            : cachedStats // ignore: cast_nullable_to_non_nullable
                  as DriverStats?,
      ),
    );
  }

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EarningsInfoCopyWith<$Res>? get cachedEarnings {
    if (_value.cachedEarnings == null) {
      return null;
    }

    return $EarningsInfoCopyWith<$Res>(_value.cachedEarnings!, (value) {
      return _then(_value.copyWith(cachedEarnings: value));
    });
  }

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DriverStatsCopyWith<$Res>? get cachedStats {
    if (_value.cachedStats == null) {
      return null;
    }

    return $DriverStatsCopyWith<$Res>(_value.cachedStats!, (value) {
      return _then(_value.copyWith(cachedStats: value));
    });
  }
}

/// @nodoc

class _$EarningsErrorImpl implements EarningsError {
  const _$EarningsErrorImpl({
    required this.message,
    this.cachedEarnings,
    this.cachedStats,
  });

  @override
  final String message;
  @override
  final EarningsInfo? cachedEarnings;
  @override
  final DriverStats? cachedStats;

  @override
  String toString() {
    return 'EarningsState.error(message: $message, cachedEarnings: $cachedEarnings, cachedStats: $cachedStats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EarningsErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.cachedEarnings, cachedEarnings) ||
                other.cachedEarnings == cachedEarnings) &&
            (identical(other.cachedStats, cachedStats) ||
                other.cachedStats == cachedStats));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, message, cachedEarnings, cachedStats);

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EarningsErrorImplCopyWith<_$EarningsErrorImpl> get copyWith =>
      __$$EarningsErrorImplCopyWithImpl<_$EarningsErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )
    loaded,
    required TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )
    error,
    required TResult Function(
      EarningsInfo currentEarnings,
      DriverStats currentStats,
    )
    refreshing,
  }) {
    return error(message, cachedEarnings, cachedStats);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult? Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult? Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
  }) {
    return error?.call(message, cachedEarnings, cachedStats);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, cachedEarnings, cachedStats);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EarningsInitial value) initial,
    required TResult Function(EarningsLoading value) loading,
    required TResult Function(EarningsLoaded value) loaded,
    required TResult Function(EarningsError value) error,
    required TResult Function(EarningsRefreshing value) refreshing,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EarningsInitial value)? initial,
    TResult? Function(EarningsLoading value)? loading,
    TResult? Function(EarningsLoaded value)? loaded,
    TResult? Function(EarningsError value)? error,
    TResult? Function(EarningsRefreshing value)? refreshing,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EarningsInitial value)? initial,
    TResult Function(EarningsLoading value)? loading,
    TResult Function(EarningsLoaded value)? loaded,
    TResult Function(EarningsError value)? error,
    TResult Function(EarningsRefreshing value)? refreshing,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class EarningsError implements EarningsState {
  const factory EarningsError({
    required final String message,
    final EarningsInfo? cachedEarnings,
    final DriverStats? cachedStats,
  }) = _$EarningsErrorImpl;

  String get message;
  EarningsInfo? get cachedEarnings;
  DriverStats? get cachedStats;

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EarningsErrorImplCopyWith<_$EarningsErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EarningsRefreshingImplCopyWith<$Res> {
  factory _$$EarningsRefreshingImplCopyWith(
    _$EarningsRefreshingImpl value,
    $Res Function(_$EarningsRefreshingImpl) then,
  ) = __$$EarningsRefreshingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({EarningsInfo currentEarnings, DriverStats currentStats});

  $EarningsInfoCopyWith<$Res> get currentEarnings;
  $DriverStatsCopyWith<$Res> get currentStats;
}

/// @nodoc
class __$$EarningsRefreshingImplCopyWithImpl<$Res>
    extends _$EarningsStateCopyWithImpl<$Res, _$EarningsRefreshingImpl>
    implements _$$EarningsRefreshingImplCopyWith<$Res> {
  __$$EarningsRefreshingImplCopyWithImpl(
    _$EarningsRefreshingImpl _value,
    $Res Function(_$EarningsRefreshingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? currentEarnings = null, Object? currentStats = null}) {
    return _then(
      _$EarningsRefreshingImpl(
        currentEarnings: null == currentEarnings
            ? _value.currentEarnings
            : currentEarnings // ignore: cast_nullable_to_non_nullable
                  as EarningsInfo,
        currentStats: null == currentStats
            ? _value.currentStats
            : currentStats // ignore: cast_nullable_to_non_nullable
                  as DriverStats,
      ),
    );
  }

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EarningsInfoCopyWith<$Res> get currentEarnings {
    return $EarningsInfoCopyWith<$Res>(_value.currentEarnings, (value) {
      return _then(_value.copyWith(currentEarnings: value));
    });
  }

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DriverStatsCopyWith<$Res> get currentStats {
    return $DriverStatsCopyWith<$Res>(_value.currentStats, (value) {
      return _then(_value.copyWith(currentStats: value));
    });
  }
}

/// @nodoc

class _$EarningsRefreshingImpl implements EarningsRefreshing {
  const _$EarningsRefreshingImpl({
    required this.currentEarnings,
    required this.currentStats,
  });

  @override
  final EarningsInfo currentEarnings;
  @override
  final DriverStats currentStats;

  @override
  String toString() {
    return 'EarningsState.refreshing(currentEarnings: $currentEarnings, currentStats: $currentStats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EarningsRefreshingImpl &&
            (identical(other.currentEarnings, currentEarnings) ||
                other.currentEarnings == currentEarnings) &&
            (identical(other.currentStats, currentStats) ||
                other.currentStats == currentStats));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currentEarnings, currentStats);

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EarningsRefreshingImplCopyWith<_$EarningsRefreshingImpl> get copyWith =>
      __$$EarningsRefreshingImplCopyWithImpl<_$EarningsRefreshingImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )
    loaded,
    required TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )
    error,
    required TResult Function(
      EarningsInfo currentEarnings,
      DriverStats currentStats,
    )
    refreshing,
  }) {
    return refreshing(currentEarnings, currentStats);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult? Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult? Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
  }) {
    return refreshing?.call(currentEarnings, currentStats);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      EarningsInfo earnings,
      DriverStats stats,
      DateTime? lastUpdated,
    )?
    loaded,
    TResult Function(
      String message,
      EarningsInfo? cachedEarnings,
      DriverStats? cachedStats,
    )?
    error,
    TResult Function(EarningsInfo currentEarnings, DriverStats currentStats)?
    refreshing,
    required TResult orElse(),
  }) {
    if (refreshing != null) {
      return refreshing(currentEarnings, currentStats);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EarningsInitial value) initial,
    required TResult Function(EarningsLoading value) loading,
    required TResult Function(EarningsLoaded value) loaded,
    required TResult Function(EarningsError value) error,
    required TResult Function(EarningsRefreshing value) refreshing,
  }) {
    return refreshing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EarningsInitial value)? initial,
    TResult? Function(EarningsLoading value)? loading,
    TResult? Function(EarningsLoaded value)? loaded,
    TResult? Function(EarningsError value)? error,
    TResult? Function(EarningsRefreshing value)? refreshing,
  }) {
    return refreshing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EarningsInitial value)? initial,
    TResult Function(EarningsLoading value)? loading,
    TResult Function(EarningsLoaded value)? loaded,
    TResult Function(EarningsError value)? error,
    TResult Function(EarningsRefreshing value)? refreshing,
    required TResult orElse(),
  }) {
    if (refreshing != null) {
      return refreshing(this);
    }
    return orElse();
  }
}

abstract class EarningsRefreshing implements EarningsState {
  const factory EarningsRefreshing({
    required final EarningsInfo currentEarnings,
    required final DriverStats currentStats,
  }) = _$EarningsRefreshingImpl;

  EarningsInfo get currentEarnings;
  DriverStats get currentStats;

  /// Create a copy of EarningsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EarningsRefreshingImplCopyWith<_$EarningsRefreshingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
