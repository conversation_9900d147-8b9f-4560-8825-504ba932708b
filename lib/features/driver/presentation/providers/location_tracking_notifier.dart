import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../services/location/location_service.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/errors/app_error.dart';

part 'location_tracking_notifier.freezed.dart';

/// State for location tracking
@freezed
class LocationTrackingState with _$LocationTrackingState {
  const factory LocationTrackingState.idle() = LocationTrackingIdle;
  const factory LocationTrackingState.requestingPermission() =
      LocationTrackingRequestingPermission;
  const factory LocationTrackingState.starting() = LocationTrackingStarting;
  const factory LocationTrackingState.active({
    required Position currentPosition,
    required DateTime startedAt,
  }) = LocationTrackingActive;
  const factory LocationTrackingState.stopping() = LocationTrackingStopping;
  const factory LocationTrackingState.error({required AppError error}) =
      LocationTrackingError;
}

/// Notifier for managing location tracking state
class LocationTrackingNotifier extends StateNotifier<LocationTrackingState> {
  final LocationService _locationService;

  LocationTrackingNotifier(this._locationService)
    : super(const LocationTrackingState.idle());

  /// Start location tracking
  Future<void> startTracking() async {
    if (state is LocationTrackingActive) {
      return; // Already tracking
    }

    try {
      state = const LocationTrackingState.requestingPermission();

      // Request permission first
      final hasPermission = await _locationService.requestLocationPermission();
      if (!hasPermission) {
        state = const LocationTrackingState.error(
          error: AppError.location(
            message: 'Location permission is required to track your location',
            errorType: LocationErrorType.permissionDenied,
          ),
        );
        return;
      }

      // Check if location services are enabled
      final serviceEnabled = await _locationService.isLocationServiceEnabled();
      if (!serviceEnabled) {
        state = const LocationTrackingState.error(
          error: AppError.location(
            message: 'Please enable location services in your device settings',
            errorType: LocationErrorType.serviceDisabled,
          ),
        );
        return;
      }

      state = const LocationTrackingState.starting();

      // Get initial position
      final initialPosition = await _locationService.getCurrentPosition();

      // Start tracking
      await _locationService.startLocationTracking();

      state = LocationTrackingState.active(
        currentPosition: initialPosition,
        startedAt: DateTime.now(),
      );
    } catch (e) {
      AppError error;
      if (e is AppError) {
        error = e;
      } else {
        error = AppError.location(
          message: 'Failed to start location tracking: ${e.toString()}',
          errorType: LocationErrorType.unavailable,
        );
      }

      state = LocationTrackingState.error(error: error);
    }
  }

  /// Stop location tracking
  Future<void> stopTracking() async {
    if (state is! LocationTrackingActive) {
      return; // Not tracking
    }

    try {
      state = const LocationTrackingState.stopping();
      await _locationService.stopLocationTracking();
      state = const LocationTrackingState.idle();
    } catch (e) {
      state = LocationTrackingState.error(
        error: AppError.location(
          message: 'Failed to stop location tracking: ${e.toString()}',
          errorType: LocationErrorType.unavailable,
        ),
      );
    }
  }

  /// Update current position (called by location stream)
  void updatePosition(Position position) {
    state.whenOrNull(
      active: (currentPosition, startedAt) {
        state = LocationTrackingState.active(
          currentPosition: position,
          startedAt: startedAt,
        );
      },
    );
  }

  /// Clear error state
  void clearError() {
    if (state is LocationTrackingError) {
      state = const LocationTrackingState.idle();
    }
  }

  /// Check if currently tracking
  bool get isTracking => state is LocationTrackingActive;

  /// Get current position if tracking
  Position? get currentPosition {
    return state.whenOrNull(active: (position, startedAt) => position);
  }

  /// Get tracking duration if active
  Duration? get trackingDuration {
    return state.whenOrNull(
      active: (position, startedAt) => DateTime.now().difference(startedAt),
    );
  }
}

/// Provider for location tracking notifier
final locationTrackingProvider =
    StateNotifierProvider<LocationTrackingNotifier, LocationTrackingState>((
      ref,
    ) {
      final locationService = getIt<LocationService>();
      return LocationTrackingNotifier(locationService);
    });

/// Provider for checking if location tracking is active
final isLocationTrackingProvider = Provider<bool>((ref) {
  final state = ref.watch(locationTrackingProvider);
  return state is LocationTrackingActive;
});

/// Provider for current tracked position
final currentTrackedPositionProvider = Provider<Position?>((ref) {
  final state = ref.watch(locationTrackingProvider);
  return state.whenOrNull(active: (position, startedAt) => position);
});

/// Provider for tracking duration
final trackingDurationProvider = Provider<Duration?>((ref) {
  final state = ref.watch(locationTrackingProvider);
  return state.whenOrNull(
    active: (position, startedAt) => DateTime.now().difference(startedAt),
  );
});
