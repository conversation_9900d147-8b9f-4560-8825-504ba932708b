import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/driver/driver_service.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';
import 'driver_profile_state.dart';

/// Notifier for managing vehicle information state
class VehicleInfoNotifier extends StateNotifier<VehicleInfoState> {
  final DriverService _driverService;

  VehicleInfoNotifier(this._driverService)
    : super(const VehicleInfoState.initial());

  /// Load vehicle information from API
  Future<void> loadVehicleInfo() async {
    state = const VehicleInfoState.loading();

    try {
      final vehicleInfo = await _driverService.getVehicleInfo();
      state = VehicleInfoState.loaded(vehicleInfo: vehicleInfo);
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = VehicleInfoState.error(message: errorMessage);
    }
  }

  /// Add new vehicle information
  Future<void> addVehicleInfo(VehicleInfoCreate vehicleData) async {
    state = const VehicleInfoState.loading();

    try {
      final vehicleInfo = await _driverService.addVehicleInfo(vehicleData);
      state = VehicleInfoState.loaded(vehicleInfo: vehicleInfo);
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = VehicleInfoState.error(message: errorMessage);
    }
  }

  /// Update existing vehicle information
  Future<void> updateVehicleInfo(VehicleInfoUpdate vehicleData) async {
    state = const VehicleInfoState.loading();

    try {
      final vehicleInfo = await _driverService.updateVehicleInfo(vehicleData);
      state = VehicleInfoState.loaded(vehicleInfo: vehicleInfo);
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = VehicleInfoState.error(message: errorMessage);
    }
  }

  /// Reset state to initial
  void reset() {
    state = const VehicleInfoState.initial();
  }

  String _getErrorMessage(dynamic error) {
    if (error is AppError) {
      return error.when(
        network: (message, details) => message,
        authentication: (message, errorCode) => message,
        validation: (message, fieldErrors) => message,
        location: (message, errorType) => message,
        document: (message, errorType) => message,
        server: (message, statusCode) => message,
        unknown: (message, exception) => message,
      );
    }
    return 'An unexpected error occurred';
  }
}
