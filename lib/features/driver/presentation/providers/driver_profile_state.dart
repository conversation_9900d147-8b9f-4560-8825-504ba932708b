import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/models.dart';

part 'driver_profile_state.freezed.dart';

/// State for driver profile management
@freezed
class DriverProfileState with _$DriverProfileState {
  const factory DriverProfileState.initial() = DriverProfileInitial;

  const factory DriverProfileState.loading() = DriverProfileLoading;

  const factory DriverProfileState.loaded({required DriverProfile profile}) =
      DriverProfileLoaded;

  const factory DriverProfileState.error({required String message}) =
      DriverProfileError;
}

/// State for vehicle information management
@freezed
class VehicleInfoState with _$VehicleInfoState {
  const factory VehicleInfoState.initial() = VehicleInfoInitial;

  const factory VehicleInfoState.loading() = VehicleInfoLoading;

  const factory VehicleInfoState.loaded({required VehicleInfo vehicleInfo}) =
      VehicleInfoLoaded;

  const factory VehicleInfoState.error({required String message}) =
      VehicleInfoError;
}

/// State for driver availability management
@freezed
class DriverAvailabilityState with _$DriverAvailabilityState {
  const factory DriverAvailabilityState.offline() = DriverAvailabilityOffline;

  const factory DriverAvailabilityState.goingOnline() =
      DriverAvailabilityGoingOnline;

  const factory DriverAvailabilityState.online({
    required LocationInfo location,
  }) = DriverAvailabilityOnline;

  const factory DriverAvailabilityState.goingOffline() =
      DriverAvailabilityGoingOffline;

  const factory DriverAvailabilityState.error({required String message}) =
      DriverAvailabilityError;
}
