// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_tracking_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$LocationTrackingState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() requestingPermission,
    required TResult Function() starting,
    required TResult Function(Position currentPosition, DateTime startedAt)
    active,
    required TResult Function() stopping,
    required TResult Function(AppError error) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? requestingPermission,
    TResult? Function()? starting,
    TResult? Function(Position currentPosition, DateTime startedAt)? active,
    TResult? Function()? stopping,
    TResult? Function(AppError error)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? requestingPermission,
    TResult Function()? starting,
    TResult Function(Position currentPosition, DateTime startedAt)? active,
    TResult Function()? stopping,
    TResult Function(AppError error)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LocationTrackingIdle value) idle,
    required TResult Function(LocationTrackingRequestingPermission value)
    requestingPermission,
    required TResult Function(LocationTrackingStarting value) starting,
    required TResult Function(LocationTrackingActive value) active,
    required TResult Function(LocationTrackingStopping value) stopping,
    required TResult Function(LocationTrackingError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LocationTrackingIdle value)? idle,
    TResult? Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult? Function(LocationTrackingStarting value)? starting,
    TResult? Function(LocationTrackingActive value)? active,
    TResult? Function(LocationTrackingStopping value)? stopping,
    TResult? Function(LocationTrackingError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LocationTrackingIdle value)? idle,
    TResult Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult Function(LocationTrackingStarting value)? starting,
    TResult Function(LocationTrackingActive value)? active,
    TResult Function(LocationTrackingStopping value)? stopping,
    TResult Function(LocationTrackingError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationTrackingStateCopyWith<$Res> {
  factory $LocationTrackingStateCopyWith(
    LocationTrackingState value,
    $Res Function(LocationTrackingState) then,
  ) = _$LocationTrackingStateCopyWithImpl<$Res, LocationTrackingState>;
}

/// @nodoc
class _$LocationTrackingStateCopyWithImpl<
  $Res,
  $Val extends LocationTrackingState
>
    implements $LocationTrackingStateCopyWith<$Res> {
  _$LocationTrackingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LocationTrackingIdleImplCopyWith<$Res> {
  factory _$$LocationTrackingIdleImplCopyWith(
    _$LocationTrackingIdleImpl value,
    $Res Function(_$LocationTrackingIdleImpl) then,
  ) = __$$LocationTrackingIdleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LocationTrackingIdleImplCopyWithImpl<$Res>
    extends
        _$LocationTrackingStateCopyWithImpl<$Res, _$LocationTrackingIdleImpl>
    implements _$$LocationTrackingIdleImplCopyWith<$Res> {
  __$$LocationTrackingIdleImplCopyWithImpl(
    _$LocationTrackingIdleImpl _value,
    $Res Function(_$LocationTrackingIdleImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LocationTrackingIdleImpl implements LocationTrackingIdle {
  const _$LocationTrackingIdleImpl();

  @override
  String toString() {
    return 'LocationTrackingState.idle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationTrackingIdleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() requestingPermission,
    required TResult Function() starting,
    required TResult Function(Position currentPosition, DateTime startedAt)
    active,
    required TResult Function() stopping,
    required TResult Function(AppError error) error,
  }) {
    return idle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? requestingPermission,
    TResult? Function()? starting,
    TResult? Function(Position currentPosition, DateTime startedAt)? active,
    TResult? Function()? stopping,
    TResult? Function(AppError error)? error,
  }) {
    return idle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? requestingPermission,
    TResult Function()? starting,
    TResult Function(Position currentPosition, DateTime startedAt)? active,
    TResult Function()? stopping,
    TResult Function(AppError error)? error,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LocationTrackingIdle value) idle,
    required TResult Function(LocationTrackingRequestingPermission value)
    requestingPermission,
    required TResult Function(LocationTrackingStarting value) starting,
    required TResult Function(LocationTrackingActive value) active,
    required TResult Function(LocationTrackingStopping value) stopping,
    required TResult Function(LocationTrackingError value) error,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LocationTrackingIdle value)? idle,
    TResult? Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult? Function(LocationTrackingStarting value)? starting,
    TResult? Function(LocationTrackingActive value)? active,
    TResult? Function(LocationTrackingStopping value)? stopping,
    TResult? Function(LocationTrackingError value)? error,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LocationTrackingIdle value)? idle,
    TResult Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult Function(LocationTrackingStarting value)? starting,
    TResult Function(LocationTrackingActive value)? active,
    TResult Function(LocationTrackingStopping value)? stopping,
    TResult Function(LocationTrackingError value)? error,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class LocationTrackingIdle implements LocationTrackingState {
  const factory LocationTrackingIdle() = _$LocationTrackingIdleImpl;
}

/// @nodoc
abstract class _$$LocationTrackingRequestingPermissionImplCopyWith<$Res> {
  factory _$$LocationTrackingRequestingPermissionImplCopyWith(
    _$LocationTrackingRequestingPermissionImpl value,
    $Res Function(_$LocationTrackingRequestingPermissionImpl) then,
  ) = __$$LocationTrackingRequestingPermissionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LocationTrackingRequestingPermissionImplCopyWithImpl<$Res>
    extends
        _$LocationTrackingStateCopyWithImpl<
          $Res,
          _$LocationTrackingRequestingPermissionImpl
        >
    implements _$$LocationTrackingRequestingPermissionImplCopyWith<$Res> {
  __$$LocationTrackingRequestingPermissionImplCopyWithImpl(
    _$LocationTrackingRequestingPermissionImpl _value,
    $Res Function(_$LocationTrackingRequestingPermissionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LocationTrackingRequestingPermissionImpl
    implements LocationTrackingRequestingPermission {
  const _$LocationTrackingRequestingPermissionImpl();

  @override
  String toString() {
    return 'LocationTrackingState.requestingPermission()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationTrackingRequestingPermissionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() requestingPermission,
    required TResult Function() starting,
    required TResult Function(Position currentPosition, DateTime startedAt)
    active,
    required TResult Function() stopping,
    required TResult Function(AppError error) error,
  }) {
    return requestingPermission();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? requestingPermission,
    TResult? Function()? starting,
    TResult? Function(Position currentPosition, DateTime startedAt)? active,
    TResult? Function()? stopping,
    TResult? Function(AppError error)? error,
  }) {
    return requestingPermission?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? requestingPermission,
    TResult Function()? starting,
    TResult Function(Position currentPosition, DateTime startedAt)? active,
    TResult Function()? stopping,
    TResult Function(AppError error)? error,
    required TResult orElse(),
  }) {
    if (requestingPermission != null) {
      return requestingPermission();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LocationTrackingIdle value) idle,
    required TResult Function(LocationTrackingRequestingPermission value)
    requestingPermission,
    required TResult Function(LocationTrackingStarting value) starting,
    required TResult Function(LocationTrackingActive value) active,
    required TResult Function(LocationTrackingStopping value) stopping,
    required TResult Function(LocationTrackingError value) error,
  }) {
    return requestingPermission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LocationTrackingIdle value)? idle,
    TResult? Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult? Function(LocationTrackingStarting value)? starting,
    TResult? Function(LocationTrackingActive value)? active,
    TResult? Function(LocationTrackingStopping value)? stopping,
    TResult? Function(LocationTrackingError value)? error,
  }) {
    return requestingPermission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LocationTrackingIdle value)? idle,
    TResult Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult Function(LocationTrackingStarting value)? starting,
    TResult Function(LocationTrackingActive value)? active,
    TResult Function(LocationTrackingStopping value)? stopping,
    TResult Function(LocationTrackingError value)? error,
    required TResult orElse(),
  }) {
    if (requestingPermission != null) {
      return requestingPermission(this);
    }
    return orElse();
  }
}

abstract class LocationTrackingRequestingPermission
    implements LocationTrackingState {
  const factory LocationTrackingRequestingPermission() =
      _$LocationTrackingRequestingPermissionImpl;
}

/// @nodoc
abstract class _$$LocationTrackingStartingImplCopyWith<$Res> {
  factory _$$LocationTrackingStartingImplCopyWith(
    _$LocationTrackingStartingImpl value,
    $Res Function(_$LocationTrackingStartingImpl) then,
  ) = __$$LocationTrackingStartingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LocationTrackingStartingImplCopyWithImpl<$Res>
    extends
        _$LocationTrackingStateCopyWithImpl<
          $Res,
          _$LocationTrackingStartingImpl
        >
    implements _$$LocationTrackingStartingImplCopyWith<$Res> {
  __$$LocationTrackingStartingImplCopyWithImpl(
    _$LocationTrackingStartingImpl _value,
    $Res Function(_$LocationTrackingStartingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LocationTrackingStartingImpl implements LocationTrackingStarting {
  const _$LocationTrackingStartingImpl();

  @override
  String toString() {
    return 'LocationTrackingState.starting()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationTrackingStartingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() requestingPermission,
    required TResult Function() starting,
    required TResult Function(Position currentPosition, DateTime startedAt)
    active,
    required TResult Function() stopping,
    required TResult Function(AppError error) error,
  }) {
    return starting();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? requestingPermission,
    TResult? Function()? starting,
    TResult? Function(Position currentPosition, DateTime startedAt)? active,
    TResult? Function()? stopping,
    TResult? Function(AppError error)? error,
  }) {
    return starting?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? requestingPermission,
    TResult Function()? starting,
    TResult Function(Position currentPosition, DateTime startedAt)? active,
    TResult Function()? stopping,
    TResult Function(AppError error)? error,
    required TResult orElse(),
  }) {
    if (starting != null) {
      return starting();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LocationTrackingIdle value) idle,
    required TResult Function(LocationTrackingRequestingPermission value)
    requestingPermission,
    required TResult Function(LocationTrackingStarting value) starting,
    required TResult Function(LocationTrackingActive value) active,
    required TResult Function(LocationTrackingStopping value) stopping,
    required TResult Function(LocationTrackingError value) error,
  }) {
    return starting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LocationTrackingIdle value)? idle,
    TResult? Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult? Function(LocationTrackingStarting value)? starting,
    TResult? Function(LocationTrackingActive value)? active,
    TResult? Function(LocationTrackingStopping value)? stopping,
    TResult? Function(LocationTrackingError value)? error,
  }) {
    return starting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LocationTrackingIdle value)? idle,
    TResult Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult Function(LocationTrackingStarting value)? starting,
    TResult Function(LocationTrackingActive value)? active,
    TResult Function(LocationTrackingStopping value)? stopping,
    TResult Function(LocationTrackingError value)? error,
    required TResult orElse(),
  }) {
    if (starting != null) {
      return starting(this);
    }
    return orElse();
  }
}

abstract class LocationTrackingStarting implements LocationTrackingState {
  const factory LocationTrackingStarting() = _$LocationTrackingStartingImpl;
}

/// @nodoc
abstract class _$$LocationTrackingActiveImplCopyWith<$Res> {
  factory _$$LocationTrackingActiveImplCopyWith(
    _$LocationTrackingActiveImpl value,
    $Res Function(_$LocationTrackingActiveImpl) then,
  ) = __$$LocationTrackingActiveImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Position currentPosition, DateTime startedAt});
}

/// @nodoc
class __$$LocationTrackingActiveImplCopyWithImpl<$Res>
    extends
        _$LocationTrackingStateCopyWithImpl<$Res, _$LocationTrackingActiveImpl>
    implements _$$LocationTrackingActiveImplCopyWith<$Res> {
  __$$LocationTrackingActiveImplCopyWithImpl(
    _$LocationTrackingActiveImpl _value,
    $Res Function(_$LocationTrackingActiveImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? currentPosition = null, Object? startedAt = null}) {
    return _then(
      _$LocationTrackingActiveImpl(
        currentPosition: null == currentPosition
            ? _value.currentPosition
            : currentPosition // ignore: cast_nullable_to_non_nullable
                  as Position,
        startedAt: null == startedAt
            ? _value.startedAt
            : startedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc

class _$LocationTrackingActiveImpl implements LocationTrackingActive {
  const _$LocationTrackingActiveImpl({
    required this.currentPosition,
    required this.startedAt,
  });

  @override
  final Position currentPosition;
  @override
  final DateTime startedAt;

  @override
  String toString() {
    return 'LocationTrackingState.active(currentPosition: $currentPosition, startedAt: $startedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationTrackingActiveImpl &&
            (identical(other.currentPosition, currentPosition) ||
                other.currentPosition == currentPosition) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currentPosition, startedAt);

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationTrackingActiveImplCopyWith<_$LocationTrackingActiveImpl>
  get copyWith =>
      __$$LocationTrackingActiveImplCopyWithImpl<_$LocationTrackingActiveImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() requestingPermission,
    required TResult Function() starting,
    required TResult Function(Position currentPosition, DateTime startedAt)
    active,
    required TResult Function() stopping,
    required TResult Function(AppError error) error,
  }) {
    return active(currentPosition, startedAt);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? requestingPermission,
    TResult? Function()? starting,
    TResult? Function(Position currentPosition, DateTime startedAt)? active,
    TResult? Function()? stopping,
    TResult? Function(AppError error)? error,
  }) {
    return active?.call(currentPosition, startedAt);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? requestingPermission,
    TResult Function()? starting,
    TResult Function(Position currentPosition, DateTime startedAt)? active,
    TResult Function()? stopping,
    TResult Function(AppError error)? error,
    required TResult orElse(),
  }) {
    if (active != null) {
      return active(currentPosition, startedAt);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LocationTrackingIdle value) idle,
    required TResult Function(LocationTrackingRequestingPermission value)
    requestingPermission,
    required TResult Function(LocationTrackingStarting value) starting,
    required TResult Function(LocationTrackingActive value) active,
    required TResult Function(LocationTrackingStopping value) stopping,
    required TResult Function(LocationTrackingError value) error,
  }) {
    return active(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LocationTrackingIdle value)? idle,
    TResult? Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult? Function(LocationTrackingStarting value)? starting,
    TResult? Function(LocationTrackingActive value)? active,
    TResult? Function(LocationTrackingStopping value)? stopping,
    TResult? Function(LocationTrackingError value)? error,
  }) {
    return active?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LocationTrackingIdle value)? idle,
    TResult Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult Function(LocationTrackingStarting value)? starting,
    TResult Function(LocationTrackingActive value)? active,
    TResult Function(LocationTrackingStopping value)? stopping,
    TResult Function(LocationTrackingError value)? error,
    required TResult orElse(),
  }) {
    if (active != null) {
      return active(this);
    }
    return orElse();
  }
}

abstract class LocationTrackingActive implements LocationTrackingState {
  const factory LocationTrackingActive({
    required final Position currentPosition,
    required final DateTime startedAt,
  }) = _$LocationTrackingActiveImpl;

  Position get currentPosition;
  DateTime get startedAt;

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationTrackingActiveImplCopyWith<_$LocationTrackingActiveImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LocationTrackingStoppingImplCopyWith<$Res> {
  factory _$$LocationTrackingStoppingImplCopyWith(
    _$LocationTrackingStoppingImpl value,
    $Res Function(_$LocationTrackingStoppingImpl) then,
  ) = __$$LocationTrackingStoppingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LocationTrackingStoppingImplCopyWithImpl<$Res>
    extends
        _$LocationTrackingStateCopyWithImpl<
          $Res,
          _$LocationTrackingStoppingImpl
        >
    implements _$$LocationTrackingStoppingImplCopyWith<$Res> {
  __$$LocationTrackingStoppingImplCopyWithImpl(
    _$LocationTrackingStoppingImpl _value,
    $Res Function(_$LocationTrackingStoppingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LocationTrackingStoppingImpl implements LocationTrackingStopping {
  const _$LocationTrackingStoppingImpl();

  @override
  String toString() {
    return 'LocationTrackingState.stopping()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationTrackingStoppingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() requestingPermission,
    required TResult Function() starting,
    required TResult Function(Position currentPosition, DateTime startedAt)
    active,
    required TResult Function() stopping,
    required TResult Function(AppError error) error,
  }) {
    return stopping();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? requestingPermission,
    TResult? Function()? starting,
    TResult? Function(Position currentPosition, DateTime startedAt)? active,
    TResult? Function()? stopping,
    TResult? Function(AppError error)? error,
  }) {
    return stopping?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? requestingPermission,
    TResult Function()? starting,
    TResult Function(Position currentPosition, DateTime startedAt)? active,
    TResult Function()? stopping,
    TResult Function(AppError error)? error,
    required TResult orElse(),
  }) {
    if (stopping != null) {
      return stopping();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LocationTrackingIdle value) idle,
    required TResult Function(LocationTrackingRequestingPermission value)
    requestingPermission,
    required TResult Function(LocationTrackingStarting value) starting,
    required TResult Function(LocationTrackingActive value) active,
    required TResult Function(LocationTrackingStopping value) stopping,
    required TResult Function(LocationTrackingError value) error,
  }) {
    return stopping(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LocationTrackingIdle value)? idle,
    TResult? Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult? Function(LocationTrackingStarting value)? starting,
    TResult? Function(LocationTrackingActive value)? active,
    TResult? Function(LocationTrackingStopping value)? stopping,
    TResult? Function(LocationTrackingError value)? error,
  }) {
    return stopping?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LocationTrackingIdle value)? idle,
    TResult Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult Function(LocationTrackingStarting value)? starting,
    TResult Function(LocationTrackingActive value)? active,
    TResult Function(LocationTrackingStopping value)? stopping,
    TResult Function(LocationTrackingError value)? error,
    required TResult orElse(),
  }) {
    if (stopping != null) {
      return stopping(this);
    }
    return orElse();
  }
}

abstract class LocationTrackingStopping implements LocationTrackingState {
  const factory LocationTrackingStopping() = _$LocationTrackingStoppingImpl;
}

/// @nodoc
abstract class _$$LocationTrackingErrorImplCopyWith<$Res> {
  factory _$$LocationTrackingErrorImplCopyWith(
    _$LocationTrackingErrorImpl value,
    $Res Function(_$LocationTrackingErrorImpl) then,
  ) = __$$LocationTrackingErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AppError error});

  $AppErrorCopyWith<$Res> get error;
}

/// @nodoc
class __$$LocationTrackingErrorImplCopyWithImpl<$Res>
    extends
        _$LocationTrackingStateCopyWithImpl<$Res, _$LocationTrackingErrorImpl>
    implements _$$LocationTrackingErrorImplCopyWith<$Res> {
  __$$LocationTrackingErrorImplCopyWithImpl(
    _$LocationTrackingErrorImpl _value,
    $Res Function(_$LocationTrackingErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? error = null}) {
    return _then(
      _$LocationTrackingErrorImpl(
        error: null == error
            ? _value.error
            : error // ignore: cast_nullable_to_non_nullable
                  as AppError,
      ),
    );
  }

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res> get error {
    return $AppErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$LocationTrackingErrorImpl implements LocationTrackingError {
  const _$LocationTrackingErrorImpl({required this.error});

  @override
  final AppError error;

  @override
  String toString() {
    return 'LocationTrackingState.error(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationTrackingErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationTrackingErrorImplCopyWith<_$LocationTrackingErrorImpl>
  get copyWith =>
      __$$LocationTrackingErrorImplCopyWithImpl<_$LocationTrackingErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() requestingPermission,
    required TResult Function() starting,
    required TResult Function(Position currentPosition, DateTime startedAt)
    active,
    required TResult Function() stopping,
    required TResult Function(AppError error) error,
  }) {
    return error(this.error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? requestingPermission,
    TResult? Function()? starting,
    TResult? Function(Position currentPosition, DateTime startedAt)? active,
    TResult? Function()? stopping,
    TResult? Function(AppError error)? error,
  }) {
    return error?.call(this.error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? requestingPermission,
    TResult Function()? starting,
    TResult Function(Position currentPosition, DateTime startedAt)? active,
    TResult Function()? stopping,
    TResult Function(AppError error)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this.error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LocationTrackingIdle value) idle,
    required TResult Function(LocationTrackingRequestingPermission value)
    requestingPermission,
    required TResult Function(LocationTrackingStarting value) starting,
    required TResult Function(LocationTrackingActive value) active,
    required TResult Function(LocationTrackingStopping value) stopping,
    required TResult Function(LocationTrackingError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LocationTrackingIdle value)? idle,
    TResult? Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult? Function(LocationTrackingStarting value)? starting,
    TResult? Function(LocationTrackingActive value)? active,
    TResult? Function(LocationTrackingStopping value)? stopping,
    TResult? Function(LocationTrackingError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LocationTrackingIdle value)? idle,
    TResult Function(LocationTrackingRequestingPermission value)?
    requestingPermission,
    TResult Function(LocationTrackingStarting value)? starting,
    TResult Function(LocationTrackingActive value)? active,
    TResult Function(LocationTrackingStopping value)? stopping,
    TResult Function(LocationTrackingError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class LocationTrackingError implements LocationTrackingState {
  const factory LocationTrackingError({required final AppError error}) =
      _$LocationTrackingErrorImpl;

  AppError get error;

  /// Create a copy of LocationTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationTrackingErrorImplCopyWith<_$LocationTrackingErrorImpl>
  get copyWith => throw _privateConstructorUsedError;
}
