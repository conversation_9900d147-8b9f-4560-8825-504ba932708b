import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../services/location/location_service.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/errors/app_error.dart';

/// Provider for real-time location updates stream
final locationStreamProvider = StreamProvider<Position>((ref) {
  final locationService = getIt<LocationService>();
  return locationService.getLocationStream();
});

/// Provider for current position (one-time fetch)
final currentPositionProvider = FutureProvider<Position>((ref) async {
  final locationService = getIt<LocationService>();
  return await locationService.getCurrentPosition();
});

/// Provider for last known location
final lastKnownLocationProvider = Provider<Position?>((ref) {
  final locationService = getIt<LocationService>();
  return locationService.lastKnownLocation;
});

/// Provider that watches location stream and provides the latest position
final latestLocationProvider = StateProvider<Position?>((ref) {
  // Listen to location stream and update state
  ref.listen<AsyncValue<Position>>(locationStreamProvider, (previous, next) {
    next.whenData((position) {
      ref.controller.state = position;
    });
  });

  return null;
});

/// Provider for location accuracy status
final locationAccuracyProvider = Provider<LocationAccuracyStatus>((ref) {
  final position = ref.watch(latestLocationProvider);

  if (position == null) {
    return LocationAccuracyStatus.unknown;
  }

  final accuracy = position.accuracy;

  if (accuracy <= 5) {
    return LocationAccuracyStatus.excellent;
  } else if (accuracy <= 10) {
    return LocationAccuracyStatus.good;
  } else if (accuracy <= 20) {
    return LocationAccuracyStatus.fair;
  } else {
    return LocationAccuracyStatus.poor;
  }
});

/// Provider for location error handling
final locationErrorProvider = StateProvider<AppError?>((ref) => null);

/// Enum for location accuracy status
enum LocationAccuracyStatus {
  unknown,
  excellent, // <= 5m
  good, // <= 10m
  fair, // <= 20m
  poor, // > 20m
}

extension LocationAccuracyStatusExtension on LocationAccuracyStatus {
  String get displayName {
    switch (this) {
      case LocationAccuracyStatus.unknown:
        return 'Unknown';
      case LocationAccuracyStatus.excellent:
        return 'Excellent';
      case LocationAccuracyStatus.good:
        return 'Good';
      case LocationAccuracyStatus.fair:
        return 'Fair';
      case LocationAccuracyStatus.poor:
        return 'Poor';
    }
  }

  String get description {
    switch (this) {
      case LocationAccuracyStatus.unknown:
        return 'Location accuracy unknown';
      case LocationAccuracyStatus.excellent:
        return 'Very accurate location (within 5m)';
      case LocationAccuracyStatus.good:
        return 'Good location accuracy (within 10m)';
      case LocationAccuracyStatus.fair:
        return 'Fair location accuracy (within 20m)';
      case LocationAccuracyStatus.poor:
        return 'Poor location accuracy (over 20m)';
    }
  }
}
