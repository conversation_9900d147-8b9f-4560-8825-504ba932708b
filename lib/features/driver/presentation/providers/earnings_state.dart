import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/models.dart';

part 'earnings_state.freezed.dart';

/// State class for earnings management
@freezed
class EarningsState with _$EarningsState {
  const factory EarningsState.initial() = EarningsInitial;

  const factory EarningsState.loading() = EarningsLoading;

  const factory EarningsState.loaded({
    required EarningsInfo earnings,
    required DriverStats stats,
    DateTime? lastUpdated,
  }) = EarningsLoaded;

  const factory EarningsState.error({
    required String message,
    EarningsInfo? cachedEarnings,
    DriverStats? cachedStats,
  }) = EarningsError;

  const factory EarningsState.refreshing({
    required EarningsInfo currentEarnings,
    required DriverStats currentStats,
  }) = EarningsRefreshing;
}

/// Extension methods for EarningsState
extension EarningsStateX on EarningsState {
  /// Check if earnings data is available
  bool get hasData => maybeWhen(
    loaded: (_, __, ___) => true,
    error: (_, earnings, stats) => earnings != null && stats != null,
    refreshing: (_, __) => true,
    orElse: () => false,
  );

  /// Get current earnings data if available
  EarningsInfo? get earnings => maybeWhen(
    loaded: (earnings, _, __) => earnings,
    error: (_, earnings, __) => earnings,
    refreshing: (earnings, _) => earnings,
    orElse: () => null,
  );

  /// Get current stats data if available
  DriverStats? get stats => maybeWhen(
    loaded: (_, stats, __) => stats,
    error: (_, __, stats) => stats,
    refreshing: (_, stats) => stats,
    orElse: () => null,
  );

  /// Check if currently loading
  bool get isLoading => maybeWhen(loading: () => true, orElse: () => false);

  /// Check if currently refreshing
  bool get isRefreshing =>
      maybeWhen(refreshing: (_, __) => true, orElse: () => false);

  /// Check if in error state
  bool get hasError =>
      maybeWhen(error: (_, __, ___) => true, orElse: () => false);

  /// Get error message if in error state
  String? get errorMessage =>
      maybeWhen(error: (message, _, __) => message, orElse: () => null);
}
