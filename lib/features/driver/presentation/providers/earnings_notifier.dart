import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/earnings/earnings_service.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';
import 'earnings_state.dart';

/// Notifier for managing earnings and statistics state
class EarningsNotifier extends StateNotifier<EarningsState> {
  final EarningsService _earningsService;

  EarningsNotifier(this._earningsService)
    : super(const EarningsState.initial());

  /// Load earnings and statistics data
  Future<void> loadEarningsData() async {
    if (state.isRefreshing) return; // Prevent multiple simultaneous loads

    // If we have data, show refreshing state, otherwise show loading
    if (state.hasData) {
      state = EarningsState.refreshing(
        currentEarnings: state.earnings!,
        currentStats: state.stats!,
      );
    } else {
      state = const EarningsState.loading();
    }

    try {
      // Load both earnings and stats concurrently
      final results = await Future.wait([
        _earningsService.getEarnings(),
        _earningsService.getDriverStats(),
      ]);

      final earnings = results[0] as EarningsInfo;
      final stats = results[1] as DriverStats;

      state = EarningsState.loaded(
        earnings: earnings,
        stats: stats,
        lastUpdated: DateTime.now(),
      );
    } catch (error) {
      final appError = _handleError(error);

      // If we have cached data, show error with cached data
      if (state.hasData) {
        state = EarningsState.error(
          message: appError.message,
          cachedEarnings: state.earnings,
          cachedStats: state.stats,
        );
      } else {
        state = EarningsState.error(message: appError.message);
      }
    }
  }

  /// Refresh earnings data
  Future<void> refreshEarningsData() async {
    await _earningsService.refreshEarningsData();
    await loadEarningsData();
  }

  /// Get earnings for a specific date range
  Future<EarningsInfo?> getEarningsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      return await _earningsService.getEarningsForDateRange(
        startDate: startDate,
        endDate: endDate,
      );
    } catch (error) {
      // Don't update state for date range queries, just return null
      return null;
    }
  }

  /// Get earnings breakdown
  Future<Map<String, dynamic>?> getEarningsBreakdown() async {
    try {
      return await _earningsService.getEarningsBreakdown();
    } catch (error) {
      return null;
    }
  }

  /// Get performance metrics
  Future<Map<String, dynamic>?> getPerformanceMetrics() async {
    try {
      return await _earningsService.getPerformanceMetrics();
    } catch (error) {
      return null;
    }
  }

  /// Clear error state and retry loading
  Future<void> retry() async {
    await loadEarningsData();
  }

  /// Reset state to initial
  void reset() {
    state = const EarningsState.initial();
  }

  /// Handle errors and convert to user-friendly messages
  AppError _handleError(dynamic error) {
    if (error is AppError) {
      return error;
    }

    // Convert other errors to AppError
    if (error.toString().contains('network') ||
        error.toString().contains('connection')) {
      return const AppError.network(
        message: 'Unable to load earnings data. Please check your connection.',
      );
    }

    if (error.toString().contains('401') ||
        error.toString().contains('unauthorized')) {
      return const AppError.authentication(
        message: 'Session expired. Please login again.',
        errorCode: '401',
      );
    }

    return AppError.unknown(
      message: 'Failed to load earnings data. Please try again.',
      exception: error,
    );
  }
}
