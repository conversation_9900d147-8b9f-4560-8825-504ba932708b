import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/models.dart';
import 'driver_providers.dart';

/// Provider for driver statistics with automatic refresh
final statsProvider = FutureProvider.autoDispose<DriverStats>((ref) async {
  final earningsService = ref.read(earningsServiceProvider);
  return await earningsService.getDriverStats();
});

/// Provider for earnings breakdown data
final earningsBreakdownProvider =
    FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
      final earningsService = ref.read(earningsServiceProvider);
      return await earningsService.getEarningsBreakdown();
    });

/// Provider for performance metrics
final performanceMetricsProvider =
    FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
      final earningsService = ref.read(earningsServiceProvider);
      return await earningsService.getPerformanceMetrics();
    });

/// Provider for earnings data with date range
final earningsDateRangeProvider = FutureProvider.autoDispose
    .family<EarningsInfo, DateRange>((ref, dateRange) async {
      final earningsService = ref.read(earningsServiceProvider);
      return await earningsService.getEarningsForDateRange(
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      );
    });

/// Helper class for date range parameters
class DateRange {
  final DateTime startDate;
  final DateTime endDate;

  const DateRange({required this.startDate, required this.endDate});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DateRange &&
          runtimeType == other.runtimeType &&
          startDate == other.startDate &&
          endDate == other.endDate;

  @override
  int get hashCode => startDate.hashCode ^ endDate.hashCode;
}

/// Computed provider for key performance indicators
final kpiProvider = Provider.autoDispose<Map<String, dynamic>>((ref) {
  final statsAsync = ref.watch(statsProvider);

  return statsAsync.when(
    data: (stats) => {
      'total_rides': stats.totalRides,
      'average_rating': stats.averageRating,
      'completion_rate': stats.completionRate,
      'acceptance_rate': stats.acceptanceRate,
      'hours_online': stats.hoursOnline,
      'earnings_per_hour': stats.totalEarnings > 0 && stats.hoursOnline > 0
          ? (stats.totalEarnings / stats.hoursOnline).toStringAsFixed(2)
          : '0.00',
    },
    loading: () => <String, dynamic>{},
    error: (_, __) => <String, dynamic>{},
  );
});

/// Provider for earnings service (imported from driver_providers.dart)
/// This is defined in driver_providers.dart to avoid circular dependencies
