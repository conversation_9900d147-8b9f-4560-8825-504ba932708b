import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../services/driver/driver_service.dart';
import '../../../../services/location/location_service.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';
import '../../../../core/di/service_locator.dart';
import 'driver_profile_state.dart';
import 'location_tracking_notifier.dart';

/// Enhanced notifier for managing driver availability state with location tracking integration
class DriverAvailabilityNotifier
    extends StateNotifier<DriverAvailabilityState> {
  final DriverService _driverService;
  final LocationService _locationService;
  final Ref _ref;

  DriverAvailabilityNotifier(
    this._driverService,
    this._locationService,
    this._ref,
  ) : super(const DriverAvailabilityState.offline());

  /// Initialize availability state by checking current status
  Future<void> initializeAvailability() async {
    try {
      final isAvailable = await _driverService.getAvailabilityStatus();
      if (isAvailable) {
        // If driver was online, start location tracking
        await _startLocationTrackingForOnlineState();
      } else {
        state = const DriverAvailabilityState.offline();
      }
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = DriverAvailabilityState.error(message: errorMessage);
    }
  }

  /// Go online - set driver as available and start location tracking
  Future<void> goOnline() async {
    if (state is DriverAvailabilityOnline) return;

    state = const DriverAvailabilityState.goingOnline();

    try {
      // First check location permissions
      final hasLocationPermission = await _locationService
          .requestLocationPermission();
      if (!hasLocationPermission) {
        state = const DriverAvailabilityState.error(
          message:
              'Location permission is required to go online. Please enable location access in your device settings.',
        );
        return;
      }

      // Check if location services are enabled
      final locationServiceEnabled = await _locationService
          .isLocationServiceEnabled();
      if (!locationServiceEnabled) {
        state = const DriverAvailabilityState.error(
          message:
              'Location services are disabled. Please enable location services in your device settings.',
        );
        return;
      }

      // Start location tracking first
      await _locationService.startLocationTracking();

      // Get current location
      final currentPosition = await _locationService.getCurrentPosition();
      final locationInfo = LocationInfo(
        latitude: currentPosition.latitude,
        longitude: currentPosition.longitude,
        updatedAt: DateTime.now(),
        heading: currentPosition.heading,
        speed: currentPosition.speed,
        accuracy: currentPosition.accuracy,
      );

      // Update availability on backend
      await _driverService.updateAvailability(true);

      // Update state to online with location
      state = DriverAvailabilityState.online(location: locationInfo);

      // Start listening to location tracking state changes
      _listenToLocationTracking();
    } catch (e) {
      // Stop location tracking if it was started
      await _locationService.stopLocationTracking();

      final errorMessage = _getErrorMessage(e);
      state = DriverAvailabilityState.error(message: errorMessage);
    }
  }

  /// Go offline - set driver as unavailable and stop location tracking
  Future<void> goOffline() async {
    if (state is DriverAvailabilityOffline) return;

    state = const DriverAvailabilityState.goingOffline();

    try {
      // Stop location tracking first
      await _locationService.stopLocationTracking();

      // Update availability on backend
      await _driverService.updateAvailability(false);

      // Update state to offline
      state = const DriverAvailabilityState.offline();
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = DriverAvailabilityState.error(message: errorMessage);
    }
  }

  /// Toggle availability status
  Future<void> toggleAvailability() async {
    final currentState = state;

    if (currentState is DriverAvailabilityOffline) {
      await goOnline();
    } else if (currentState is DriverAvailabilityOnline) {
      await goOffline();
    }
  }

  /// Handle location permission errors with user guidance
  Future<void> handleLocationPermissionError() async {
    final permission = await _locationService.checkLocationPermission();

    String errorMessage;
    switch (permission) {
      case LocationPermission.denied:
        errorMessage =
            'Location permission is required to go online. Please grant location access when prompted.';
        break;
      case LocationPermission.deniedForever:
        errorMessage =
            'Location permission is permanently denied. Please enable location access in your device settings.';
        break;
      case LocationPermission.unableToDetermine:
        errorMessage =
            'Unable to determine location permission status. Please check your device settings.';
        break;
      default:
        errorMessage = 'Location access is required to go online.';
    }

    state = DriverAvailabilityState.error(message: errorMessage);
  }

  /// Start location tracking for online state (used during initialization)
  Future<void> _startLocationTrackingForOnlineState() async {
    try {
      // Check permissions first
      final hasPermission = await _locationService.requestLocationPermission();
      if (!hasPermission) {
        await handleLocationPermissionError();
        return;
      }

      // Start location tracking
      await _locationService.startLocationTracking();

      // Get current location
      final currentPosition = await _locationService.getCurrentPosition();
      final locationInfo = LocationInfo(
        latitude: currentPosition.latitude,
        longitude: currentPosition.longitude,
        updatedAt: DateTime.now(),
        heading: currentPosition.heading,
        speed: currentPosition.speed,
        accuracy: currentPosition.accuracy,
      );

      state = DriverAvailabilityState.online(location: locationInfo);
      _listenToLocationTracking();
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = DriverAvailabilityState.error(message: errorMessage);
    }
  }

  /// Listen to location tracking state changes and update location
  void _listenToLocationTracking() {
    // Listen to location tracking notifier
    _ref.listen<LocationTrackingState>(locationTrackingProvider, (
      previous,
      next,
    ) {
      next.whenOrNull(
        active: (position, startedAt) {
          // Update location when tracking is active
          _updateLocationFromPosition(position);
        },
        error: (error) {
          // Handle location tracking errors
          if (state is DriverAvailabilityOnline) {
            state = DriverAvailabilityState.error(
              message: 'Location tracking error: ${error.message}',
            );
          }
        },
      );
    });
  }

  /// Update location from position
  void _updateLocationFromPosition(Position position) {
    if (state is DriverAvailabilityOnline) {
      final locationInfo = LocationInfo(
        latitude: position.latitude,
        longitude: position.longitude,
        updatedAt: DateTime.now(),
        heading: position.heading,
        speed: position.speed,
        accuracy: position.accuracy,
      );

      state = DriverAvailabilityState.online(location: locationInfo);
    }
  }

  /// Check if driver is currently online
  bool get isOnline => state is DriverAvailabilityOnline;

  /// Check if driver is currently offline
  bool get isOffline => state is DriverAvailabilityOffline;

  /// Check if availability is being changed
  bool get isChanging =>
      state is DriverAvailabilityGoingOnline ||
      state is DriverAvailabilityGoingOffline;

  /// Get current location if online
  LocationInfo? get currentLocation {
    return state.whenOrNull(online: (location) => location);
  }

  /// Reset state to offline and stop location tracking
  Future<void> reset() async {
    await _locationService.stopLocationTracking();
    state = const DriverAvailabilityState.offline();
  }

  /// Clear error state
  void clearError() {
    if (state is DriverAvailabilityError) {
      state = const DriverAvailabilityState.offline();
    }
  }

  /// Retry going online after fixing location issues
  Future<void> retryGoOnline() async {
    if (state is DriverAvailabilityError) {
      await goOnline();
    }
  }

  /// Handle location service recovery
  Future<void> handleLocationServiceRecovery() async {
    // Only attempt recovery if we're in an error state due to location issues
    if (state is DriverAvailabilityError) {
      final hasPermission = await _locationService.requestLocationPermission();
      final serviceEnabled = await _locationService.isLocationServiceEnabled();

      if (hasPermission && serviceEnabled) {
        // Location issues are resolved, attempt to go online again
        await retryGoOnline();
      }
    }
  }

  /// Force stop all location tracking and reset to offline
  Future<void> forceOffline() async {
    try {
      await _locationService.stopLocationTracking();
      // Don't update backend availability if we're forcing offline due to errors
      state = const DriverAvailabilityState.offline();
    } catch (e) {
      // Even if stopping location tracking fails, set state to offline
      state = const DriverAvailabilityState.offline();
    }
  }

  String _getErrorMessage(dynamic error) {
    if (error is AppError) {
      return error.when(
        network: (message, details) => message,
        authentication: (message, errorCode) => message,
        validation: (message, fieldErrors) => message,
        location: (message, errorType) {
          switch (errorType) {
            case LocationErrorType.permissionDenied:
              return 'Location permission is required to go online. Please enable location access.';
            case LocationErrorType.serviceDisabled:
              return 'Location services are disabled. Please enable location services in your device settings.';
            case LocationErrorType.timeout:
              return 'Location request timed out. Please try again.';
            case LocationErrorType.unavailable:
              return 'Location services are currently unavailable.';
          }
        },
        document: (message, errorType) => message,
        server: (message, statusCode) => message,
        unknown: (message, exception) => message,
      );
    }
    return 'An unexpected error occurred: ${error.toString()}';
  }
}

/// Provider for driver availability with location tracking integration
final driverAvailabilityProvider =
    StateNotifierProvider<DriverAvailabilityNotifier, DriverAvailabilityState>((
      ref,
    ) {
      final driverService = getIt<DriverService>();
      final locationService = getIt<LocationService>();
      return DriverAvailabilityNotifier(driverService, locationService, ref);
    });

/// Provider to check if driver is currently online
final isDriverOnlineProvider = Provider<bool>((ref) {
  final availabilityState = ref.watch(driverAvailabilityProvider);
  return availabilityState is DriverAvailabilityOnline;
});

/// Provider to check if driver is currently offline
final isDriverOfflineProvider = Provider<bool>((ref) {
  final availabilityState = ref.watch(driverAvailabilityProvider);
  return availabilityState is DriverAvailabilityOffline;
});

/// Provider to check if availability is being changed
final isAvailabilityChangingProvider = Provider<bool>((ref) {
  final availabilityState = ref.watch(driverAvailabilityProvider);
  return availabilityState is DriverAvailabilityGoingOnline ||
      availabilityState is DriverAvailabilityGoingOffline;
});

/// Provider to get current driver location when online
final currentDriverLocationProvider = Provider<LocationInfo?>((ref) {
  final availabilityState = ref.watch(driverAvailabilityProvider);
  return availabilityState.whenOrNull(online: (location) => location);
});
