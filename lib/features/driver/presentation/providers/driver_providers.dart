import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../services/driver/driver_service.dart';
import '../../../../services/earnings/earnings_service.dart';
import '../../../../shared/models/models.dart';
import 'driver_profile_notifier.dart';
import 'vehicle_info_notifier.dart';
import 'driver_profile_state.dart';
import 'earnings_notifier.dart';
import 'earnings_state.dart';

/// Provider for DriverService
final driverServiceProvider = Provider<DriverService>((ref) {
  return getIt<DriverService>();
});

/// Provider for EarningsService
final earningsServiceProvider = Provider<EarningsService>((ref) {
  return getIt<EarningsService>();
});

/// Provider for driver profile state management
final driverProfileProvider =
    StateNotifierProvider<DriverProfileNotifier, DriverProfileState>((ref) {
      final driverService = ref.read(driverServiceProvider);
      return DriverProfileNotifier(driverService);
    });

/// Provider for vehicle information state management
final vehicleInfoProvider =
    StateNotifierProvider<VehicleInfoNotifier, VehicleInfoState>((ref) {
      final driverService = ref.read(driverServiceProvider);
      return VehicleInfoNotifier(driverService);
    });

// Driver availability provider is now defined in driver_availability_notifier.dart

/// Provider for earnings state management
final earningsProvider = StateNotifierProvider<EarningsNotifier, EarningsState>(
  (ref) {
    final earningsService = ref.read(earningsServiceProvider);
    return EarningsNotifier(earningsService);
  },
);

/// Provider for driver earnings (legacy - kept for backward compatibility)
final driverEarningsProvider = FutureProvider<EarningsInfo>((ref) async {
  final driverService = ref.read(driverServiceProvider);
  return await driverService.getEarnings();
});

/// Provider for driver statistics (legacy - kept for backward compatibility)
final driverStatsProvider = FutureProvider<DriverStats>((ref) async {
  final driverService = ref.read(driverServiceProvider);
  return await driverService.getDriverStats();
});

/// Provider for verification status
final verificationStatusProvider = FutureProvider<VerificationStatus>((
  ref,
) async {
  final driverService = ref.read(driverServiceProvider);
  return await driverService.getVerificationStatus();
});

/// Provider to check if driver has complete profile
final hasCompleteProfileProvider = FutureProvider<bool>((ref) async {
  final driverService = ref.read(driverServiceProvider);
  return await driverService.hasCompleteProfile();
});

/// Provider for current driver profile (computed from state)
final currentDriverProfileProvider = Provider<DriverProfile?>((ref) {
  final profileState = ref.watch(driverProfileProvider);
  return profileState.maybeWhen(
    loaded: (profile) => profile,
    orElse: () => null,
  );
});

/// Provider for current vehicle info (computed from state)
final currentVehicleInfoProvider = Provider<VehicleInfo?>((ref) {
  final vehicleState = ref.watch(vehicleInfoProvider);
  return vehicleState.maybeWhen(
    loaded: (vehicleInfo) => vehicleInfo,
    orElse: () => null,
  );
});

// Driver availability related providers are now defined in driver_availability_notifier.dart
