// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_profile_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$DriverProfileState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(DriverProfile profile) loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(DriverProfile profile)? loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(DriverProfile profile)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverProfileInitial value) initial,
    required TResult Function(DriverProfileLoading value) loading,
    required TResult Function(DriverProfileLoaded value) loaded,
    required TResult Function(DriverProfileError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverProfileInitial value)? initial,
    TResult? Function(DriverProfileLoading value)? loading,
    TResult? Function(DriverProfileLoaded value)? loaded,
    TResult? Function(DriverProfileError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverProfileInitial value)? initial,
    TResult Function(DriverProfileLoading value)? loading,
    TResult Function(DriverProfileLoaded value)? loaded,
    TResult Function(DriverProfileError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileStateCopyWith<$Res> {
  factory $DriverProfileStateCopyWith(
    DriverProfileState value,
    $Res Function(DriverProfileState) then,
  ) = _$DriverProfileStateCopyWithImpl<$Res, DriverProfileState>;
}

/// @nodoc
class _$DriverProfileStateCopyWithImpl<$Res, $Val extends DriverProfileState>
    implements $DriverProfileStateCopyWith<$Res> {
  _$DriverProfileStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DriverProfileInitialImplCopyWith<$Res> {
  factory _$$DriverProfileInitialImplCopyWith(
    _$DriverProfileInitialImpl value,
    $Res Function(_$DriverProfileInitialImpl) then,
  ) = __$$DriverProfileInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverProfileInitialImplCopyWithImpl<$Res>
    extends _$DriverProfileStateCopyWithImpl<$Res, _$DriverProfileInitialImpl>
    implements _$$DriverProfileInitialImplCopyWith<$Res> {
  __$$DriverProfileInitialImplCopyWithImpl(
    _$DriverProfileInitialImpl _value,
    $Res Function(_$DriverProfileInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DriverProfileInitialImpl implements DriverProfileInitial {
  const _$DriverProfileInitialImpl();

  @override
  String toString() {
    return 'DriverProfileState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(DriverProfile profile) loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(DriverProfile profile)? loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(DriverProfile profile)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverProfileInitial value) initial,
    required TResult Function(DriverProfileLoading value) loading,
    required TResult Function(DriverProfileLoaded value) loaded,
    required TResult Function(DriverProfileError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverProfileInitial value)? initial,
    TResult? Function(DriverProfileLoading value)? loading,
    TResult? Function(DriverProfileLoaded value)? loaded,
    TResult? Function(DriverProfileError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverProfileInitial value)? initial,
    TResult Function(DriverProfileLoading value)? loading,
    TResult Function(DriverProfileLoaded value)? loaded,
    TResult Function(DriverProfileError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class DriverProfileInitial implements DriverProfileState {
  const factory DriverProfileInitial() = _$DriverProfileInitialImpl;
}

/// @nodoc
abstract class _$$DriverProfileLoadingImplCopyWith<$Res> {
  factory _$$DriverProfileLoadingImplCopyWith(
    _$DriverProfileLoadingImpl value,
    $Res Function(_$DriverProfileLoadingImpl) then,
  ) = __$$DriverProfileLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverProfileLoadingImplCopyWithImpl<$Res>
    extends _$DriverProfileStateCopyWithImpl<$Res, _$DriverProfileLoadingImpl>
    implements _$$DriverProfileLoadingImplCopyWith<$Res> {
  __$$DriverProfileLoadingImplCopyWithImpl(
    _$DriverProfileLoadingImpl _value,
    $Res Function(_$DriverProfileLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DriverProfileLoadingImpl implements DriverProfileLoading {
  const _$DriverProfileLoadingImpl();

  @override
  String toString() {
    return 'DriverProfileState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(DriverProfile profile) loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(DriverProfile profile)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(DriverProfile profile)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverProfileInitial value) initial,
    required TResult Function(DriverProfileLoading value) loading,
    required TResult Function(DriverProfileLoaded value) loaded,
    required TResult Function(DriverProfileError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverProfileInitial value)? initial,
    TResult? Function(DriverProfileLoading value)? loading,
    TResult? Function(DriverProfileLoaded value)? loaded,
    TResult? Function(DriverProfileError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverProfileInitial value)? initial,
    TResult Function(DriverProfileLoading value)? loading,
    TResult Function(DriverProfileLoaded value)? loaded,
    TResult Function(DriverProfileError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class DriverProfileLoading implements DriverProfileState {
  const factory DriverProfileLoading() = _$DriverProfileLoadingImpl;
}

/// @nodoc
abstract class _$$DriverProfileLoadedImplCopyWith<$Res> {
  factory _$$DriverProfileLoadedImplCopyWith(
    _$DriverProfileLoadedImpl value,
    $Res Function(_$DriverProfileLoadedImpl) then,
  ) = __$$DriverProfileLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DriverProfile profile});

  $DriverProfileCopyWith<$Res> get profile;
}

/// @nodoc
class __$$DriverProfileLoadedImplCopyWithImpl<$Res>
    extends _$DriverProfileStateCopyWithImpl<$Res, _$DriverProfileLoadedImpl>
    implements _$$DriverProfileLoadedImplCopyWith<$Res> {
  __$$DriverProfileLoadedImplCopyWithImpl(
    _$DriverProfileLoadedImpl _value,
    $Res Function(_$DriverProfileLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? profile = null}) {
    return _then(
      _$DriverProfileLoadedImpl(
        profile: null == profile
            ? _value.profile
            : profile // ignore: cast_nullable_to_non_nullable
                  as DriverProfile,
      ),
    );
  }

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DriverProfileCopyWith<$Res> get profile {
    return $DriverProfileCopyWith<$Res>(_value.profile, (value) {
      return _then(_value.copyWith(profile: value));
    });
  }
}

/// @nodoc

class _$DriverProfileLoadedImpl implements DriverProfileLoaded {
  const _$DriverProfileLoadedImpl({required this.profile});

  @override
  final DriverProfile profile;

  @override
  String toString() {
    return 'DriverProfileState.loaded(profile: $profile)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileLoadedImpl &&
            (identical(other.profile, profile) || other.profile == profile));
  }

  @override
  int get hashCode => Object.hash(runtimeType, profile);

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileLoadedImplCopyWith<_$DriverProfileLoadedImpl> get copyWith =>
      __$$DriverProfileLoadedImplCopyWithImpl<_$DriverProfileLoadedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(DriverProfile profile) loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(profile);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(DriverProfile profile)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(profile);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(DriverProfile profile)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(profile);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverProfileInitial value) initial,
    required TResult Function(DriverProfileLoading value) loading,
    required TResult Function(DriverProfileLoaded value) loaded,
    required TResult Function(DriverProfileError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverProfileInitial value)? initial,
    TResult? Function(DriverProfileLoading value)? loading,
    TResult? Function(DriverProfileLoaded value)? loaded,
    TResult? Function(DriverProfileError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverProfileInitial value)? initial,
    TResult Function(DriverProfileLoading value)? loading,
    TResult Function(DriverProfileLoaded value)? loaded,
    TResult Function(DriverProfileError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class DriverProfileLoaded implements DriverProfileState {
  const factory DriverProfileLoaded({required final DriverProfile profile}) =
      _$DriverProfileLoadedImpl;

  DriverProfile get profile;

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileLoadedImplCopyWith<_$DriverProfileLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DriverProfileErrorImplCopyWith<$Res> {
  factory _$$DriverProfileErrorImplCopyWith(
    _$DriverProfileErrorImpl value,
    $Res Function(_$DriverProfileErrorImpl) then,
  ) = __$$DriverProfileErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DriverProfileErrorImplCopyWithImpl<$Res>
    extends _$DriverProfileStateCopyWithImpl<$Res, _$DriverProfileErrorImpl>
    implements _$$DriverProfileErrorImplCopyWith<$Res> {
  __$$DriverProfileErrorImplCopyWithImpl(
    _$DriverProfileErrorImpl _value,
    $Res Function(_$DriverProfileErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$DriverProfileErrorImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$DriverProfileErrorImpl implements DriverProfileError {
  const _$DriverProfileErrorImpl({required this.message});

  @override
  final String message;

  @override
  String toString() {
    return 'DriverProfileState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileErrorImplCopyWith<_$DriverProfileErrorImpl> get copyWith =>
      __$$DriverProfileErrorImplCopyWithImpl<_$DriverProfileErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(DriverProfile profile) loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(DriverProfile profile)? loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(DriverProfile profile)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverProfileInitial value) initial,
    required TResult Function(DriverProfileLoading value) loading,
    required TResult Function(DriverProfileLoaded value) loaded,
    required TResult Function(DriverProfileError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverProfileInitial value)? initial,
    TResult? Function(DriverProfileLoading value)? loading,
    TResult? Function(DriverProfileLoaded value)? loaded,
    TResult? Function(DriverProfileError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverProfileInitial value)? initial,
    TResult Function(DriverProfileLoading value)? loading,
    TResult Function(DriverProfileLoaded value)? loaded,
    TResult Function(DriverProfileError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class DriverProfileError implements DriverProfileState {
  const factory DriverProfileError({required final String message}) =
      _$DriverProfileErrorImpl;

  String get message;

  /// Create a copy of DriverProfileState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileErrorImplCopyWith<_$DriverProfileErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$VehicleInfoState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VehicleInfo vehicleInfo) loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VehicleInfo vehicleInfo)? loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VehicleInfo vehicleInfo)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VehicleInfoInitial value) initial,
    required TResult Function(VehicleInfoLoading value) loading,
    required TResult Function(VehicleInfoLoaded value) loaded,
    required TResult Function(VehicleInfoError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VehicleInfoInitial value)? initial,
    TResult? Function(VehicleInfoLoading value)? loading,
    TResult? Function(VehicleInfoLoaded value)? loaded,
    TResult? Function(VehicleInfoError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VehicleInfoInitial value)? initial,
    TResult Function(VehicleInfoLoading value)? loading,
    TResult Function(VehicleInfoLoaded value)? loaded,
    TResult Function(VehicleInfoError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleInfoStateCopyWith<$Res> {
  factory $VehicleInfoStateCopyWith(
    VehicleInfoState value,
    $Res Function(VehicleInfoState) then,
  ) = _$VehicleInfoStateCopyWithImpl<$Res, VehicleInfoState>;
}

/// @nodoc
class _$VehicleInfoStateCopyWithImpl<$Res, $Val extends VehicleInfoState>
    implements $VehicleInfoStateCopyWith<$Res> {
  _$VehicleInfoStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$VehicleInfoInitialImplCopyWith<$Res> {
  factory _$$VehicleInfoInitialImplCopyWith(
    _$VehicleInfoInitialImpl value,
    $Res Function(_$VehicleInfoInitialImpl) then,
  ) = __$$VehicleInfoInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VehicleInfoInitialImplCopyWithImpl<$Res>
    extends _$VehicleInfoStateCopyWithImpl<$Res, _$VehicleInfoInitialImpl>
    implements _$$VehicleInfoInitialImplCopyWith<$Res> {
  __$$VehicleInfoInitialImplCopyWithImpl(
    _$VehicleInfoInitialImpl _value,
    $Res Function(_$VehicleInfoInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VehicleInfoInitialImpl implements VehicleInfoInitial {
  const _$VehicleInfoInitialImpl();

  @override
  String toString() {
    return 'VehicleInfoState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$VehicleInfoInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VehicleInfo vehicleInfo) loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VehicleInfo vehicleInfo)? loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VehicleInfo vehicleInfo)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VehicleInfoInitial value) initial,
    required TResult Function(VehicleInfoLoading value) loading,
    required TResult Function(VehicleInfoLoaded value) loaded,
    required TResult Function(VehicleInfoError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VehicleInfoInitial value)? initial,
    TResult? Function(VehicleInfoLoading value)? loading,
    TResult? Function(VehicleInfoLoaded value)? loaded,
    TResult? Function(VehicleInfoError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VehicleInfoInitial value)? initial,
    TResult Function(VehicleInfoLoading value)? loading,
    TResult Function(VehicleInfoLoaded value)? loaded,
    TResult Function(VehicleInfoError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class VehicleInfoInitial implements VehicleInfoState {
  const factory VehicleInfoInitial() = _$VehicleInfoInitialImpl;
}

/// @nodoc
abstract class _$$VehicleInfoLoadingImplCopyWith<$Res> {
  factory _$$VehicleInfoLoadingImplCopyWith(
    _$VehicleInfoLoadingImpl value,
    $Res Function(_$VehicleInfoLoadingImpl) then,
  ) = __$$VehicleInfoLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VehicleInfoLoadingImplCopyWithImpl<$Res>
    extends _$VehicleInfoStateCopyWithImpl<$Res, _$VehicleInfoLoadingImpl>
    implements _$$VehicleInfoLoadingImplCopyWith<$Res> {
  __$$VehicleInfoLoadingImplCopyWithImpl(
    _$VehicleInfoLoadingImpl _value,
    $Res Function(_$VehicleInfoLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VehicleInfoLoadingImpl implements VehicleInfoLoading {
  const _$VehicleInfoLoadingImpl();

  @override
  String toString() {
    return 'VehicleInfoState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$VehicleInfoLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VehicleInfo vehicleInfo) loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VehicleInfo vehicleInfo)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VehicleInfo vehicleInfo)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VehicleInfoInitial value) initial,
    required TResult Function(VehicleInfoLoading value) loading,
    required TResult Function(VehicleInfoLoaded value) loaded,
    required TResult Function(VehicleInfoError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VehicleInfoInitial value)? initial,
    TResult? Function(VehicleInfoLoading value)? loading,
    TResult? Function(VehicleInfoLoaded value)? loaded,
    TResult? Function(VehicleInfoError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VehicleInfoInitial value)? initial,
    TResult Function(VehicleInfoLoading value)? loading,
    TResult Function(VehicleInfoLoaded value)? loaded,
    TResult Function(VehicleInfoError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class VehicleInfoLoading implements VehicleInfoState {
  const factory VehicleInfoLoading() = _$VehicleInfoLoadingImpl;
}

/// @nodoc
abstract class _$$VehicleInfoLoadedImplCopyWith<$Res> {
  factory _$$VehicleInfoLoadedImplCopyWith(
    _$VehicleInfoLoadedImpl value,
    $Res Function(_$VehicleInfoLoadedImpl) then,
  ) = __$$VehicleInfoLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VehicleInfo vehicleInfo});

  $VehicleInfoCopyWith<$Res> get vehicleInfo;
}

/// @nodoc
class __$$VehicleInfoLoadedImplCopyWithImpl<$Res>
    extends _$VehicleInfoStateCopyWithImpl<$Res, _$VehicleInfoLoadedImpl>
    implements _$$VehicleInfoLoadedImplCopyWith<$Res> {
  __$$VehicleInfoLoadedImplCopyWithImpl(
    _$VehicleInfoLoadedImpl _value,
    $Res Function(_$VehicleInfoLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? vehicleInfo = null}) {
    return _then(
      _$VehicleInfoLoadedImpl(
        vehicleInfo: null == vehicleInfo
            ? _value.vehicleInfo
            : vehicleInfo // ignore: cast_nullable_to_non_nullable
                  as VehicleInfo,
      ),
    );
  }

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VehicleInfoCopyWith<$Res> get vehicleInfo {
    return $VehicleInfoCopyWith<$Res>(_value.vehicleInfo, (value) {
      return _then(_value.copyWith(vehicleInfo: value));
    });
  }
}

/// @nodoc

class _$VehicleInfoLoadedImpl implements VehicleInfoLoaded {
  const _$VehicleInfoLoadedImpl({required this.vehicleInfo});

  @override
  final VehicleInfo vehicleInfo;

  @override
  String toString() {
    return 'VehicleInfoState.loaded(vehicleInfo: $vehicleInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleInfoLoadedImpl &&
            (identical(other.vehicleInfo, vehicleInfo) ||
                other.vehicleInfo == vehicleInfo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, vehicleInfo);

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleInfoLoadedImplCopyWith<_$VehicleInfoLoadedImpl> get copyWith =>
      __$$VehicleInfoLoadedImplCopyWithImpl<_$VehicleInfoLoadedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VehicleInfo vehicleInfo) loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(vehicleInfo);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VehicleInfo vehicleInfo)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(vehicleInfo);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VehicleInfo vehicleInfo)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(vehicleInfo);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VehicleInfoInitial value) initial,
    required TResult Function(VehicleInfoLoading value) loading,
    required TResult Function(VehicleInfoLoaded value) loaded,
    required TResult Function(VehicleInfoError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VehicleInfoInitial value)? initial,
    TResult? Function(VehicleInfoLoading value)? loading,
    TResult? Function(VehicleInfoLoaded value)? loaded,
    TResult? Function(VehicleInfoError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VehicleInfoInitial value)? initial,
    TResult Function(VehicleInfoLoading value)? loading,
    TResult Function(VehicleInfoLoaded value)? loaded,
    TResult Function(VehicleInfoError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class VehicleInfoLoaded implements VehicleInfoState {
  const factory VehicleInfoLoaded({required final VehicleInfo vehicleInfo}) =
      _$VehicleInfoLoadedImpl;

  VehicleInfo get vehicleInfo;

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VehicleInfoLoadedImplCopyWith<_$VehicleInfoLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VehicleInfoErrorImplCopyWith<$Res> {
  factory _$$VehicleInfoErrorImplCopyWith(
    _$VehicleInfoErrorImpl value,
    $Res Function(_$VehicleInfoErrorImpl) then,
  ) = __$$VehicleInfoErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$VehicleInfoErrorImplCopyWithImpl<$Res>
    extends _$VehicleInfoStateCopyWithImpl<$Res, _$VehicleInfoErrorImpl>
    implements _$$VehicleInfoErrorImplCopyWith<$Res> {
  __$$VehicleInfoErrorImplCopyWithImpl(
    _$VehicleInfoErrorImpl _value,
    $Res Function(_$VehicleInfoErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$VehicleInfoErrorImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$VehicleInfoErrorImpl implements VehicleInfoError {
  const _$VehicleInfoErrorImpl({required this.message});

  @override
  final String message;

  @override
  String toString() {
    return 'VehicleInfoState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleInfoErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleInfoErrorImplCopyWith<_$VehicleInfoErrorImpl> get copyWith =>
      __$$VehicleInfoErrorImplCopyWithImpl<_$VehicleInfoErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VehicleInfo vehicleInfo) loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VehicleInfo vehicleInfo)? loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VehicleInfo vehicleInfo)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VehicleInfoInitial value) initial,
    required TResult Function(VehicleInfoLoading value) loading,
    required TResult Function(VehicleInfoLoaded value) loaded,
    required TResult Function(VehicleInfoError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VehicleInfoInitial value)? initial,
    TResult? Function(VehicleInfoLoading value)? loading,
    TResult? Function(VehicleInfoLoaded value)? loaded,
    TResult? Function(VehicleInfoError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VehicleInfoInitial value)? initial,
    TResult Function(VehicleInfoLoading value)? loading,
    TResult Function(VehicleInfoLoaded value)? loaded,
    TResult Function(VehicleInfoError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class VehicleInfoError implements VehicleInfoState {
  const factory VehicleInfoError({required final String message}) =
      _$VehicleInfoErrorImpl;

  String get message;

  /// Create a copy of VehicleInfoState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VehicleInfoErrorImplCopyWith<_$VehicleInfoErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DriverAvailabilityState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() offline,
    required TResult Function() goingOnline,
    required TResult Function(LocationInfo location) online,
    required TResult Function() goingOffline,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? offline,
    TResult? Function()? goingOnline,
    TResult? Function(LocationInfo location)? online,
    TResult? Function()? goingOffline,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? offline,
    TResult Function()? goingOnline,
    TResult Function(LocationInfo location)? online,
    TResult Function()? goingOffline,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverAvailabilityOffline value) offline,
    required TResult Function(DriverAvailabilityGoingOnline value) goingOnline,
    required TResult Function(DriverAvailabilityOnline value) online,
    required TResult Function(DriverAvailabilityGoingOffline value)
    goingOffline,
    required TResult Function(DriverAvailabilityError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverAvailabilityOffline value)? offline,
    TResult? Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult? Function(DriverAvailabilityOnline value)? online,
    TResult? Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult? Function(DriverAvailabilityError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverAvailabilityOffline value)? offline,
    TResult Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult Function(DriverAvailabilityOnline value)? online,
    TResult Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult Function(DriverAvailabilityError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverAvailabilityStateCopyWith<$Res> {
  factory $DriverAvailabilityStateCopyWith(
    DriverAvailabilityState value,
    $Res Function(DriverAvailabilityState) then,
  ) = _$DriverAvailabilityStateCopyWithImpl<$Res, DriverAvailabilityState>;
}

/// @nodoc
class _$DriverAvailabilityStateCopyWithImpl<
  $Res,
  $Val extends DriverAvailabilityState
>
    implements $DriverAvailabilityStateCopyWith<$Res> {
  _$DriverAvailabilityStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DriverAvailabilityOfflineImplCopyWith<$Res> {
  factory _$$DriverAvailabilityOfflineImplCopyWith(
    _$DriverAvailabilityOfflineImpl value,
    $Res Function(_$DriverAvailabilityOfflineImpl) then,
  ) = __$$DriverAvailabilityOfflineImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverAvailabilityOfflineImplCopyWithImpl<$Res>
    extends
        _$DriverAvailabilityStateCopyWithImpl<
          $Res,
          _$DriverAvailabilityOfflineImpl
        >
    implements _$$DriverAvailabilityOfflineImplCopyWith<$Res> {
  __$$DriverAvailabilityOfflineImplCopyWithImpl(
    _$DriverAvailabilityOfflineImpl _value,
    $Res Function(_$DriverAvailabilityOfflineImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DriverAvailabilityOfflineImpl implements DriverAvailabilityOffline {
  const _$DriverAvailabilityOfflineImpl();

  @override
  String toString() {
    return 'DriverAvailabilityState.offline()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverAvailabilityOfflineImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() offline,
    required TResult Function() goingOnline,
    required TResult Function(LocationInfo location) online,
    required TResult Function() goingOffline,
    required TResult Function(String message) error,
  }) {
    return offline();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? offline,
    TResult? Function()? goingOnline,
    TResult? Function(LocationInfo location)? online,
    TResult? Function()? goingOffline,
    TResult? Function(String message)? error,
  }) {
    return offline?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? offline,
    TResult Function()? goingOnline,
    TResult Function(LocationInfo location)? online,
    TResult Function()? goingOffline,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (offline != null) {
      return offline();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverAvailabilityOffline value) offline,
    required TResult Function(DriverAvailabilityGoingOnline value) goingOnline,
    required TResult Function(DriverAvailabilityOnline value) online,
    required TResult Function(DriverAvailabilityGoingOffline value)
    goingOffline,
    required TResult Function(DriverAvailabilityError value) error,
  }) {
    return offline(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverAvailabilityOffline value)? offline,
    TResult? Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult? Function(DriverAvailabilityOnline value)? online,
    TResult? Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult? Function(DriverAvailabilityError value)? error,
  }) {
    return offline?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverAvailabilityOffline value)? offline,
    TResult Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult Function(DriverAvailabilityOnline value)? online,
    TResult Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult Function(DriverAvailabilityError value)? error,
    required TResult orElse(),
  }) {
    if (offline != null) {
      return offline(this);
    }
    return orElse();
  }
}

abstract class DriverAvailabilityOffline implements DriverAvailabilityState {
  const factory DriverAvailabilityOffline() = _$DriverAvailabilityOfflineImpl;
}

/// @nodoc
abstract class _$$DriverAvailabilityGoingOnlineImplCopyWith<$Res> {
  factory _$$DriverAvailabilityGoingOnlineImplCopyWith(
    _$DriverAvailabilityGoingOnlineImpl value,
    $Res Function(_$DriverAvailabilityGoingOnlineImpl) then,
  ) = __$$DriverAvailabilityGoingOnlineImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverAvailabilityGoingOnlineImplCopyWithImpl<$Res>
    extends
        _$DriverAvailabilityStateCopyWithImpl<
          $Res,
          _$DriverAvailabilityGoingOnlineImpl
        >
    implements _$$DriverAvailabilityGoingOnlineImplCopyWith<$Res> {
  __$$DriverAvailabilityGoingOnlineImplCopyWithImpl(
    _$DriverAvailabilityGoingOnlineImpl _value,
    $Res Function(_$DriverAvailabilityGoingOnlineImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DriverAvailabilityGoingOnlineImpl
    implements DriverAvailabilityGoingOnline {
  const _$DriverAvailabilityGoingOnlineImpl();

  @override
  String toString() {
    return 'DriverAvailabilityState.goingOnline()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverAvailabilityGoingOnlineImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() offline,
    required TResult Function() goingOnline,
    required TResult Function(LocationInfo location) online,
    required TResult Function() goingOffline,
    required TResult Function(String message) error,
  }) {
    return goingOnline();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? offline,
    TResult? Function()? goingOnline,
    TResult? Function(LocationInfo location)? online,
    TResult? Function()? goingOffline,
    TResult? Function(String message)? error,
  }) {
    return goingOnline?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? offline,
    TResult Function()? goingOnline,
    TResult Function(LocationInfo location)? online,
    TResult Function()? goingOffline,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (goingOnline != null) {
      return goingOnline();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverAvailabilityOffline value) offline,
    required TResult Function(DriverAvailabilityGoingOnline value) goingOnline,
    required TResult Function(DriverAvailabilityOnline value) online,
    required TResult Function(DriverAvailabilityGoingOffline value)
    goingOffline,
    required TResult Function(DriverAvailabilityError value) error,
  }) {
    return goingOnline(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverAvailabilityOffline value)? offline,
    TResult? Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult? Function(DriverAvailabilityOnline value)? online,
    TResult? Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult? Function(DriverAvailabilityError value)? error,
  }) {
    return goingOnline?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverAvailabilityOffline value)? offline,
    TResult Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult Function(DriverAvailabilityOnline value)? online,
    TResult Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult Function(DriverAvailabilityError value)? error,
    required TResult orElse(),
  }) {
    if (goingOnline != null) {
      return goingOnline(this);
    }
    return orElse();
  }
}

abstract class DriverAvailabilityGoingOnline
    implements DriverAvailabilityState {
  const factory DriverAvailabilityGoingOnline() =
      _$DriverAvailabilityGoingOnlineImpl;
}

/// @nodoc
abstract class _$$DriverAvailabilityOnlineImplCopyWith<$Res> {
  factory _$$DriverAvailabilityOnlineImplCopyWith(
    _$DriverAvailabilityOnlineImpl value,
    $Res Function(_$DriverAvailabilityOnlineImpl) then,
  ) = __$$DriverAvailabilityOnlineImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LocationInfo location});

  $LocationInfoCopyWith<$Res> get location;
}

/// @nodoc
class __$$DriverAvailabilityOnlineImplCopyWithImpl<$Res>
    extends
        _$DriverAvailabilityStateCopyWithImpl<
          $Res,
          _$DriverAvailabilityOnlineImpl
        >
    implements _$$DriverAvailabilityOnlineImplCopyWith<$Res> {
  __$$DriverAvailabilityOnlineImplCopyWithImpl(
    _$DriverAvailabilityOnlineImpl _value,
    $Res Function(_$DriverAvailabilityOnlineImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? location = null}) {
    return _then(
      _$DriverAvailabilityOnlineImpl(
        location: null == location
            ? _value.location
            : location // ignore: cast_nullable_to_non_nullable
                  as LocationInfo,
      ),
    );
  }

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationInfoCopyWith<$Res> get location {
    return $LocationInfoCopyWith<$Res>(_value.location, (value) {
      return _then(_value.copyWith(location: value));
    });
  }
}

/// @nodoc

class _$DriverAvailabilityOnlineImpl implements DriverAvailabilityOnline {
  const _$DriverAvailabilityOnlineImpl({required this.location});

  @override
  final LocationInfo location;

  @override
  String toString() {
    return 'DriverAvailabilityState.online(location: $location)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverAvailabilityOnlineImpl &&
            (identical(other.location, location) ||
                other.location == location));
  }

  @override
  int get hashCode => Object.hash(runtimeType, location);

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverAvailabilityOnlineImplCopyWith<_$DriverAvailabilityOnlineImpl>
  get copyWith =>
      __$$DriverAvailabilityOnlineImplCopyWithImpl<
        _$DriverAvailabilityOnlineImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() offline,
    required TResult Function() goingOnline,
    required TResult Function(LocationInfo location) online,
    required TResult Function() goingOffline,
    required TResult Function(String message) error,
  }) {
    return online(location);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? offline,
    TResult? Function()? goingOnline,
    TResult? Function(LocationInfo location)? online,
    TResult? Function()? goingOffline,
    TResult? Function(String message)? error,
  }) {
    return online?.call(location);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? offline,
    TResult Function()? goingOnline,
    TResult Function(LocationInfo location)? online,
    TResult Function()? goingOffline,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (online != null) {
      return online(location);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverAvailabilityOffline value) offline,
    required TResult Function(DriverAvailabilityGoingOnline value) goingOnline,
    required TResult Function(DriverAvailabilityOnline value) online,
    required TResult Function(DriverAvailabilityGoingOffline value)
    goingOffline,
    required TResult Function(DriverAvailabilityError value) error,
  }) {
    return online(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverAvailabilityOffline value)? offline,
    TResult? Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult? Function(DriverAvailabilityOnline value)? online,
    TResult? Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult? Function(DriverAvailabilityError value)? error,
  }) {
    return online?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverAvailabilityOffline value)? offline,
    TResult Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult Function(DriverAvailabilityOnline value)? online,
    TResult Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult Function(DriverAvailabilityError value)? error,
    required TResult orElse(),
  }) {
    if (online != null) {
      return online(this);
    }
    return orElse();
  }
}

abstract class DriverAvailabilityOnline implements DriverAvailabilityState {
  const factory DriverAvailabilityOnline({
    required final LocationInfo location,
  }) = _$DriverAvailabilityOnlineImpl;

  LocationInfo get location;

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverAvailabilityOnlineImplCopyWith<_$DriverAvailabilityOnlineImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DriverAvailabilityGoingOfflineImplCopyWith<$Res> {
  factory _$$DriverAvailabilityGoingOfflineImplCopyWith(
    _$DriverAvailabilityGoingOfflineImpl value,
    $Res Function(_$DriverAvailabilityGoingOfflineImpl) then,
  ) = __$$DriverAvailabilityGoingOfflineImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DriverAvailabilityGoingOfflineImplCopyWithImpl<$Res>
    extends
        _$DriverAvailabilityStateCopyWithImpl<
          $Res,
          _$DriverAvailabilityGoingOfflineImpl
        >
    implements _$$DriverAvailabilityGoingOfflineImplCopyWith<$Res> {
  __$$DriverAvailabilityGoingOfflineImplCopyWithImpl(
    _$DriverAvailabilityGoingOfflineImpl _value,
    $Res Function(_$DriverAvailabilityGoingOfflineImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DriverAvailabilityGoingOfflineImpl
    implements DriverAvailabilityGoingOffline {
  const _$DriverAvailabilityGoingOfflineImpl();

  @override
  String toString() {
    return 'DriverAvailabilityState.goingOffline()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverAvailabilityGoingOfflineImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() offline,
    required TResult Function() goingOnline,
    required TResult Function(LocationInfo location) online,
    required TResult Function() goingOffline,
    required TResult Function(String message) error,
  }) {
    return goingOffline();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? offline,
    TResult? Function()? goingOnline,
    TResult? Function(LocationInfo location)? online,
    TResult? Function()? goingOffline,
    TResult? Function(String message)? error,
  }) {
    return goingOffline?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? offline,
    TResult Function()? goingOnline,
    TResult Function(LocationInfo location)? online,
    TResult Function()? goingOffline,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (goingOffline != null) {
      return goingOffline();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverAvailabilityOffline value) offline,
    required TResult Function(DriverAvailabilityGoingOnline value) goingOnline,
    required TResult Function(DriverAvailabilityOnline value) online,
    required TResult Function(DriverAvailabilityGoingOffline value)
    goingOffline,
    required TResult Function(DriverAvailabilityError value) error,
  }) {
    return goingOffline(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverAvailabilityOffline value)? offline,
    TResult? Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult? Function(DriverAvailabilityOnline value)? online,
    TResult? Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult? Function(DriverAvailabilityError value)? error,
  }) {
    return goingOffline?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverAvailabilityOffline value)? offline,
    TResult Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult Function(DriverAvailabilityOnline value)? online,
    TResult Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult Function(DriverAvailabilityError value)? error,
    required TResult orElse(),
  }) {
    if (goingOffline != null) {
      return goingOffline(this);
    }
    return orElse();
  }
}

abstract class DriverAvailabilityGoingOffline
    implements DriverAvailabilityState {
  const factory DriverAvailabilityGoingOffline() =
      _$DriverAvailabilityGoingOfflineImpl;
}

/// @nodoc
abstract class _$$DriverAvailabilityErrorImplCopyWith<$Res> {
  factory _$$DriverAvailabilityErrorImplCopyWith(
    _$DriverAvailabilityErrorImpl value,
    $Res Function(_$DriverAvailabilityErrorImpl) then,
  ) = __$$DriverAvailabilityErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DriverAvailabilityErrorImplCopyWithImpl<$Res>
    extends
        _$DriverAvailabilityStateCopyWithImpl<
          $Res,
          _$DriverAvailabilityErrorImpl
        >
    implements _$$DriverAvailabilityErrorImplCopyWith<$Res> {
  __$$DriverAvailabilityErrorImplCopyWithImpl(
    _$DriverAvailabilityErrorImpl _value,
    $Res Function(_$DriverAvailabilityErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$DriverAvailabilityErrorImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$DriverAvailabilityErrorImpl implements DriverAvailabilityError {
  const _$DriverAvailabilityErrorImpl({required this.message});

  @override
  final String message;

  @override
  String toString() {
    return 'DriverAvailabilityState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverAvailabilityErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverAvailabilityErrorImplCopyWith<_$DriverAvailabilityErrorImpl>
  get copyWith =>
      __$$DriverAvailabilityErrorImplCopyWithImpl<
        _$DriverAvailabilityErrorImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() offline,
    required TResult Function() goingOnline,
    required TResult Function(LocationInfo location) online,
    required TResult Function() goingOffline,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? offline,
    TResult? Function()? goingOnline,
    TResult? Function(LocationInfo location)? online,
    TResult? Function()? goingOffline,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? offline,
    TResult Function()? goingOnline,
    TResult Function(LocationInfo location)? online,
    TResult Function()? goingOffline,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DriverAvailabilityOffline value) offline,
    required TResult Function(DriverAvailabilityGoingOnline value) goingOnline,
    required TResult Function(DriverAvailabilityOnline value) online,
    required TResult Function(DriverAvailabilityGoingOffline value)
    goingOffline,
    required TResult Function(DriverAvailabilityError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DriverAvailabilityOffline value)? offline,
    TResult? Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult? Function(DriverAvailabilityOnline value)? online,
    TResult? Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult? Function(DriverAvailabilityError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DriverAvailabilityOffline value)? offline,
    TResult Function(DriverAvailabilityGoingOnline value)? goingOnline,
    TResult Function(DriverAvailabilityOnline value)? online,
    TResult Function(DriverAvailabilityGoingOffline value)? goingOffline,
    TResult Function(DriverAvailabilityError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class DriverAvailabilityError implements DriverAvailabilityState {
  const factory DriverAvailabilityError({required final String message}) =
      _$DriverAvailabilityErrorImpl;

  String get message;

  /// Create a copy of DriverAvailabilityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverAvailabilityErrorImplCopyWith<_$DriverAvailabilityErrorImpl>
  get copyWith => throw _privateConstructorUsedError;
}
