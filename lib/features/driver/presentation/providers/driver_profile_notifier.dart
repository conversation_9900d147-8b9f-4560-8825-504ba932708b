import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/driver/driver_service.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';
import 'driver_profile_state.dart';

/// Notifier for managing driver profile state
class DriverProfileNotifier extends StateNotifier<DriverProfileState> {
  final DriverService _driverService;

  DriverProfileNotifier(this._driverService)
    : super(const DriverProfileState.initial());

  /// Load driver profile from API
  Future<void> loadDriverProfile() async {
    state = const DriverProfileState.loading();

    try {
      final profile = await _driverService.getDriverProfile();
      state = DriverProfileState.loaded(profile: profile);
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = DriverProfileState.error(message: errorMessage);
    }
  }

  /// Create new driver profile
  Future<void> createDriverProfile(DriverProfileCreate profileData) async {
    state = const DriverProfileState.loading();

    try {
      final profile = await _driverService.createDriverProfile(profileData);
      state = DriverProfileState.loaded(profile: profile);
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = DriverProfileState.error(message: errorMessage);
    }
  }

  /// Update existing driver profile
  Future<void> updateDriverProfile(DriverProfileUpdate profileData) async {
    state = const DriverProfileState.loading();

    try {
      final profile = await _driverService.updateDriverProfile(profileData);
      state = DriverProfileState.loaded(profile: profile);
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      state = DriverProfileState.error(message: errorMessage);
    }
  }

  /// Check if driver has complete profile
  Future<bool> hasCompleteProfile() async {
    try {
      return await _driverService.hasCompleteProfile();
    } catch (e) {
      return false;
    }
  }

  /// Reset state to initial
  void reset() {
    state = const DriverProfileState.initial();
  }

  String _getErrorMessage(dynamic error) {
    if (error is AppError) {
      return error.when(
        network: (message, details) => message,
        authentication: (message, errorCode) => message,
        validation: (message, fieldErrors) => message,
        location: (message, errorType) => message,
        document: (message, errorType) => message,
        server: (message, statusCode) => message,
        unknown: (message, exception) => message,
      );
    }
    return 'An unexpected error occurred';
  }
}
