import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/form_validators.dart';
import '../../../../core/errors/app_error.dart';
import '../../../../shared/models/models.dart';
import '../../../../shared/widgets/error_display_widget.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/form_submission_handler.dart';
import '../providers/driver_providers.dart';
import '../providers/driver_profile_state.dart';
import '../providers/form_providers.dart';

/// Screen for vehicle information input and management
class VehicleSetupScreen extends ConsumerStatefulWidget {
  const VehicleSetupScreen({super.key});

  @override
  ConsumerState<VehicleSetupScreen> createState() => _VehicleSetupScreenState();
}

class _VehicleSetupScreenState extends ConsumerState<VehicleSetupScreen> {
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Initialize form state and load existing vehicle info if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(vehicleFormProvider.notifier).reset();
      ref.read(vehicleInfoProvider.notifier).loadVehicleInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    final vehicleState = ref.watch(vehicleInfoProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Vehicle Information'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vehicle Details',
                style: AppTextStyles.heading2.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please provide your vehicle information for ride requests.',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 24),
              Expanded(
                child: vehicleState.when(
                  initial: () => _buildForm(),
                  loading: () => _buildLoadingState(),
                  loaded: (vehicleInfo) =>
                      _buildForm(existingVehicle: vehicleInfo),
                  error: (message) => _buildErrorState(message),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForm({VehicleInfo? existingVehicle}) {
    final formState = ref.watch(vehicleFormProvider);

    // Pre-fill form if editing existing vehicle
    if (existingVehicle != null && !formState.isUpdateMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(vehicleFormProvider.notifier)
            .loadExistingData(existingVehicle);
      });
    }

    return FormSubmissionHandler(
      isLoading: formState.isLoading,
      error: formState.errorMessage != null
          ? AppError.unknown(message: formState.errorMessage!)
          : null,
      onRetry: () => ref.read(vehicleFormProvider.notifier).clearError(),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildMakeField(),
                    const SizedBox(height: 16),
                    _buildModelField(),
                    const SizedBox(height: 16),
                    _buildYearField(),
                    const SizedBox(height: 16),
                    _buildColorField(),
                    const SizedBox(height: 16),
                    _buildLicensePlateField(),
                    const SizedBox(height: 24),
                    _buildInfoCard(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildSubmitButton(isUpdate: existingVehicle != null),
          ],
        ),
      ),
    );
  }

  Widget _buildMakeField() {
    final formState = ref.watch(vehicleFormProvider);

    return DriverTextField(
      label: 'Vehicle Make',
      hint: 'e.g., Toyota, Honda, Ford',
      value: formState.make,
      onChanged: (value) =>
          ref.read(vehicleFormProvider.notifier).updateMake(value),
      validator: FormValidators.validateVehicleMake,
      errorText: formState.getFieldError('make'),
      prefixIcon: const Icon(Icons.directions_car),
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.words,
    );
  }

  Widget _buildModelField() {
    final formState = ref.watch(vehicleFormProvider);

    return DriverTextField(
      label: 'Vehicle Model',
      hint: 'e.g., Camry, Civic, Focus',
      value: formState.model,
      onChanged: (value) =>
          ref.read(vehicleFormProvider.notifier).updateModel(value),
      validator: FormValidators.validateVehicleModel,
      errorText: formState.getFieldError('model'),
      prefixIcon: const Icon(Icons.car_rental),
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.words,
    );
  }

  Widget _buildYearField() {
    final formState = ref.watch(vehicleFormProvider);

    return DriverTextField(
      label: 'Year',
      hint: 'e.g., 2020',
      value: formState.year,
      onChanged: (value) =>
          ref.read(vehicleFormProvider.notifier).updateYear(value),
      validator: FormValidators.validateVehicleYear,
      errorText: formState.getFieldError('year'),
      prefixIcon: const Icon(Icons.calendar_today),
      keyboardType: TextInputType.number,
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildColorField() {
    final formState = ref.watch(vehicleFormProvider);

    return DriverTextField(
      label: 'Color',
      hint: 'e.g., White, Black, Silver',
      value: formState.color,
      onChanged: (value) =>
          ref.read(vehicleFormProvider.notifier).updateColor(value),
      validator: FormValidators.validateVehicleColor,
      errorText: formState.getFieldError('color'),
      prefixIcon: const Icon(Icons.palette),
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.words,
    );
  }

  Widget _buildLicensePlateField() {
    final formState = ref.watch(vehicleFormProvider);

    return DriverTextField(
      label: 'License Plate',
      hint: 'Enter your license plate number',
      value: formState.licensePlate,
      onChanged: (value) =>
          ref.read(vehicleFormProvider.notifier).updateLicensePlate(value),
      validator: FormValidators.validateLicensePlate,
      errorText: formState.getFieldError('licensePlate'),
      prefixIcon: const Icon(Icons.confirmation_number),
      textInputAction: TextInputAction.done,
      textCapitalization: TextCapitalization.characters,
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Vehicle Requirements',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• Vehicle must be 1990 or newer\n'
            '• All information must match your registration documents\n'
            '• License plate must be clearly visible and readable',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton({required bool isUpdate}) {
    final formState = ref.watch(vehicleFormProvider);

    return FormSubmitButton(
      text: isUpdate ? 'Update Vehicle' : 'Add Vehicle',
      onPressed: _handleSubmit,
      isValid: formState.canSubmit,
      isLoading: formState.isLoading,
      loadingText: isUpdate ? 'Updating vehicle...' : 'Adding vehicle...',
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading vehicle information...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Column(
      children: [
        Expanded(
          child: ErrorDisplayWidget(
            error: AppError.unknown(message: message),
            onRetry: () {
              ref.read(vehicleInfoProvider.notifier).loadVehicleInfo();
            },
          ),
        ),
        const SizedBox(height: 16),
        DriverActionButton(
          text: 'Go Back',
          onPressed: () => Navigator.of(context).pop(),
          isPrimary: false,
        ),
      ],
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    final vehicleState = ref.read(vehicleInfoProvider);
    final isUpdate = vehicleState is VehicleInfoLoaded;

    try {
      if (isUpdate) {
        final updateData = ref
            .read(vehicleFormProvider.notifier)
            .getUpdateData();
        await ref
            .read(vehicleInfoProvider.notifier)
            .updateVehicleInfo(updateData);
      } else {
        final createData = ref
            .read(vehicleFormProvider.notifier)
            .getCreateData();
        await ref.read(vehicleInfoProvider.notifier).addVehicleInfo(createData);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isUpdate
                  ? 'Vehicle updated successfully!'
                  : 'Vehicle added successfully!',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ref
            .read(vehicleFormProvider.notifier)
            .setError('Error: ${e.toString()}');
      }
    }
  }
}
