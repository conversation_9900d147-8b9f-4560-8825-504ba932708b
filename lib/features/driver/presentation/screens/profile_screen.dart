import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/errors/app_error.dart';
import '../../../../shared/models/models.dart';
import '../../../../shared/widgets/error_display_widget.dart';
import '../../../../shared/widgets/availability_toggle_widget.dart';
import '../providers/driver_providers.dart';
import '../providers/driver_profile_state.dart';
import '../providers/driver_availability_notifier.dart';
import 'profile_setup_screen.dart';
import 'vehicle_setup_screen.dart';

/// Screen for viewing and editing driver information
class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // Load profile data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(driverProfileProvider.notifier).loadDriverProfile();
      ref.read(vehicleInfoProvider.notifier).loadVehicleInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(driverProfileProvider);
    final vehicleState = ref.watch(vehicleInfoProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Driver Profile'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AvailabilityToggleWidget(
                  showConfirmationDialog: true,
                  showStatusText: true,
                  isCompact: false,
                ),
                const SizedBox(height: 16),
                _buildProfileSection(profileState),
                const SizedBox(height: 16),
                _buildVehicleSection(vehicleState),
                const SizedBox(height: 16),
                _buildStatsSection(),
                const SizedBox(height: 16),
                _buildActionsSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection(DriverProfileState profileState) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Driver Profile',
                  style: AppTextStyles.heading3.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _navigateToProfileSetup(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            profileState.when(
              initial: () => _buildEmptyState('No profile data'),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded: (profile) => _buildProfileInfo(profile),
              error: (message) => ErrorDisplayWidget(
                error: AppError.unknown(message: message),
                onRetry: () => ref
                    .read(driverProfileProvider.notifier)
                    .loadDriverProfile(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileInfo(DriverProfile profile) {
    return Column(
      children: [
        _buildInfoRow('License Number', profile.licenseNumber),
        if (profile.stripeAccountId != null)
          _buildInfoRow('Stripe Account', profile.stripeAccountId!),
        _buildInfoRow(
          'Verification Status',
          profile.isVerified ? 'Verified' : 'Pending Verification',
        ),
        _buildInfoRow('Total Rides', profile.totalRides.toString()),
        _buildInfoRow('Rating', '${profile.rating.toStringAsFixed(1)} ⭐'),
      ],
    );
  }

  Widget _buildVehicleSection(VehicleInfoState vehicleState) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Vehicle Information',
                  style: AppTextStyles.heading3.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _navigateToVehicleSetup(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            vehicleState.when(
              initial: () => _buildEmptyState('No vehicle information'),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded: (vehicle) => _buildVehicleInfo(vehicle),
              error: (message) => ErrorDisplayWidget(
                error: AppError.unknown(message: message),
                onRetry: () =>
                    ref.read(vehicleInfoProvider.notifier).loadVehicleInfo(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVehicleInfo(VehicleInfo vehicle) {
    return Column(
      children: [
        _buildInfoRow('Make & Model', '${vehicle.make} ${vehicle.model}'),
        _buildInfoRow('Year', vehicle.year.toString()),
        _buildInfoRow('Color', vehicle.color),
        _buildInfoRow('License Plate', vehicle.licensePlate),
      ],
    );
  }

  Widget _buildStatsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistics',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final earningsAsync = ref.watch(driverEarningsProvider);
                final statsAsync = ref.watch(driverStatsProvider);

                return Column(
                  children: [
                    earningsAsync.when(
                      data: (earnings) => _buildInfoRow(
                        'Total Earnings',
                        '\$${earnings.totalEarnings.toStringAsFixed(2)}',
                      ),
                      loading: () => _buildLoadingRow('Total Earnings'),
                      error: (error, stack) =>
                          _buildInfoRow('Total Earnings', 'Error loading'),
                    ),
                    statsAsync.when(
                      data: (stats) => Column(
                        children: [
                          _buildInfoRow(
                            'Completed Rides',
                            stats.totalRides.toString(),
                          ),
                          _buildInfoRow(
                            'Average Rating',
                            '${stats.averageRating.toStringAsFixed(1)} ⭐',
                          ),
                        ],
                      ),
                      loading: () => Column(
                        children: [
                          _buildLoadingRow('Completed Rides'),
                          _buildLoadingRow('Average Rating'),
                        ],
                      ),
                      error: (error, stack) => Column(
                        children: [
                          _buildInfoRow('Completed Rides', 'Error loading'),
                          _buildInfoRow('Average Rating', 'Error loading'),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            _buildActionButton(
              icon: Icons.person_outline,
              title: 'Edit Profile',
              subtitle: 'Update your driver information',
              onTap: _navigateToProfileSetup,
            ),
            const SizedBox(height: 8),
            _buildActionButton(
              icon: Icons.directions_car,
              title: 'Edit Vehicle',
              subtitle: 'Update your vehicle information',
              onTap: _navigateToVehicleSetup,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingRow(String label) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppColors.primary, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.chevron_right, color: AppColors.textSecondary),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          children: [
            Icon(Icons.info_outline, size: 48, color: AppColors.textSecondary),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _refreshData() async {
    await Future.wait<void>([
      ref.read(driverProfileProvider.notifier).loadDriverProfile(),
      ref.read(vehicleInfoProvider.notifier).loadVehicleInfo(),
      ref.read(driverAvailabilityProvider.notifier).initializeAvailability(),
    ]);
  }

  void _navigateToProfileSetup() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ProfileSetupScreen()));
  }

  void _navigateToVehicleSetup() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const VehicleSetupScreen()));
  }
}
