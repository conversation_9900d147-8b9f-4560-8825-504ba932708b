import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/errors/app_error.dart';
import '../../../../shared/models/models.dart';
import '../../../../shared/widgets/error_display_widget.dart';
import '../providers/driver_providers.dart';
import '../providers/earnings_state.dart';
import '../providers/stats_provider.dart';

/// Screen for displaying comprehensive earnings information and statistics
class EarningsScreen extends ConsumerStatefulWidget {
  const EarningsScreen({super.key});

  @override
  ConsumerState<EarningsScreen> createState() => _EarningsScreenState();
}

class _EarningsScreenState extends ConsumerState<EarningsScreen> {
  @override
  void initState() {
    super.initState();
    // Load earnings data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(earningsProvider.notifier).loadEarningsData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final earningsState = ref.watch(earningsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Earnings & Statistics'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshEarnings,
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _refreshEarnings,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildEarningsOverview(earningsState),
                const SizedBox(height: 16),
                _buildStatisticsSection(earningsState),
                const SizedBox(height: 16),
                _buildPerformanceMetrics(earningsState),
                const SizedBox(height: 16),
                _buildEarningsBreakdown(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEarningsOverview(EarningsState state) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Earnings Overview',
                  style: AppTextStyles.heading3.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                if (state.isRefreshing)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            state.when(
              initial: () => _buildEmptyState('No earnings data available'),
              loading: () => _buildLoadingState(),
              loaded: (earnings, stats, lastUpdated) =>
                  _buildEarningsData(earnings),
              error: (message, cachedEarnings, cachedStats) =>
                  cachedEarnings != null
                  ? Column(
                      children: [
                        _buildEarningsData(cachedEarnings),
                        const SizedBox(height: 8),
                        _buildErrorBanner(message),
                      ],
                    )
                  : ErrorDisplayWidget(
                      error: AppError.unknown(message: message),
                      onRetry: () => ref
                          .read(earningsProvider.notifier)
                          .loadEarningsData(),
                    ),
              refreshing: (earnings, stats) => _buildEarningsData(earnings),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEarningsData(EarningsInfo earnings) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildEarningsCard(
                title: 'Total Earnings',
                amount: earnings.totalEarnings,
                currency: earnings.currency ?? 'USD',
                icon: Icons.account_balance_wallet,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildEarningsCard(
                title: 'This Week',
                amount: earnings.weeklyEarnings,
                currency: earnings.currency ?? 'USD',
                icon: Icons.date_range,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildEarningsCard(
                title: 'Today',
                amount: earnings.todayEarnings,
                currency: earnings.currency ?? 'USD',
                icon: Icons.today,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildEarningsCard(
                title: 'Pending',
                amount: earnings.pendingPayouts,
                currency: earnings.currency ?? 'USD',
                icon: Icons.pending,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        if (earnings.lastPayoutDate != null) ...[
          const SizedBox(height: 12),
          _buildInfoRow('Last Payout', _formatDate(earnings.lastPayoutDate!)),
        ],
      ],
    );
  }

  Widget _buildEarningsCard({
    required String title,
    required double amount,
    required String currency,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: AppTextStyles.heading4.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsSection(EarningsState state) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ride Statistics',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            state.when(
              initial: () => _buildEmptyState('No statistics available'),
              loading: () => _buildLoadingState(),
              loaded: (earnings, stats, lastUpdated) =>
                  _buildStatisticsData(earnings, stats),
              error: (message, cachedEarnings, cachedStats) =>
                  cachedStats != null && cachedEarnings != null
                  ? _buildStatisticsData(cachedEarnings, cachedStats)
                  : _buildEmptyState('Statistics unavailable'),
              refreshing: (earnings, stats) =>
                  _buildStatisticsData(earnings, stats),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsData(EarningsInfo earnings, DriverStats stats) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: 'Total Rides',
                value: stats.totalRides.toString(),
                icon: Icons.directions_car,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                title: 'This Week',
                value: earnings.weeklyRides.toString(),
                icon: Icons.date_range,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: 'Today',
                value: earnings.todayRides.toString(),
                icon: Icons.today,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                title: 'Rating',
                value: '${stats.averageRating.toStringAsFixed(1)} ⭐',
                icon: Icons.star,
                color: Colors.amber,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildInfoRow(
          'Completion Rate',
          '${stats.completionRate.toStringAsFixed(1)}%',
        ),
        _buildInfoRow(
          'Acceptance Rate',
          '${stats.acceptanceRate.toStringAsFixed(1)}%',
        ),
        _buildInfoRow('Hours Online', '${stats.hoursOnline} hours'),
        if (stats.lastRideDate != null)
          _buildInfoRow('Last Ride', _formatDate(stats.lastRideDate!)),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.heading4.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics(EarningsState state) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Metrics',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final kpiAsync = ref.watch(kpiProvider);

                if (kpiAsync.isEmpty) {
                  return _buildEmptyState('Performance data unavailable');
                }

                return Column(
                  children: [
                    _buildInfoRow(
                      'Earnings per Hour',
                      '\$${kpiAsync['earnings_per_hour'] ?? '0.00'}',
                    ),
                    _buildInfoRow(
                      'Completed Rides',
                      '${kpiAsync['completed_rides'] ?? 0}',
                    ),
                    _buildInfoRow(
                      'Cancelled Rides',
                      '${kpiAsync['cancelled_rides'] ?? 0}',
                    ),
                    _buildInfoRow(
                      'Total Hours Online',
                      '${kpiAsync['hours_online'] ?? 0} hours',
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEarningsBreakdown() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Earnings Breakdown',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final breakdownAsync = ref.watch(earningsBreakdownProvider);

                return breakdownAsync.when(
                  data: (breakdown) => _buildBreakdownData(breakdown),
                  loading: () => _buildLoadingState(),
                  error: (error, stack) =>
                      _buildEmptyState('Breakdown data unavailable'),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBreakdownData(Map<String, dynamic> breakdown) {
    return Column(
      children: [
        if (breakdown['ride_earnings'] != null)
          _buildInfoRow(
            'Ride Earnings',
            '\$${breakdown['ride_earnings'].toStringAsFixed(2)}',
          ),
        if (breakdown['tips'] != null)
          _buildInfoRow('Tips', '\$${breakdown['tips'].toStringAsFixed(2)}'),
        if (breakdown['bonuses'] != null)
          _buildInfoRow(
            'Bonuses',
            '\$${breakdown['bonuses'].toStringAsFixed(2)}',
          ),
        if (breakdown['surge_earnings'] != null)
          _buildInfoRow(
            'Surge Earnings',
            '\$${breakdown['surge_earnings'].toStringAsFixed(2)}',
          ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          children: [
            Icon(Icons.info_outline, size: 48, color: AppColors.textSecondary),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorBanner(String message) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.warning, color: Colors.red, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Using cached data: $message',
              style: AppTextStyles.bodySmall.copyWith(color: Colors.red),
            ),
          ),
          TextButton(
            onPressed: () => ref.read(earningsProvider.notifier).retry(),
            child: Text(
              'Retry',
              style: AppTextStyles.bodySmall.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _refreshEarnings() async {
    await ref.read(earningsProvider.notifier).refreshEarningsData();
  }
}
