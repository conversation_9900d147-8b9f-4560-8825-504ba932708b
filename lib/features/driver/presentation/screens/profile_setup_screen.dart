import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/form_validators.dart';
import '../../../../core/errors/app_error.dart';
import '../../../../shared/models/models.dart';
import '../../../../shared/widgets/error_display_widget.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/form_submission_handler.dart';
import '../providers/driver_providers.dart';
import '../providers/form_providers.dart';

/// Screen for driver-specific profile completion
class ProfileSetupScreen extends ConsumerStatefulWidget {
  const ProfileSetupScreen({super.key});

  @override
  ConsumerState<ProfileSetupScreen> createState() => _ProfileSetupScreenState();
}

class _ProfileSetupScreenState extends ConsumerState<ProfileSetupScreen> {
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Initialize form state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(driverProfileFormProvider.notifier).reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(driverProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Profile'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Driver Information',
                style: AppTextStyles.heading2.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please provide your driver-specific information to complete your profile.',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 24),
              Expanded(
                child: profileState.when(
                  initial: () => _buildForm(),
                  loading: () => _buildLoadingState(),
                  loaded: (profile) => _buildSuccessState(profile),
                  error: (message) => _buildErrorState(message),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    final formState = ref.watch(driverProfileFormProvider);

    return FormSubmissionHandler(
      isLoading: formState.isLoading,
      error: formState.errorMessage != null
          ? AppError.unknown(message: formState.errorMessage!)
          : null,
      onRetry: () => ref.read(driverProfileFormProvider.notifier).clearError(),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildLicenseNumberField(),
                    const SizedBox(height: 16),
                    _buildStripeAccountField(),
                    const SizedBox(height: 24),
                    _buildInfoCard(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildLicenseNumberField() {
    final formState = ref.watch(driverProfileFormProvider);

    return DriverTextField(
      label: 'Driver\'s License Number',
      hint: 'Enter your license number',
      value: formState.licenseNumber,
      onChanged: (value) => ref
          .read(driverProfileFormProvider.notifier)
          .updateLicenseNumber(value),
      validator: FormValidators.validateLicenseNumber,
      errorText: formState.getFieldError('licenseNumber'),
      prefixIcon: const Icon(Icons.credit_card),
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.characters,
    );
  }

  Widget _buildStripeAccountField() {
    final formState = ref.watch(driverProfileFormProvider);

    return DriverTextField(
      label: 'Stripe Account ID (Optional)',
      hint: 'Enter your Stripe account ID for payments',
      value: formState.stripeAccountId,
      onChanged: (value) => ref
          .read(driverProfileFormProvider.notifier)
          .updateStripeAccountId(value),
      validator: FormValidators.validateStripeAccountId,
      errorText: formState.getFieldError('stripeAccountId'),
      prefixIcon: const Icon(Icons.payment),
      textInputAction: TextInputAction.done,
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Important Information',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• Your driver\'s license number is required for verification\n'
            '• Stripe account is optional but needed for receiving payments\n'
            '• All information will be securely stored and encrypted',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    final formState = ref.watch(driverProfileFormProvider);

    return FormSubmitButton(
      text: 'Complete Profile',
      onPressed: _handleSubmit,
      isValid: formState.canSubmit,
      isLoading: formState.isLoading,
      loadingText: 'Setting up profile...',
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Setting up your profile...'),
        ],
      ),
    );
  }

  Widget _buildSuccessState(DriverProfile profile) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Profile Setup Complete!',
            style: AppTextStyles.heading3.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your driver profile has been successfully created.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          DriverActionButton(
            text: 'Continue',
            onPressed: () => Navigator.of(context).pop(),
            isPrimary: true,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Column(
      children: [
        Expanded(
          child: ErrorDisplayWidget(
            error: AppError.unknown(message: message),
            onRetry: () {
              ref.read(driverProfileProvider.notifier).reset();
            },
          ),
        ),
        const SizedBox(height: 16),
        DriverActionButton(
          text: 'Go Back',
          onPressed: () => Navigator.of(context).pop(),
          isPrimary: false,
        ),
      ],
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    final formState = ref.read(driverProfileFormProvider);

    final profileData = DriverProfileCreate(
      licenseNumber: formState.licenseNumber.trim(),
      stripeAccountId: formState.stripeAccountId.trim().isEmpty
          ? null
          : formState.stripeAccountId.trim(),
    );

    await ref.read(driverProfileFormProvider.notifier).submitForm(profileData);
  }
}
