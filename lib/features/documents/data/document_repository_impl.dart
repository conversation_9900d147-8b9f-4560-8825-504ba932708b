import 'dart:io';
import 'dart:convert';
import '../../../shared/models/models.dart';
import '../../../core/errors/app_error.dart';
import '../../../services/api/api_client.dart';
import '../../../services/storage/storage_service.dart';
import '../domain/document_repository.dart';

/// Implementation of DocumentRepository
/// Handles document verification operations with API integration and local caching
class DocumentRepositoryImpl implements DocumentRepository {
  final ApiClient _apiClient;
  final StorageService _storageService;
  
  static const String _documentsKey = 'cached_documents';
  static const String _verificationStatusKey = 'verification_status';
  
  // Document validation constants
  static const int _maxFileSizeBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> _allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf'];
  
  DocumentRepositoryImpl({
    required ApiClient apiClient,
    required StorageService storageService,
  }) : _apiClient = apiClient,
       _storageService = storageService;

  @override
  Future<VerificationDocument> uploadDocument(
    File file, 
    DocumentType documentType,
    {String? description}
  ) async {
    try {
      // Validate document before upload
      final isValid = await validateDocument(file, documentType);
      if (!isValid) {
        throw const AppError.document(
          message: 'Document validation failed. Please check file size and format.',
          errorType: DocumentErrorType.invalidFormat,
        );
      }
      
      // Upload document
      final response = await _apiClient.uploadFile(
        '/drivers/documents/upload',
        file,
        fieldName: 'document',
        data: {
          'document_type': documentType.name,
          if (description != null) 'description': description,
        },
      );
      
      final document = VerificationDocument.fromJson(response);
      
      // Update cached documents
      await _updateDocumentCache(document);
      
      return document;
    } catch (e) {
      if (e is AppError) rethrow;
      throw _handleError(e);
    }
  }

  @override
  Future<List<VerificationDocument>> getDocuments() async {
    try {
      // Fetch from API
      final response = await _apiClient.get('/drivers/documents');
      final documentsJson = response as List;
      final documents = documentsJson
          .map((json) => VerificationDocument.fromJson(json))
          .toList();
      
      // Update cache
      await _cacheDocuments(documents);
      
      return documents;
    } catch (e) {
      // If API fails, try to return cached data
      final cachedDocuments = await _getCachedDocuments();
      if (cachedDocuments.isNotEmpty) {
        return cachedDocuments;
      }
      
      throw _handleError(e);
    }
  }

  @override
  Future<void> deleteDocument(String documentId) async {
    try {
      await _apiClient.delete('/drivers/documents/$documentId');
      
      // Remove from cache
      await _removeDocumentFromCache(documentId);
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<void> submitVerificationRequest() async {
    try {
      await _apiClient.post('/drivers/verification/request');
      
      // Clear verification status cache to force refresh
      await _storageService.delete(_verificationStatusKey);
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<VerificationStatus> getVerificationStatus() async {
    try {
      final response = await _apiClient.get('/drivers/verification-status');
      final status = VerificationStatus.fromJson(response);
      
      // Cache verification status
      await _cacheVerificationStatus(status);
      
      return status;
    } catch (e) {
      // If API fails, try to return cached data
      final cachedStatus = await _getCachedVerificationStatus();
      if (cachedStatus != null) {
        return cachedStatus;
      }
      
      throw _handleError(e);
    }
  }

  @override
  Future<bool> validateDocument(File file, DocumentType documentType) async {
    try {
      // Check if file exists
      if (!await file.exists()) {
        return false;
      }
      
      // Check file size
      final fileSize = await file.length();
      if (fileSize > _maxFileSizeBytes) {
        return false;
      }
      
      // Check file extension
      final fileName = file.path.toLowerCase();
      final hasValidExtension = _allowedExtensions.any(
        (ext) => fileName.endsWith(ext)
      );
      
      if (!hasValidExtension) {
        return false;
      }
      
      // Additional validation based on document type
      return _validateDocumentType(file, documentType);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> clearDocumentCache() async {
    try {
      await Future.wait([
        _storageService.delete(_documentsKey),
        _storageService.delete(_verificationStatusKey),
      ]);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// Cache documents locally
  Future<void> _cacheDocuments(List<VerificationDocument> documents) async {
    try {
      final documentsJson = documents.map((doc) => doc.toJson()).toList();
      final jsonString = jsonEncode(documentsJson);
      await _storageService.store(_documentsKey, jsonString);
    } catch (e) {
      // Ignore cache errors
    }
  }

  /// Get cached documents
  Future<List<VerificationDocument>> _getCachedDocuments() async {
    try {
      final jsonString = await _storageService.get(_documentsKey);
      if (jsonString == null) return [];
      
      final documentsJson = jsonDecode(jsonString) as List;
      return documentsJson
          .map((json) => VerificationDocument.fromJson(json))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Update document cache with new document
  Future<void> _updateDocumentCache(VerificationDocument newDocument) async {
    try {
      final cachedDocuments = await _getCachedDocuments();
      cachedDocuments.add(newDocument);
      await _cacheDocuments(cachedDocuments);
    } catch (e) {
      // Ignore cache errors
    }
  }

  /// Remove document from cache
  Future<void> _removeDocumentFromCache(String documentId) async {
    try {
      final cachedDocuments = await _getCachedDocuments();
      cachedDocuments.removeWhere((doc) => doc.id == documentId);
      await _cacheDocuments(cachedDocuments);
    } catch (e) {
      // Ignore cache errors
    }
  }

  /// Cache verification status
  Future<void> _cacheVerificationStatus(VerificationStatus status) async {
    try {
      final jsonString = jsonEncode(status.toJson());
      await _storageService.store(_verificationStatusKey, jsonString);
    } catch (e) {
      // Ignore cache errors
    }
  }

  /// Get cached verification status
  Future<VerificationStatus?> _getCachedVerificationStatus() async {
    try {
      final jsonString = await _storageService.get(_verificationStatusKey);
      if (jsonString == null) return null;
      
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return VerificationStatus.fromJson(json);
    } catch (e) {
      return null;
    }
  }

  /// Validate document based on type-specific rules
  bool _validateDocumentType(File file, DocumentType documentType) {
    // Basic validation - can be extended with more specific rules
    switch (documentType) {
      case DocumentType.license:
        // Driver's license should be image format
        return _isImageFile(file);
      case DocumentType.insurance:
        // Insurance can be PDF or image
        return _isImageFile(file) || _isPdfFile(file);
      case DocumentType.registration:
        // Registration can be PDF or image
        return _isImageFile(file) || _isPdfFile(file);
      case DocumentType.vehiclePhoto:
        // Vehicle photo should be image format
        return _isImageFile(file);
    }
  }

  /// Check if file is an image
  bool _isImageFile(File file) {
    final fileName = file.path.toLowerCase();
    return fileName.endsWith('.jpg') || 
           fileName.endsWith('.jpeg') || 
           fileName.endsWith('.png');
  }

  /// Check if file is a PDF
  bool _isPdfFile(File file) {
    return file.path.toLowerCase().endsWith('.pdf');
  }

  /// Handle and transform errors into appropriate AppError types
  AppError _handleError(dynamic error) {
    if (error is AppError) {
      return error;
    }
    
    // Handle different types of errors and convert to AppError
    if (error.toString().contains('network') || 
        error.toString().contains('connection')) {
      return const AppError.network(
        message: 'Network connection failed. Please check your internet connection.',
      );
    }
    
    if (error.toString().contains('401') || 
        error.toString().contains('unauthorized')) {
      return const AppError.authentication(
        message: 'Authentication failed. Please login again.',
        errorCode: '401',
      );
    }
    
    if (error.toString().contains('413') || 
        error.toString().contains('file too large')) {
      return const AppError.document(
        message: 'File size exceeds the maximum limit of 5MB.',
        errorType: DocumentErrorType.fileTooLarge,
      );
    }
    
    if (error.toString().contains('415') || 
        error.toString().contains('unsupported media')) {
      return const AppError.document(
        message: 'Unsupported file format. Please use JPG, PNG, or PDF.',
        errorType: DocumentErrorType.invalidFormat,
      );
    }
    
    if (error.toString().contains('upload')) {
      return const AppError.document(
        message: 'Document upload failed. Please try again.',
        errorType: DocumentErrorType.uploadFailed,
      );
    }
    
    return AppError.unknown(
      message: 'An unexpected error occurred.',
      exception: error,
    );
  }
}