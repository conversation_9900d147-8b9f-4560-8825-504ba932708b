import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/errors/app_error.dart';
import '../../../../shared/models/models.dart';
import '../../../../shared/widgets/error_display_widget.dart';
import '../providers/document_providers.dart';
import '../providers/document_state.dart';

/// Screen for displaying verification progress and status
class VerificationStatusScreen extends ConsumerStatefulWidget {
  const VerificationStatusScreen({super.key});

  @override
  ConsumerState<VerificationStatusScreen> createState() =>
      _VerificationStatusScreenState();
}

class _VerificationStatusScreenState
    extends ConsumerState<VerificationStatusScreen> {
  @override
  void initState() {
    super.initState();
    // Load verification status when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(verificationStatusProvider.notifier).loadVerificationStatus();
    });
  }

  @override
  Widget build(BuildContext context) {
    final verificationState = ref.watch(verificationStatusProvider);
    final verificationProgress = ref.watch(verificationProgressProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verification Status'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref
                .read(verificationStatusProvider.notifier)
                .refreshVerificationStatus(),
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () => ref
              .read(verificationStatusProvider.notifier)
              .refreshVerificationStatus(),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProgressCard(verificationProgress),
                const SizedBox(height: 16),
                _buildStatusCard(verificationState),
                const SizedBox(height: 16),
                _buildDocumentsStatusCard(verificationState),
                const SizedBox(height: 16),
                _buildTimelineCard(verificationState),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressCard(double progress) {
    final progressPercentage = (progress * 100).toInt();

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verification Progress',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      progress == 1.0 ? Colors.green : AppColors.primary,
                    ),
                    minHeight: 8,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '$progressPercentage%',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              progress == 1.0
                  ? 'All documents approved'
                  : '$progressPercentage% of documents approved',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(VerificationStatusState verificationState) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Status',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            verificationState.when(
              initial: () => _buildStatusInfo(
                status: 'Not Started',
                message: 'Upload documents to begin verification',
                icon: Icons.help_outline,
                color: Colors.grey,
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded: (status) => _buildVerificationStatusInfo(status),
              error: (message) => ErrorDisplayWidget(
                error: AppError.unknown(message: message),
                onRetry: () => ref
                    .read(verificationStatusProvider.notifier)
                    .loadVerificationStatus(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationStatusInfo(VerificationStatus status) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (status.status.toLowerCase()) {
      case 'approved':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'APPROVED';
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusText = 'REJECTED';
        break;
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = 'PENDING REVIEW';
        break;
      case 'under_review':
        statusColor = Colors.blue;
        statusIcon = Icons.visibility;
        statusText = 'UNDER REVIEW';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help_outline;
        statusText = status.status.toUpperCase();
    }

    return _buildStatusInfo(
      status: statusText,
      message: status.message ?? _getDefaultStatusMessage(status.status),
      icon: statusIcon,
      color: statusColor,
      submittedAt: status.submittedAt,
      reviewedAt: status.reviewedAt,
    );
  }

  Widget _buildStatusInfo({
    required String status,
    required String message,
    required IconData icon,
    required Color color,
    DateTime? submittedAt,
    DateTime? reviewedAt,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    status,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  Text(
                    message,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        if (submittedAt != null || reviewedAt != null) ...[
          const SizedBox(height: 12),
          if (submittedAt != null) _buildDateInfo('Submitted', submittedAt),
          if (reviewedAt != null) _buildDateInfo('Reviewed', reviewedAt),
        ],
      ],
    );
  }

  Widget _buildDateInfo(String label, DateTime date) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        children: [
          Icon(Icons.schedule, size: 16, color: AppColors.textSecondary),
          const SizedBox(width: 8),
          Text(
            '$label: ${_formatDateTime(date)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsStatusCard(VerificationStatusState verificationState) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Documents Status',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            verificationState.when(
              initial: () => _buildEmptyState('No documents to review'),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded: (status) => _buildDocumentsList(status.documents),
              error: (message) => Text(
                'Error loading documents: $message',
                style: AppTextStyles.bodyMedium.copyWith(color: Colors.red),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentsList(List<VerificationDocument> documents) {
    if (documents.isEmpty) {
      return _buildEmptyState('No documents uploaded yet');
    }

    return Column(
      children: documents
          .map((document) => _buildDocumentStatusItem(document))
          .toList(),
    );
  }

  Widget _buildDocumentStatusItem(VerificationDocument document) {
    Color statusColor;
    IconData statusIcon;

    switch (document.status) {
      case DocumentStatus.approved:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case DocumentStatus.rejected:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      case DocumentStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(_getDocumentTypeIcon(document.documentType), size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getDocumentTypeName(document.documentType),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(statusIcon, color: statusColor, size: 14),
                    const SizedBox(width: 4),
                    Text(
                      document.status.name.toUpperCase(),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: statusColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Uploaded: ${_formatDate(document.uploadedAt)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          if (document.reviewedAt != null) ...[
            Text(
              'Reviewed: ${_formatDate(document.reviewedAt!)}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
          if (document.rejectionReason != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Rejection Reason: ${document.rejectionReason}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimelineCard(VerificationStatusState verificationState) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verification Timeline',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            verificationState.when(
              initial: () => _buildTimelineStep(
                title: 'Upload Documents',
                description: 'Upload all required documents',
                isCompleted: false,
                isActive: true,
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded: (status) => _buildTimeline(status),
              error: (message) => Text(
                'Error loading timeline: $message',
                style: AppTextStyles.bodyMedium.copyWith(color: Colors.red),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeline(VerificationStatus status) {
    final hasDocuments = status.documents.isNotEmpty;
    final isSubmitted = status.submittedAt != null;
    final isReviewed = status.reviewedAt != null;
    final isApproved = status.status.toLowerCase() == 'approved';

    return Column(
      children: [
        _buildTimelineStep(
          title: 'Documents Uploaded',
          description: 'All required documents have been uploaded',
          isCompleted: hasDocuments,
          isActive: !hasDocuments,
        ),
        _buildTimelineStep(
          title: 'Submitted for Review',
          description: 'Documents submitted to admin for verification',
          isCompleted: isSubmitted,
          isActive: hasDocuments && !isSubmitted,
        ),
        _buildTimelineStep(
          title: 'Under Review',
          description: 'Admin is reviewing your documents',
          isCompleted: isReviewed,
          isActive: isSubmitted && !isReviewed,
        ),
        _buildTimelineStep(
          title: 'Verification Complete',
          description: isApproved
              ? 'Your documents have been approved'
              : 'Verification process completed',
          isCompleted: isApproved,
          isActive: isReviewed && !isApproved,
          isLast: true,
        ),
      ],
    );
  }

  Widget _buildTimelineStep({
    required String title,
    required String description,
    required bool isCompleted,
    required bool isActive,
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isCompleted
                    ? Colors.green
                    : isActive
                    ? AppColors.primary
                    : Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
              child: Icon(
                isCompleted ? Icons.check : Icons.circle,
                color: Colors.white,
                size: 16,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: isCompleted ? Colors.green : Colors.grey.shade300,
              ),
          ],
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isCompleted || isActive
                        ? AppColors.textPrimary
                        : AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          children: [
            Icon(Icons.description, size: 48, color: AppColors.textSecondary),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _getDocumentTypeName(DocumentType type) {
    switch (type) {
      case DocumentType.license:
        return 'Driver\'s License';
      case DocumentType.insurance:
        return 'Vehicle Insurance';
      case DocumentType.registration:
        return 'Vehicle Registration';
      case DocumentType.vehiclePhoto:
        return 'Vehicle Photo';
    }
  }

  IconData _getDocumentTypeIcon(DocumentType type) {
    switch (type) {
      case DocumentType.license:
        return Icons.badge;
      case DocumentType.insurance:
        return Icons.security;
      case DocumentType.registration:
        return Icons.description;
      case DocumentType.vehiclePhoto:
        return Icons.directions_car;
    }
  }

  String _getDefaultStatusMessage(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'Congratulations! Your documents have been approved.';
      case 'rejected':
        return 'Some documents were rejected. Please check and re-upload.';
      case 'pending':
        return 'Your documents are waiting for admin review.';
      case 'under_review':
        return 'Admin is currently reviewing your documents.';
      default:
        return 'Status information not available.';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
