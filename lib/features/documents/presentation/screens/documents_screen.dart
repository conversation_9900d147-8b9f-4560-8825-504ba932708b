import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/errors/app_error.dart';
import '../../../../shared/models/models.dart';
import '../../../../shared/widgets/error_display_widget.dart';
import '../providers/document_providers.dart';
import '../providers/document_state.dart';
import 'document_upload_screen.dart';
import 'verification_status_screen.dart';

/// Screen for viewing and managing uploaded documents
class DocumentsScreen extends ConsumerStatefulWidget {
  const DocumentsScreen({super.key});

  @override
  ConsumerState<DocumentsScreen> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends ConsumerState<DocumentsScreen> {
  @override
  void initState() {
    super.initState();
    // Load documents when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(documentsProvider.notifier).loadDocuments();
      ref.read(verificationStatusProvider.notifier).loadVerificationStatus();
    });
  }

  @override
  Widget build(BuildContext context) {
    final documentsState = ref.watch(documentsProvider);
    final verificationState = ref.watch(verificationStatusProvider);
    final hasAllRequired = ref.watch(hasAllRequiredDocumentsProvider);
    final missingTypes = ref.watch(missingDocumentTypesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Documents'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _navigateToVerificationStatus(),
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildVerificationStatusCard(verificationState),
                const SizedBox(height: 16),
                _buildRequiredDocumentsCard(hasAllRequired, missingTypes),
                const SizedBox(height: 16),
                _buildDocumentsSection(documentsState),
                const SizedBox(height: 16),
                _buildActionsSection(hasAllRequired),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToDocumentUpload(),
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildVerificationStatusCard(
    VerificationStatusState verificationState,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Verification Status',
                  style: AppTextStyles.heading3.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.visibility),
                  onPressed: () => _navigateToVerificationStatus(),
                ),
              ],
            ),
            const SizedBox(height: 8),
            verificationState.when(
              initial: () => _buildStatusRow(
                icon: Icons.help_outline,
                text: 'Not checked yet',
                color: Colors.grey,
              ),
              loading: () => _buildStatusRow(
                icon: Icons.hourglass_empty,
                text: 'Loading status...',
                color: Colors.orange,
              ),
              loaded: (status) => _buildVerificationStatusInfo(status),
              error: (message) => _buildStatusRow(
                icon: Icons.error,
                text: 'Error: $message',
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationStatusInfo(VerificationStatus status) {
    Color statusColor;
    IconData statusIcon;

    switch (status.status.toLowerCase()) {
      case 'approved':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      case 'pending':
      case 'under_review':
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help_outline;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStatusRow(
          icon: statusIcon,
          text: status.status.replaceAll('_', ' ').toUpperCase(),
          color: statusColor,
        ),
        if (status.message != null) ...[
          const SizedBox(height: 8),
          Text(
            status.message!,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
        if (status.submittedAt != null) ...[
          const SizedBox(height: 4),
          Text(
            'Submitted: ${_formatDate(status.submittedAt!)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildRequiredDocumentsCard(
    bool hasAllRequired,
    List<DocumentType> missingTypes,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Required Documents',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            if (hasAllRequired) ...[
              _buildStatusRow(
                icon: Icons.check_circle,
                text: 'All required documents uploaded',
                color: Colors.green,
              ),
            ] else ...[
              _buildStatusRow(
                icon: Icons.warning,
                text: '${missingTypes.length} documents missing',
                color: Colors.orange,
              ),
              const SizedBox(height: 8),
              ...missingTypes.map(
                (type) => Padding(
                  padding: const EdgeInsets.only(left: 24.0, bottom: 4.0),
                  child: Text(
                    '• ${_getDocumentTypeName(type)}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentsSection(DocumentsState documentsState) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Uploaded Documents',
                  style: AppTextStyles.heading3.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                Consumer(
                  builder: (context, ref, child) {
                    final documentCount = ref.watch(documentCountProvider);
                    return Text(
                      '$documentCount documents',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            documentsState.when(
              initial: () => _buildEmptyState('No documents uploaded yet'),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded: (documents) => documents.isEmpty
                  ? _buildEmptyState('No documents uploaded yet')
                  : _buildDocumentsList(documents),
              error: (message) => ErrorDisplayWidget(
                error: AppError.unknown(message: message),
                onRetry: () =>
                    ref.read(documentsProvider.notifier).loadDocuments(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentsList(List<VerificationDocument> documents) {
    return Column(
      children: documents
          .map((document) => _buildDocumentItem(document))
          .toList(),
    );
  }

  Widget _buildDocumentItem(VerificationDocument document) {
    Color statusColor;
    IconData statusIcon;

    switch (document.status) {
      case DocumentStatus.approved:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case DocumentStatus.rejected:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      case DocumentStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getDocumentTypeIcon(document.documentType),
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getDocumentTypeName(document.documentType),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(statusIcon, color: statusColor, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      document.status.name.toUpperCase(),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: statusColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Text(
                  'Uploaded: ${_formatDate(document.uploadedAt)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                if (document.rejectionReason != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Reason: ${document.rejectionReason}',
                    style: AppTextStyles.bodySmall.copyWith(color: Colors.red),
                  ),
                ],
              ],
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleDocumentAction(value, document),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'view',
                child: Row(
                  children: [
                    Icon(Icons.visibility),
                    SizedBox(width: 8),
                    Text('View'),
                  ],
                ),
              ),
              if (document.status == DocumentStatus.rejected)
                const PopupMenuItem(
                  value: 'reupload',
                  child: Row(
                    children: [
                      Icon(Icons.upload),
                      SizedBox(width: 8),
                      Text('Re-upload'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionsSection(bool hasAllRequired) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions',
              style: AppTextStyles.heading3.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            _buildActionButton(
              icon: Icons.upload_file,
              title: 'Upload Document',
              subtitle: 'Add a new verification document',
              onTap: () => _navigateToDocumentUpload(),
            ),
            const SizedBox(height: 8),
            _buildActionButton(
              icon: Icons.info,
              title: 'Verification Status',
              subtitle: 'View detailed verification progress',
              onTap: () => _navigateToVerificationStatus(),
            ),
            if (hasAllRequired) ...[
              const SizedBox(height: 8),
              Consumer(
                builder: (context, ref, child) {
                  final isVerificationComplete = ref.watch(
                    isVerificationCompleteProvider,
                  );
                  final isVerificationPending = ref.watch(
                    isVerificationPendingProvider,
                  );

                  if (isVerificationComplete) {
                    return _buildActionButton(
                      icon: Icons.check_circle,
                      title: 'Verification Complete',
                      subtitle: 'Your documents have been verified',
                      onTap: null,
                      color: Colors.green,
                    );
                  } else if (isVerificationPending) {
                    return _buildActionButton(
                      icon: Icons.hourglass_empty,
                      title: 'Under Review',
                      subtitle: 'Your documents are being reviewed',
                      onTap: null,
                      color: Colors.orange,
                    );
                  } else {
                    return _buildActionButton(
                      icon: Icons.send,
                      title: 'Submit for Verification',
                      subtitle: 'Submit all documents for review',
                      onTap: _submitForVerification,
                      color: AppColors.primary,
                    );
                  }
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
    Color? color,
  }) {
    final effectiveColor = color ?? AppColors.primary;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: effectiveColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: effectiveColor, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(Icons.chevron_right, color: AppColors.textSecondary),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.bodySmall.copyWith(color: color),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          children: [
            Icon(Icons.description, size: 48, color: AppColors.textSecondary),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _getDocumentTypeName(DocumentType type) {
    switch (type) {
      case DocumentType.license:
        return 'Driver\'s License';
      case DocumentType.insurance:
        return 'Vehicle Insurance';
      case DocumentType.registration:
        return 'Vehicle Registration';
      case DocumentType.vehiclePhoto:
        return 'Vehicle Photo';
    }
  }

  IconData _getDocumentTypeIcon(DocumentType type) {
    switch (type) {
      case DocumentType.license:
        return Icons.badge;
      case DocumentType.insurance:
        return Icons.security;
      case DocumentType.registration:
        return Icons.description;
      case DocumentType.vehiclePhoto:
        return Icons.directions_car;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _refreshData() async {
    await Future.wait([
      ref.read(documentsProvider.notifier).refreshDocuments(),
      ref.read(verificationStatusProvider.notifier).refreshVerificationStatus(),
    ]);
  }

  void _navigateToDocumentUpload() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const DocumentUploadScreen()),
    );
  }

  void _navigateToVerificationStatus() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const VerificationStatusScreen()),
    );
  }

  Future<void> _submitForVerification() async {
    final success = await ref
        .read(verificationStatusProvider.notifier)
        .submitForVerification();

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Documents submitted for verification'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _handleDocumentAction(String action, VerificationDocument document) {
    switch (action) {
      case 'view':
        _viewDocument(document);
        break;
      case 'reupload':
        _reuploadDocument(document);
        break;
      case 'delete':
        _deleteDocument(document);
        break;
    }
  }

  void _viewDocument(VerificationDocument document) {
    // TODO: Implement document viewing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Document viewing not implemented yet')),
    );
  }

  void _reuploadDocument(VerificationDocument document) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DocumentUploadScreen(
          documentType: document.documentType,
          isReupload: true,
        ),
      ),
    );
  }

  Future<void> _deleteDocument(VerificationDocument document) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Document'),
        content: Text(
          'Are you sure you want to delete this ${_getDocumentTypeName(document.documentType).toLowerCase()}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(documentServiceProvider).deleteDocument(document.id);
        ref.read(documentsProvider.notifier).removeDocument(document.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Document deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete document: $error'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
