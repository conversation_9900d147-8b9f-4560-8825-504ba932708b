import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../services/documents/document_service.dart';
import '../../../../shared/models/models.dart';
import 'document_state.dart';
import 'documents_notifier.dart';
import 'document_upload_notifier.dart';
import 'verification_status_notifier.dart';

/// Provider for DocumentService
final documentServiceProvider = Provider<DocumentService>((ref) {
  return getIt<DocumentService>();
});

/// Provider for document list state management
final documentsProvider =
    StateNotifierProvider<DocumentsNotifier, DocumentsState>((ref) {
      final documentService = ref.read(documentServiceProvider);
      return DocumentsNotifier(documentService);
    });

/// Provider for document upload state management
final documentUploadProvider =
    StateNotifierProvider<DocumentUploadNotifier, DocumentUploadState>((ref) {
      final documentService = ref.read(documentServiceProvider);
      return DocumentUploadNotifier(documentService);
    });

/// Provider for verification status state management
final verificationStatusProvider =
    StateNotifierProvider<VerificationStatusNotifier, VerificationStatusState>((
      ref,
    ) {
      final documentService = ref.read(documentServiceProvider);
      return VerificationStatusNotifier(documentService);
    });

/// Provider for current document list (computed from state)
final currentDocumentsProvider = Provider<List<VerificationDocument>>((ref) {
  final documentsState = ref.watch(documentsProvider);
  return documentsState.maybeWhen(
    loaded: (documents) => documents,
    orElse: () => [],
  );
});

/// Provider for current verification status (computed from state)
final currentVerificationStatusProvider = Provider<VerificationStatus?>((ref) {
  final statusState = ref.watch(verificationStatusProvider);
  return statusState.maybeWhen(loaded: (status) => status, orElse: () => null);
});

/// Provider to check if documents are loading
final documentsLoadingProvider = Provider<bool>((ref) {
  final documentsState = ref.watch(documentsProvider);
  return documentsState.maybeWhen(loading: () => true, orElse: () => false);
});

/// Provider to check if verification status is loading
final verificationStatusLoadingProvider = Provider<bool>((ref) {
  final statusState = ref.watch(verificationStatusProvider);
  return statusState.maybeWhen(loading: () => true, orElse: () => false);
});

/// Provider to check if upload is in progress
final isUploadingProvider = Provider<bool>((ref) {
  final uploadState = ref.watch(documentUploadProvider);
  return uploadState.maybeWhen(uploading: (_, __) => true, orElse: () => false);
});

/// Provider for upload progress
final uploadProgressProvider = Provider<double>((ref) {
  final uploadState = ref.watch(documentUploadProvider);
  return uploadState.maybeWhen(
    uploading: (_, progress) => progress,
    orElse: () => 0.0,
  );
});

/// Provider to check if all required documents are uploaded
final hasAllRequiredDocumentsProvider = Provider<bool>((ref) {
  final documents = ref.watch(currentDocumentsProvider);
  final requiredTypes = [
    DocumentType.license,
    DocumentType.insurance,
    DocumentType.registration,
    DocumentType.vehiclePhoto,
  ];

  final uploadedTypes = documents.map((doc) => doc.documentType).toSet();
  return requiredTypes.every((type) => uploadedTypes.contains(type));
});

/// Provider for missing document types
final missingDocumentTypesProvider = Provider<List<DocumentType>>((ref) {
  final documents = ref.watch(currentDocumentsProvider);
  final requiredTypes = [
    DocumentType.license,
    DocumentType.insurance,
    DocumentType.registration,
    DocumentType.vehiclePhoto,
  ];

  final uploadedTypes = documents.map((doc) => doc.documentType).toSet();
  return requiredTypes.where((type) => !uploadedTypes.contains(type)).toList();
});

/// Provider for document count
final documentCountProvider = Provider<int>((ref) {
  final documents = ref.watch(currentDocumentsProvider);
  return documents.length;
});

/// Provider to check if verification is complete
final isVerificationCompleteProvider = Provider<bool>((ref) {
  final status = ref.watch(currentVerificationStatusProvider);
  return status?.isComplete ?? false;
});

/// Provider to check if verification is approved
final isVerificationApprovedProvider = Provider<bool>((ref) {
  final status = ref.watch(currentVerificationStatusProvider);
  return status?.status.toLowerCase() == 'approved';
});

/// Provider to check if verification is pending
final isVerificationPendingProvider = Provider<bool>((ref) {
  final status = ref.watch(currentVerificationStatusProvider);
  return status?.status.toLowerCase() == 'pending';
});

/// Provider to check if verification is rejected
final isVerificationRejectedProvider = Provider<bool>((ref) {
  final status = ref.watch(currentVerificationStatusProvider);
  return status?.status.toLowerCase() == 'rejected';
});

/// Provider for documents by type
final documentsOfTypeProvider =
    Provider.family<List<VerificationDocument>, DocumentType>((ref, type) {
      final documents = ref.watch(currentDocumentsProvider);
      return documents.where((doc) => doc.documentType == type).toList();
    });

/// Provider to check if a specific document type exists
final hasDocumentTypeProvider = Provider.family<bool, DocumentType>((
  ref,
  type,
) {
  final documents = ref.watch(currentDocumentsProvider);
  return documents.any((doc) => doc.documentType == type);
});

/// Provider for approved documents
final approvedDocumentsProvider = Provider<List<VerificationDocument>>((ref) {
  final documents = ref.watch(currentDocumentsProvider);
  return documents
      .where((doc) => doc.status == DocumentStatus.approved)
      .toList();
});

/// Provider for pending documents
final pendingDocumentsProvider = Provider<List<VerificationDocument>>((ref) {
  final documents = ref.watch(currentDocumentsProvider);
  return documents
      .where((doc) => doc.status == DocumentStatus.pending)
      .toList();
});

/// Provider for rejected documents
final rejectedDocumentsProvider = Provider<List<VerificationDocument>>((ref) {
  final documents = ref.watch(currentDocumentsProvider);
  return documents
      .where((doc) => doc.status == DocumentStatus.rejected)
      .toList();
});

/// Provider for verification progress (0.0 to 1.0)
final verificationProgressProvider = Provider<double>((ref) {
  final documents = ref.watch(currentDocumentsProvider);
  if (documents.isEmpty) return 0.0;

  final approvedCount = documents
      .where((doc) => doc.status == DocumentStatus.approved)
      .length;
  return approvedCount / documents.length;
});

/// Provider to check if any documents are rejected
final hasRejectedDocumentsProvider = Provider<bool>((ref) {
  final documents = ref.watch(currentDocumentsProvider);
  return documents.any((doc) => doc.status == DocumentStatus.rejected);
});

/// Provider for rejection reasons
final rejectionReasonsProvider = Provider<Map<DocumentType, String>>((ref) {
  final rejectedDocs = ref.watch(rejectedDocumentsProvider);
  final reasons = <DocumentType, String>{};

  for (final doc in rejectedDocs) {
    if (doc.rejectionReason != null) {
      reasons[doc.documentType] = doc.rejectionReason!;
    }
  }

  return reasons;
});
