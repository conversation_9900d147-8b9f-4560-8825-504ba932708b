import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/documents/document_service.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';
import 'document_state.dart';

/// Notifier for managing document upload state
/// Handles document upload progress and status
class DocumentUploadNotifier extends StateNotifier<DocumentUploadState> {
  final DocumentService _documentService;

  DocumentUploadNotifier(this._documentService)
    : super(const DocumentUploadState.initial());

  /// Upload a document with progress tracking
  Future<VerificationDocument?> uploadDocument(
    File file,
    DocumentType documentType, {
    String? description,
  }) async {
    // Set uploading state with 0% progress
    state = DocumentUploadState.uploading(
      documentType: documentType,
      progress: 0.0,
    );

    try {
      // Simulate progress updates (in real implementation, this would come from the API client)
      _updateProgress(documentType, 0.1);

      // Upload the document
      final document = await _documentService.uploadDocument(
        file,
        documentType,
        description: description,
      );

      // Complete progress
      _updateProgress(documentType, 1.0);

      // Set success state
      state = DocumentUploadState.success(document: document);

      // Reset to initial state after a short delay
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          state = const DocumentUploadState.initial();
        }
      });

      return document;
    } catch (error) {
      final message = _getErrorMessage(error);
      state = DocumentUploadState.error(
        message: message,
        documentType: documentType,
      );

      // Reset to initial state after error display
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          state = const DocumentUploadState.initial();
        }
      });

      return null;
    }
  }

  /// Update upload progress
  void _updateProgress(DocumentType documentType, double progress) {
    // Only update if currently uploading the same document type
    state.maybeWhen(
      uploading: (type, currentProgress) {
        if (type == documentType) {
          state = DocumentUploadState.uploading(
            documentType: documentType,
            progress: progress,
          );
        }
      },
      orElse: () {},
    );
  }

  /// Reset the upload state
  void resetState() {
    state = const DocumentUploadState.initial();
  }

  /// Check if currently uploading
  bool get isUploading {
    return state.maybeWhen(uploading: (_, __) => true, orElse: () => false);
  }

  /// Get current upload progress (0.0 to 1.0)
  double get uploadProgress {
    return state.maybeWhen(
      uploading: (_, progress) => progress,
      orElse: () => 0.0,
    );
  }

  /// Get the document type currently being uploaded
  DocumentType? get uploadingDocumentType {
    return state.maybeWhen(uploading: (type, _) => type, orElse: () => null);
  }

  /// Check if upload was successful
  bool get isUploadSuccessful {
    return state.maybeWhen(success: (_) => true, orElse: () => false);
  }

  /// Get the successfully uploaded document
  VerificationDocument? get uploadedDocument {
    return state.maybeWhen(success: (document) => document, orElse: () => null);
  }

  /// Check if there's an upload error
  bool get hasUploadError {
    return state.maybeWhen(error: (_, __) => true, orElse: () => false);
  }

  /// Get the upload error message
  String? get uploadErrorMessage {
    return state.maybeWhen(error: (message, _) => message, orElse: () => null);
  }

  /// Extract error message from various error types
  String _getErrorMessage(dynamic error) {
    if (error is AppError) {
      return error.when(
        network: (message, details) => 'Network error: $message',
        authentication: (message, errorCode) =>
            'Authentication error: $message',
        validation: (message, fieldErrors) => 'Validation error: $message',
        location: (message, errorType) => 'Location error: $message',
        document: (message, errorType) {
          switch (errorType) {
            case DocumentErrorType.uploadFailed:
              return 'Upload failed: $message';
            case DocumentErrorType.invalidFormat:
              return 'Invalid file format: $message';
            case DocumentErrorType.fileTooLarge:
              return 'File too large: $message';
            case DocumentErrorType.compressionFailed:
              return 'Compression failed: $message';
          }
        },
        server: (message, statusCode) => 'Server error ($statusCode): $message',
        unknown: (message, exception) => 'Unknown error: $message',
      );
    }
    return 'Upload failed: ${error.toString()}';
  }
}
