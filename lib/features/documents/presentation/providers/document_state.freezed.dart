// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'document_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$DocumentsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<VerificationDocument> documents) loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<VerificationDocument> documents)? loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<VerificationDocument> documents)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentsInitial value) initial,
    required TResult Function(DocumentsLoading value) loading,
    required TResult Function(DocumentsLoaded value) loaded,
    required TResult Function(DocumentsError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentsInitial value)? initial,
    TResult? Function(DocumentsLoading value)? loading,
    TResult? Function(DocumentsLoaded value)? loaded,
    TResult? Function(DocumentsError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentsInitial value)? initial,
    TResult Function(DocumentsLoading value)? loading,
    TResult Function(DocumentsLoaded value)? loaded,
    TResult Function(DocumentsError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentsStateCopyWith<$Res> {
  factory $DocumentsStateCopyWith(
    DocumentsState value,
    $Res Function(DocumentsState) then,
  ) = _$DocumentsStateCopyWithImpl<$Res, DocumentsState>;
}

/// @nodoc
class _$DocumentsStateCopyWithImpl<$Res, $Val extends DocumentsState>
    implements $DocumentsStateCopyWith<$Res> {
  _$DocumentsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DocumentsInitialImplCopyWith<$Res> {
  factory _$$DocumentsInitialImplCopyWith(
    _$DocumentsInitialImpl value,
    $Res Function(_$DocumentsInitialImpl) then,
  ) = __$$DocumentsInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DocumentsInitialImplCopyWithImpl<$Res>
    extends _$DocumentsStateCopyWithImpl<$Res, _$DocumentsInitialImpl>
    implements _$$DocumentsInitialImplCopyWith<$Res> {
  __$$DocumentsInitialImplCopyWithImpl(
    _$DocumentsInitialImpl _value,
    $Res Function(_$DocumentsInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DocumentsInitialImpl implements DocumentsInitial {
  const _$DocumentsInitialImpl();

  @override
  String toString() {
    return 'DocumentsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DocumentsInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<VerificationDocument> documents) loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<VerificationDocument> documents)? loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<VerificationDocument> documents)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentsInitial value) initial,
    required TResult Function(DocumentsLoading value) loading,
    required TResult Function(DocumentsLoaded value) loaded,
    required TResult Function(DocumentsError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentsInitial value)? initial,
    TResult? Function(DocumentsLoading value)? loading,
    TResult? Function(DocumentsLoaded value)? loaded,
    TResult? Function(DocumentsError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentsInitial value)? initial,
    TResult Function(DocumentsLoading value)? loading,
    TResult Function(DocumentsLoaded value)? loaded,
    TResult Function(DocumentsError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class DocumentsInitial implements DocumentsState {
  const factory DocumentsInitial() = _$DocumentsInitialImpl;
}

/// @nodoc
abstract class _$$DocumentsLoadingImplCopyWith<$Res> {
  factory _$$DocumentsLoadingImplCopyWith(
    _$DocumentsLoadingImpl value,
    $Res Function(_$DocumentsLoadingImpl) then,
  ) = __$$DocumentsLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DocumentsLoadingImplCopyWithImpl<$Res>
    extends _$DocumentsStateCopyWithImpl<$Res, _$DocumentsLoadingImpl>
    implements _$$DocumentsLoadingImplCopyWith<$Res> {
  __$$DocumentsLoadingImplCopyWithImpl(
    _$DocumentsLoadingImpl _value,
    $Res Function(_$DocumentsLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DocumentsLoadingImpl implements DocumentsLoading {
  const _$DocumentsLoadingImpl();

  @override
  String toString() {
    return 'DocumentsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DocumentsLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<VerificationDocument> documents) loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<VerificationDocument> documents)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<VerificationDocument> documents)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentsInitial value) initial,
    required TResult Function(DocumentsLoading value) loading,
    required TResult Function(DocumentsLoaded value) loaded,
    required TResult Function(DocumentsError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentsInitial value)? initial,
    TResult? Function(DocumentsLoading value)? loading,
    TResult? Function(DocumentsLoaded value)? loaded,
    TResult? Function(DocumentsError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentsInitial value)? initial,
    TResult Function(DocumentsLoading value)? loading,
    TResult Function(DocumentsLoaded value)? loaded,
    TResult Function(DocumentsError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class DocumentsLoading implements DocumentsState {
  const factory DocumentsLoading() = _$DocumentsLoadingImpl;
}

/// @nodoc
abstract class _$$DocumentsLoadedImplCopyWith<$Res> {
  factory _$$DocumentsLoadedImplCopyWith(
    _$DocumentsLoadedImpl value,
    $Res Function(_$DocumentsLoadedImpl) then,
  ) = __$$DocumentsLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<VerificationDocument> documents});
}

/// @nodoc
class __$$DocumentsLoadedImplCopyWithImpl<$Res>
    extends _$DocumentsStateCopyWithImpl<$Res, _$DocumentsLoadedImpl>
    implements _$$DocumentsLoadedImplCopyWith<$Res> {
  __$$DocumentsLoadedImplCopyWithImpl(
    _$DocumentsLoadedImpl _value,
    $Res Function(_$DocumentsLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? documents = null}) {
    return _then(
      _$DocumentsLoadedImpl(
        documents: null == documents
            ? _value._documents
            : documents // ignore: cast_nullable_to_non_nullable
                  as List<VerificationDocument>,
      ),
    );
  }
}

/// @nodoc

class _$DocumentsLoadedImpl implements DocumentsLoaded {
  const _$DocumentsLoadedImpl({
    required final List<VerificationDocument> documents,
  }) : _documents = documents;

  final List<VerificationDocument> _documents;
  @override
  List<VerificationDocument> get documents {
    if (_documents is EqualUnmodifiableListView) return _documents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_documents);
  }

  @override
  String toString() {
    return 'DocumentsState.loaded(documents: $documents)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentsLoadedImpl &&
            const DeepCollectionEquality().equals(
              other._documents,
              _documents,
            ));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_documents));

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentsLoadedImplCopyWith<_$DocumentsLoadedImpl> get copyWith =>
      __$$DocumentsLoadedImplCopyWithImpl<_$DocumentsLoadedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<VerificationDocument> documents) loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(documents);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<VerificationDocument> documents)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(documents);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<VerificationDocument> documents)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(documents);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentsInitial value) initial,
    required TResult Function(DocumentsLoading value) loading,
    required TResult Function(DocumentsLoaded value) loaded,
    required TResult Function(DocumentsError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentsInitial value)? initial,
    TResult? Function(DocumentsLoading value)? loading,
    TResult? Function(DocumentsLoaded value)? loaded,
    TResult? Function(DocumentsError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentsInitial value)? initial,
    TResult Function(DocumentsLoading value)? loading,
    TResult Function(DocumentsLoaded value)? loaded,
    TResult Function(DocumentsError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class DocumentsLoaded implements DocumentsState {
  const factory DocumentsLoaded({
    required final List<VerificationDocument> documents,
  }) = _$DocumentsLoadedImpl;

  List<VerificationDocument> get documents;

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentsLoadedImplCopyWith<_$DocumentsLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DocumentsErrorImplCopyWith<$Res> {
  factory _$$DocumentsErrorImplCopyWith(
    _$DocumentsErrorImpl value,
    $Res Function(_$DocumentsErrorImpl) then,
  ) = __$$DocumentsErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DocumentsErrorImplCopyWithImpl<$Res>
    extends _$DocumentsStateCopyWithImpl<$Res, _$DocumentsErrorImpl>
    implements _$$DocumentsErrorImplCopyWith<$Res> {
  __$$DocumentsErrorImplCopyWithImpl(
    _$DocumentsErrorImpl _value,
    $Res Function(_$DocumentsErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$DocumentsErrorImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$DocumentsErrorImpl implements DocumentsError {
  const _$DocumentsErrorImpl({required this.message});

  @override
  final String message;

  @override
  String toString() {
    return 'DocumentsState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentsErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentsErrorImplCopyWith<_$DocumentsErrorImpl> get copyWith =>
      __$$DocumentsErrorImplCopyWithImpl<_$DocumentsErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<VerificationDocument> documents) loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<VerificationDocument> documents)? loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<VerificationDocument> documents)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentsInitial value) initial,
    required TResult Function(DocumentsLoading value) loading,
    required TResult Function(DocumentsLoaded value) loaded,
    required TResult Function(DocumentsError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentsInitial value)? initial,
    TResult? Function(DocumentsLoading value)? loading,
    TResult? Function(DocumentsLoaded value)? loaded,
    TResult? Function(DocumentsError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentsInitial value)? initial,
    TResult Function(DocumentsLoading value)? loading,
    TResult Function(DocumentsLoaded value)? loaded,
    TResult Function(DocumentsError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class DocumentsError implements DocumentsState {
  const factory DocumentsError({required final String message}) =
      _$DocumentsErrorImpl;

  String get message;

  /// Create a copy of DocumentsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentsErrorImplCopyWith<_$DocumentsErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DocumentUploadState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(DocumentType documentType, double progress)
    uploading,
    required TResult Function(VerificationDocument document) success,
    required TResult Function(String message, DocumentType? documentType) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(DocumentType documentType, double progress)? uploading,
    TResult? Function(VerificationDocument document)? success,
    TResult? Function(String message, DocumentType? documentType)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(DocumentType documentType, double progress)? uploading,
    TResult Function(VerificationDocument document)? success,
    TResult Function(String message, DocumentType? documentType)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentUploadInitial value) initial,
    required TResult Function(DocumentUploadUploading value) uploading,
    required TResult Function(DocumentUploadSuccess value) success,
    required TResult Function(DocumentUploadError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentUploadInitial value)? initial,
    TResult? Function(DocumentUploadUploading value)? uploading,
    TResult? Function(DocumentUploadSuccess value)? success,
    TResult? Function(DocumentUploadError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentUploadInitial value)? initial,
    TResult Function(DocumentUploadUploading value)? uploading,
    TResult Function(DocumentUploadSuccess value)? success,
    TResult Function(DocumentUploadError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentUploadStateCopyWith<$Res> {
  factory $DocumentUploadStateCopyWith(
    DocumentUploadState value,
    $Res Function(DocumentUploadState) then,
  ) = _$DocumentUploadStateCopyWithImpl<$Res, DocumentUploadState>;
}

/// @nodoc
class _$DocumentUploadStateCopyWithImpl<$Res, $Val extends DocumentUploadState>
    implements $DocumentUploadStateCopyWith<$Res> {
  _$DocumentUploadStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DocumentUploadInitialImplCopyWith<$Res> {
  factory _$$DocumentUploadInitialImplCopyWith(
    _$DocumentUploadInitialImpl value,
    $Res Function(_$DocumentUploadInitialImpl) then,
  ) = __$$DocumentUploadInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DocumentUploadInitialImplCopyWithImpl<$Res>
    extends _$DocumentUploadStateCopyWithImpl<$Res, _$DocumentUploadInitialImpl>
    implements _$$DocumentUploadInitialImplCopyWith<$Res> {
  __$$DocumentUploadInitialImplCopyWithImpl(
    _$DocumentUploadInitialImpl _value,
    $Res Function(_$DocumentUploadInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DocumentUploadInitialImpl implements DocumentUploadInitial {
  const _$DocumentUploadInitialImpl();

  @override
  String toString() {
    return 'DocumentUploadState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentUploadInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(DocumentType documentType, double progress)
    uploading,
    required TResult Function(VerificationDocument document) success,
    required TResult Function(String message, DocumentType? documentType) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(DocumentType documentType, double progress)? uploading,
    TResult? Function(VerificationDocument document)? success,
    TResult? Function(String message, DocumentType? documentType)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(DocumentType documentType, double progress)? uploading,
    TResult Function(VerificationDocument document)? success,
    TResult Function(String message, DocumentType? documentType)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentUploadInitial value) initial,
    required TResult Function(DocumentUploadUploading value) uploading,
    required TResult Function(DocumentUploadSuccess value) success,
    required TResult Function(DocumentUploadError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentUploadInitial value)? initial,
    TResult? Function(DocumentUploadUploading value)? uploading,
    TResult? Function(DocumentUploadSuccess value)? success,
    TResult? Function(DocumentUploadError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentUploadInitial value)? initial,
    TResult Function(DocumentUploadUploading value)? uploading,
    TResult Function(DocumentUploadSuccess value)? success,
    TResult Function(DocumentUploadError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class DocumentUploadInitial implements DocumentUploadState {
  const factory DocumentUploadInitial() = _$DocumentUploadInitialImpl;
}

/// @nodoc
abstract class _$$DocumentUploadUploadingImplCopyWith<$Res> {
  factory _$$DocumentUploadUploadingImplCopyWith(
    _$DocumentUploadUploadingImpl value,
    $Res Function(_$DocumentUploadUploadingImpl) then,
  ) = __$$DocumentUploadUploadingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DocumentType documentType, double progress});
}

/// @nodoc
class __$$DocumentUploadUploadingImplCopyWithImpl<$Res>
    extends
        _$DocumentUploadStateCopyWithImpl<$Res, _$DocumentUploadUploadingImpl>
    implements _$$DocumentUploadUploadingImplCopyWith<$Res> {
  __$$DocumentUploadUploadingImplCopyWithImpl(
    _$DocumentUploadUploadingImpl _value,
    $Res Function(_$DocumentUploadUploadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? documentType = null, Object? progress = null}) {
    return _then(
      _$DocumentUploadUploadingImpl(
        documentType: null == documentType
            ? _value.documentType
            : documentType // ignore: cast_nullable_to_non_nullable
                  as DocumentType,
        progress: null == progress
            ? _value.progress
            : progress // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc

class _$DocumentUploadUploadingImpl implements DocumentUploadUploading {
  const _$DocumentUploadUploadingImpl({
    required this.documentType,
    required this.progress,
  });

  @override
  final DocumentType documentType;
  @override
  final double progress;

  @override
  String toString() {
    return 'DocumentUploadState.uploading(documentType: $documentType, progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentUploadUploadingImpl &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.progress, progress) ||
                other.progress == progress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, documentType, progress);

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentUploadUploadingImplCopyWith<_$DocumentUploadUploadingImpl>
  get copyWith =>
      __$$DocumentUploadUploadingImplCopyWithImpl<
        _$DocumentUploadUploadingImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(DocumentType documentType, double progress)
    uploading,
    required TResult Function(VerificationDocument document) success,
    required TResult Function(String message, DocumentType? documentType) error,
  }) {
    return uploading(documentType, progress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(DocumentType documentType, double progress)? uploading,
    TResult? Function(VerificationDocument document)? success,
    TResult? Function(String message, DocumentType? documentType)? error,
  }) {
    return uploading?.call(documentType, progress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(DocumentType documentType, double progress)? uploading,
    TResult Function(VerificationDocument document)? success,
    TResult Function(String message, DocumentType? documentType)? error,
    required TResult orElse(),
  }) {
    if (uploading != null) {
      return uploading(documentType, progress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentUploadInitial value) initial,
    required TResult Function(DocumentUploadUploading value) uploading,
    required TResult Function(DocumentUploadSuccess value) success,
    required TResult Function(DocumentUploadError value) error,
  }) {
    return uploading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentUploadInitial value)? initial,
    TResult? Function(DocumentUploadUploading value)? uploading,
    TResult? Function(DocumentUploadSuccess value)? success,
    TResult? Function(DocumentUploadError value)? error,
  }) {
    return uploading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentUploadInitial value)? initial,
    TResult Function(DocumentUploadUploading value)? uploading,
    TResult Function(DocumentUploadSuccess value)? success,
    TResult Function(DocumentUploadError value)? error,
    required TResult orElse(),
  }) {
    if (uploading != null) {
      return uploading(this);
    }
    return orElse();
  }
}

abstract class DocumentUploadUploading implements DocumentUploadState {
  const factory DocumentUploadUploading({
    required final DocumentType documentType,
    required final double progress,
  }) = _$DocumentUploadUploadingImpl;

  DocumentType get documentType;
  double get progress;

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentUploadUploadingImplCopyWith<_$DocumentUploadUploadingImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DocumentUploadSuccessImplCopyWith<$Res> {
  factory _$$DocumentUploadSuccessImplCopyWith(
    _$DocumentUploadSuccessImpl value,
    $Res Function(_$DocumentUploadSuccessImpl) then,
  ) = __$$DocumentUploadSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VerificationDocument document});

  $VerificationDocumentCopyWith<$Res> get document;
}

/// @nodoc
class __$$DocumentUploadSuccessImplCopyWithImpl<$Res>
    extends _$DocumentUploadStateCopyWithImpl<$Res, _$DocumentUploadSuccessImpl>
    implements _$$DocumentUploadSuccessImplCopyWith<$Res> {
  __$$DocumentUploadSuccessImplCopyWithImpl(
    _$DocumentUploadSuccessImpl _value,
    $Res Function(_$DocumentUploadSuccessImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? document = null}) {
    return _then(
      _$DocumentUploadSuccessImpl(
        document: null == document
            ? _value.document
            : document // ignore: cast_nullable_to_non_nullable
                  as VerificationDocument,
      ),
    );
  }

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VerificationDocumentCopyWith<$Res> get document {
    return $VerificationDocumentCopyWith<$Res>(_value.document, (value) {
      return _then(_value.copyWith(document: value));
    });
  }
}

/// @nodoc

class _$DocumentUploadSuccessImpl implements DocumentUploadSuccess {
  const _$DocumentUploadSuccessImpl({required this.document});

  @override
  final VerificationDocument document;

  @override
  String toString() {
    return 'DocumentUploadState.success(document: $document)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentUploadSuccessImpl &&
            (identical(other.document, document) ||
                other.document == document));
  }

  @override
  int get hashCode => Object.hash(runtimeType, document);

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentUploadSuccessImplCopyWith<_$DocumentUploadSuccessImpl>
  get copyWith =>
      __$$DocumentUploadSuccessImplCopyWithImpl<_$DocumentUploadSuccessImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(DocumentType documentType, double progress)
    uploading,
    required TResult Function(VerificationDocument document) success,
    required TResult Function(String message, DocumentType? documentType) error,
  }) {
    return success(document);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(DocumentType documentType, double progress)? uploading,
    TResult? Function(VerificationDocument document)? success,
    TResult? Function(String message, DocumentType? documentType)? error,
  }) {
    return success?.call(document);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(DocumentType documentType, double progress)? uploading,
    TResult Function(VerificationDocument document)? success,
    TResult Function(String message, DocumentType? documentType)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(document);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentUploadInitial value) initial,
    required TResult Function(DocumentUploadUploading value) uploading,
    required TResult Function(DocumentUploadSuccess value) success,
    required TResult Function(DocumentUploadError value) error,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentUploadInitial value)? initial,
    TResult? Function(DocumentUploadUploading value)? uploading,
    TResult? Function(DocumentUploadSuccess value)? success,
    TResult? Function(DocumentUploadError value)? error,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentUploadInitial value)? initial,
    TResult Function(DocumentUploadUploading value)? uploading,
    TResult Function(DocumentUploadSuccess value)? success,
    TResult Function(DocumentUploadError value)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class DocumentUploadSuccess implements DocumentUploadState {
  const factory DocumentUploadSuccess({
    required final VerificationDocument document,
  }) = _$DocumentUploadSuccessImpl;

  VerificationDocument get document;

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentUploadSuccessImplCopyWith<_$DocumentUploadSuccessImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DocumentUploadErrorImplCopyWith<$Res> {
  factory _$$DocumentUploadErrorImplCopyWith(
    _$DocumentUploadErrorImpl value,
    $Res Function(_$DocumentUploadErrorImpl) then,
  ) = __$$DocumentUploadErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, DocumentType? documentType});
}

/// @nodoc
class __$$DocumentUploadErrorImplCopyWithImpl<$Res>
    extends _$DocumentUploadStateCopyWithImpl<$Res, _$DocumentUploadErrorImpl>
    implements _$$DocumentUploadErrorImplCopyWith<$Res> {
  __$$DocumentUploadErrorImplCopyWithImpl(
    _$DocumentUploadErrorImpl _value,
    $Res Function(_$DocumentUploadErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? documentType = freezed}) {
    return _then(
      _$DocumentUploadErrorImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        documentType: freezed == documentType
            ? _value.documentType
            : documentType // ignore: cast_nullable_to_non_nullable
                  as DocumentType?,
      ),
    );
  }
}

/// @nodoc

class _$DocumentUploadErrorImpl implements DocumentUploadError {
  const _$DocumentUploadErrorImpl({required this.message, this.documentType});

  @override
  final String message;
  @override
  final DocumentType? documentType;

  @override
  String toString() {
    return 'DocumentUploadState.error(message: $message, documentType: $documentType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentUploadErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, documentType);

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentUploadErrorImplCopyWith<_$DocumentUploadErrorImpl> get copyWith =>
      __$$DocumentUploadErrorImplCopyWithImpl<_$DocumentUploadErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(DocumentType documentType, double progress)
    uploading,
    required TResult Function(VerificationDocument document) success,
    required TResult Function(String message, DocumentType? documentType) error,
  }) {
    return error(message, documentType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(DocumentType documentType, double progress)? uploading,
    TResult? Function(VerificationDocument document)? success,
    TResult? Function(String message, DocumentType? documentType)? error,
  }) {
    return error?.call(message, documentType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(DocumentType documentType, double progress)? uploading,
    TResult Function(VerificationDocument document)? success,
    TResult Function(String message, DocumentType? documentType)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, documentType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentUploadInitial value) initial,
    required TResult Function(DocumentUploadUploading value) uploading,
    required TResult Function(DocumentUploadSuccess value) success,
    required TResult Function(DocumentUploadError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentUploadInitial value)? initial,
    TResult? Function(DocumentUploadUploading value)? uploading,
    TResult? Function(DocumentUploadSuccess value)? success,
    TResult? Function(DocumentUploadError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentUploadInitial value)? initial,
    TResult Function(DocumentUploadUploading value)? uploading,
    TResult Function(DocumentUploadSuccess value)? success,
    TResult Function(DocumentUploadError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class DocumentUploadError implements DocumentUploadState {
  const factory DocumentUploadError({
    required final String message,
    final DocumentType? documentType,
  }) = _$DocumentUploadErrorImpl;

  String get message;
  DocumentType? get documentType;

  /// Create a copy of DocumentUploadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentUploadErrorImplCopyWith<_$DocumentUploadErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$VerificationStatusState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VerificationStatus status) loaded,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VerificationStatus status)? loaded,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VerificationStatus status)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VerificationStatusInitial value) initial,
    required TResult Function(VerificationStatusLoading value) loading,
    required TResult Function(VerificationStatusLoaded value) loaded,
    required TResult Function(VerificationStatusError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VerificationStatusInitial value)? initial,
    TResult? Function(VerificationStatusLoading value)? loading,
    TResult? Function(VerificationStatusLoaded value)? loaded,
    TResult? Function(VerificationStatusError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VerificationStatusInitial value)? initial,
    TResult Function(VerificationStatusLoading value)? loading,
    TResult Function(VerificationStatusLoaded value)? loaded,
    TResult Function(VerificationStatusError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationStatusStateCopyWith<$Res> {
  factory $VerificationStatusStateCopyWith(
    VerificationStatusState value,
    $Res Function(VerificationStatusState) then,
  ) = _$VerificationStatusStateCopyWithImpl<$Res, VerificationStatusState>;
}

/// @nodoc
class _$VerificationStatusStateCopyWithImpl<
  $Res,
  $Val extends VerificationStatusState
>
    implements $VerificationStatusStateCopyWith<$Res> {
  _$VerificationStatusStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$VerificationStatusInitialImplCopyWith<$Res> {
  factory _$$VerificationStatusInitialImplCopyWith(
    _$VerificationStatusInitialImpl value,
    $Res Function(_$VerificationStatusInitialImpl) then,
  ) = __$$VerificationStatusInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VerificationStatusInitialImplCopyWithImpl<$Res>
    extends
        _$VerificationStatusStateCopyWithImpl<
          $Res,
          _$VerificationStatusInitialImpl
        >
    implements _$$VerificationStatusInitialImplCopyWith<$Res> {
  __$$VerificationStatusInitialImplCopyWithImpl(
    _$VerificationStatusInitialImpl _value,
    $Res Function(_$VerificationStatusInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VerificationStatusInitialImpl implements VerificationStatusInitial {
  const _$VerificationStatusInitialImpl();

  @override
  String toString() {
    return 'VerificationStatusState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationStatusInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VerificationStatus status) loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VerificationStatus status)? loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VerificationStatus status)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VerificationStatusInitial value) initial,
    required TResult Function(VerificationStatusLoading value) loading,
    required TResult Function(VerificationStatusLoaded value) loaded,
    required TResult Function(VerificationStatusError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VerificationStatusInitial value)? initial,
    TResult? Function(VerificationStatusLoading value)? loading,
    TResult? Function(VerificationStatusLoaded value)? loaded,
    TResult? Function(VerificationStatusError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VerificationStatusInitial value)? initial,
    TResult Function(VerificationStatusLoading value)? loading,
    TResult Function(VerificationStatusLoaded value)? loaded,
    TResult Function(VerificationStatusError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class VerificationStatusInitial implements VerificationStatusState {
  const factory VerificationStatusInitial() = _$VerificationStatusInitialImpl;
}

/// @nodoc
abstract class _$$VerificationStatusLoadingImplCopyWith<$Res> {
  factory _$$VerificationStatusLoadingImplCopyWith(
    _$VerificationStatusLoadingImpl value,
    $Res Function(_$VerificationStatusLoadingImpl) then,
  ) = __$$VerificationStatusLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VerificationStatusLoadingImplCopyWithImpl<$Res>
    extends
        _$VerificationStatusStateCopyWithImpl<
          $Res,
          _$VerificationStatusLoadingImpl
        >
    implements _$$VerificationStatusLoadingImplCopyWith<$Res> {
  __$$VerificationStatusLoadingImplCopyWithImpl(
    _$VerificationStatusLoadingImpl _value,
    $Res Function(_$VerificationStatusLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VerificationStatusLoadingImpl implements VerificationStatusLoading {
  const _$VerificationStatusLoadingImpl();

  @override
  String toString() {
    return 'VerificationStatusState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationStatusLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VerificationStatus status) loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VerificationStatus status)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VerificationStatus status)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VerificationStatusInitial value) initial,
    required TResult Function(VerificationStatusLoading value) loading,
    required TResult Function(VerificationStatusLoaded value) loaded,
    required TResult Function(VerificationStatusError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VerificationStatusInitial value)? initial,
    TResult? Function(VerificationStatusLoading value)? loading,
    TResult? Function(VerificationStatusLoaded value)? loaded,
    TResult? Function(VerificationStatusError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VerificationStatusInitial value)? initial,
    TResult Function(VerificationStatusLoading value)? loading,
    TResult Function(VerificationStatusLoaded value)? loaded,
    TResult Function(VerificationStatusError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class VerificationStatusLoading implements VerificationStatusState {
  const factory VerificationStatusLoading() = _$VerificationStatusLoadingImpl;
}

/// @nodoc
abstract class _$$VerificationStatusLoadedImplCopyWith<$Res> {
  factory _$$VerificationStatusLoadedImplCopyWith(
    _$VerificationStatusLoadedImpl value,
    $Res Function(_$VerificationStatusLoadedImpl) then,
  ) = __$$VerificationStatusLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VerificationStatus status});

  $VerificationStatusCopyWith<$Res> get status;
}

/// @nodoc
class __$$VerificationStatusLoadedImplCopyWithImpl<$Res>
    extends
        _$VerificationStatusStateCopyWithImpl<
          $Res,
          _$VerificationStatusLoadedImpl
        >
    implements _$$VerificationStatusLoadedImplCopyWith<$Res> {
  __$$VerificationStatusLoadedImplCopyWithImpl(
    _$VerificationStatusLoadedImpl _value,
    $Res Function(_$VerificationStatusLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? status = null}) {
    return _then(
      _$VerificationStatusLoadedImpl(
        status: null == status
            ? _value.status
            : status // ignore: cast_nullable_to_non_nullable
                  as VerificationStatus,
      ),
    );
  }

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VerificationStatusCopyWith<$Res> get status {
    return $VerificationStatusCopyWith<$Res>(_value.status, (value) {
      return _then(_value.copyWith(status: value));
    });
  }
}

/// @nodoc

class _$VerificationStatusLoadedImpl implements VerificationStatusLoaded {
  const _$VerificationStatusLoadedImpl({required this.status});

  @override
  final VerificationStatus status;

  @override
  String toString() {
    return 'VerificationStatusState.loaded(status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationStatusLoadedImpl &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationStatusLoadedImplCopyWith<_$VerificationStatusLoadedImpl>
  get copyWith =>
      __$$VerificationStatusLoadedImplCopyWithImpl<
        _$VerificationStatusLoadedImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VerificationStatus status) loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VerificationStatus status)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VerificationStatus status)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VerificationStatusInitial value) initial,
    required TResult Function(VerificationStatusLoading value) loading,
    required TResult Function(VerificationStatusLoaded value) loaded,
    required TResult Function(VerificationStatusError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VerificationStatusInitial value)? initial,
    TResult? Function(VerificationStatusLoading value)? loading,
    TResult? Function(VerificationStatusLoaded value)? loaded,
    TResult? Function(VerificationStatusError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VerificationStatusInitial value)? initial,
    TResult Function(VerificationStatusLoading value)? loading,
    TResult Function(VerificationStatusLoaded value)? loaded,
    TResult Function(VerificationStatusError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class VerificationStatusLoaded implements VerificationStatusState {
  const factory VerificationStatusLoaded({
    required final VerificationStatus status,
  }) = _$VerificationStatusLoadedImpl;

  VerificationStatus get status;

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationStatusLoadedImplCopyWith<_$VerificationStatusLoadedImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VerificationStatusErrorImplCopyWith<$Res> {
  factory _$$VerificationStatusErrorImplCopyWith(
    _$VerificationStatusErrorImpl value,
    $Res Function(_$VerificationStatusErrorImpl) then,
  ) = __$$VerificationStatusErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$VerificationStatusErrorImplCopyWithImpl<$Res>
    extends
        _$VerificationStatusStateCopyWithImpl<
          $Res,
          _$VerificationStatusErrorImpl
        >
    implements _$$VerificationStatusErrorImplCopyWith<$Res> {
  __$$VerificationStatusErrorImplCopyWithImpl(
    _$VerificationStatusErrorImpl _value,
    $Res Function(_$VerificationStatusErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$VerificationStatusErrorImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$VerificationStatusErrorImpl implements VerificationStatusError {
  const _$VerificationStatusErrorImpl({required this.message});

  @override
  final String message;

  @override
  String toString() {
    return 'VerificationStatusState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationStatusErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationStatusErrorImplCopyWith<_$VerificationStatusErrorImpl>
  get copyWith =>
      __$$VerificationStatusErrorImplCopyWithImpl<
        _$VerificationStatusErrorImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(VerificationStatus status) loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(VerificationStatus status)? loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(VerificationStatus status)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(VerificationStatusInitial value) initial,
    required TResult Function(VerificationStatusLoading value) loading,
    required TResult Function(VerificationStatusLoaded value) loaded,
    required TResult Function(VerificationStatusError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(VerificationStatusInitial value)? initial,
    TResult? Function(VerificationStatusLoading value)? loading,
    TResult? Function(VerificationStatusLoaded value)? loaded,
    TResult? Function(VerificationStatusError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(VerificationStatusInitial value)? initial,
    TResult Function(VerificationStatusLoading value)? loading,
    TResult Function(VerificationStatusLoaded value)? loaded,
    TResult Function(VerificationStatusError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class VerificationStatusError implements VerificationStatusState {
  const factory VerificationStatusError({required final String message}) =
      _$VerificationStatusErrorImpl;

  String get message;

  /// Create a copy of VerificationStatusState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationStatusErrorImplCopyWith<_$VerificationStatusErrorImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DocumentDeletionState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String documentId) deleting,
    required TResult Function(String documentId) success,
    required TResult Function(String message, String documentId) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String documentId)? deleting,
    TResult? Function(String documentId)? success,
    TResult? Function(String message, String documentId)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String documentId)? deleting,
    TResult Function(String documentId)? success,
    TResult Function(String message, String documentId)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentDeletionInitial value) initial,
    required TResult Function(DocumentDeletionDeleting value) deleting,
    required TResult Function(DocumentDeletionSuccess value) success,
    required TResult Function(DocumentDeletionError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentDeletionInitial value)? initial,
    TResult? Function(DocumentDeletionDeleting value)? deleting,
    TResult? Function(DocumentDeletionSuccess value)? success,
    TResult? Function(DocumentDeletionError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentDeletionInitial value)? initial,
    TResult Function(DocumentDeletionDeleting value)? deleting,
    TResult Function(DocumentDeletionSuccess value)? success,
    TResult Function(DocumentDeletionError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentDeletionStateCopyWith<$Res> {
  factory $DocumentDeletionStateCopyWith(
    DocumentDeletionState value,
    $Res Function(DocumentDeletionState) then,
  ) = _$DocumentDeletionStateCopyWithImpl<$Res, DocumentDeletionState>;
}

/// @nodoc
class _$DocumentDeletionStateCopyWithImpl<
  $Res,
  $Val extends DocumentDeletionState
>
    implements $DocumentDeletionStateCopyWith<$Res> {
  _$DocumentDeletionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DocumentDeletionInitialImplCopyWith<$Res> {
  factory _$$DocumentDeletionInitialImplCopyWith(
    _$DocumentDeletionInitialImpl value,
    $Res Function(_$DocumentDeletionInitialImpl) then,
  ) = __$$DocumentDeletionInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DocumentDeletionInitialImplCopyWithImpl<$Res>
    extends
        _$DocumentDeletionStateCopyWithImpl<$Res, _$DocumentDeletionInitialImpl>
    implements _$$DocumentDeletionInitialImplCopyWith<$Res> {
  __$$DocumentDeletionInitialImplCopyWithImpl(
    _$DocumentDeletionInitialImpl _value,
    $Res Function(_$DocumentDeletionInitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DocumentDeletionInitialImpl implements DocumentDeletionInitial {
  const _$DocumentDeletionInitialImpl();

  @override
  String toString() {
    return 'DocumentDeletionState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentDeletionInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String documentId) deleting,
    required TResult Function(String documentId) success,
    required TResult Function(String message, String documentId) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String documentId)? deleting,
    TResult? Function(String documentId)? success,
    TResult? Function(String message, String documentId)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String documentId)? deleting,
    TResult Function(String documentId)? success,
    TResult Function(String message, String documentId)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentDeletionInitial value) initial,
    required TResult Function(DocumentDeletionDeleting value) deleting,
    required TResult Function(DocumentDeletionSuccess value) success,
    required TResult Function(DocumentDeletionError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentDeletionInitial value)? initial,
    TResult? Function(DocumentDeletionDeleting value)? deleting,
    TResult? Function(DocumentDeletionSuccess value)? success,
    TResult? Function(DocumentDeletionError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentDeletionInitial value)? initial,
    TResult Function(DocumentDeletionDeleting value)? deleting,
    TResult Function(DocumentDeletionSuccess value)? success,
    TResult Function(DocumentDeletionError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class DocumentDeletionInitial implements DocumentDeletionState {
  const factory DocumentDeletionInitial() = _$DocumentDeletionInitialImpl;
}

/// @nodoc
abstract class _$$DocumentDeletionDeletingImplCopyWith<$Res> {
  factory _$$DocumentDeletionDeletingImplCopyWith(
    _$DocumentDeletionDeletingImpl value,
    $Res Function(_$DocumentDeletionDeletingImpl) then,
  ) = __$$DocumentDeletionDeletingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String documentId});
}

/// @nodoc
class __$$DocumentDeletionDeletingImplCopyWithImpl<$Res>
    extends
        _$DocumentDeletionStateCopyWithImpl<
          $Res,
          _$DocumentDeletionDeletingImpl
        >
    implements _$$DocumentDeletionDeletingImplCopyWith<$Res> {
  __$$DocumentDeletionDeletingImplCopyWithImpl(
    _$DocumentDeletionDeletingImpl _value,
    $Res Function(_$DocumentDeletionDeletingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? documentId = null}) {
    return _then(
      _$DocumentDeletionDeletingImpl(
        documentId: null == documentId
            ? _value.documentId
            : documentId // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$DocumentDeletionDeletingImpl implements DocumentDeletionDeleting {
  const _$DocumentDeletionDeletingImpl({required this.documentId});

  @override
  final String documentId;

  @override
  String toString() {
    return 'DocumentDeletionState.deleting(documentId: $documentId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentDeletionDeletingImpl &&
            (identical(other.documentId, documentId) ||
                other.documentId == documentId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, documentId);

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentDeletionDeletingImplCopyWith<_$DocumentDeletionDeletingImpl>
  get copyWith =>
      __$$DocumentDeletionDeletingImplCopyWithImpl<
        _$DocumentDeletionDeletingImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String documentId) deleting,
    required TResult Function(String documentId) success,
    required TResult Function(String message, String documentId) error,
  }) {
    return deleting(documentId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String documentId)? deleting,
    TResult? Function(String documentId)? success,
    TResult? Function(String message, String documentId)? error,
  }) {
    return deleting?.call(documentId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String documentId)? deleting,
    TResult Function(String documentId)? success,
    TResult Function(String message, String documentId)? error,
    required TResult orElse(),
  }) {
    if (deleting != null) {
      return deleting(documentId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentDeletionInitial value) initial,
    required TResult Function(DocumentDeletionDeleting value) deleting,
    required TResult Function(DocumentDeletionSuccess value) success,
    required TResult Function(DocumentDeletionError value) error,
  }) {
    return deleting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentDeletionInitial value)? initial,
    TResult? Function(DocumentDeletionDeleting value)? deleting,
    TResult? Function(DocumentDeletionSuccess value)? success,
    TResult? Function(DocumentDeletionError value)? error,
  }) {
    return deleting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentDeletionInitial value)? initial,
    TResult Function(DocumentDeletionDeleting value)? deleting,
    TResult Function(DocumentDeletionSuccess value)? success,
    TResult Function(DocumentDeletionError value)? error,
    required TResult orElse(),
  }) {
    if (deleting != null) {
      return deleting(this);
    }
    return orElse();
  }
}

abstract class DocumentDeletionDeleting implements DocumentDeletionState {
  const factory DocumentDeletionDeleting({required final String documentId}) =
      _$DocumentDeletionDeletingImpl;

  String get documentId;

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentDeletionDeletingImplCopyWith<_$DocumentDeletionDeletingImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DocumentDeletionSuccessImplCopyWith<$Res> {
  factory _$$DocumentDeletionSuccessImplCopyWith(
    _$DocumentDeletionSuccessImpl value,
    $Res Function(_$DocumentDeletionSuccessImpl) then,
  ) = __$$DocumentDeletionSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String documentId});
}

/// @nodoc
class __$$DocumentDeletionSuccessImplCopyWithImpl<$Res>
    extends
        _$DocumentDeletionStateCopyWithImpl<$Res, _$DocumentDeletionSuccessImpl>
    implements _$$DocumentDeletionSuccessImplCopyWith<$Res> {
  __$$DocumentDeletionSuccessImplCopyWithImpl(
    _$DocumentDeletionSuccessImpl _value,
    $Res Function(_$DocumentDeletionSuccessImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? documentId = null}) {
    return _then(
      _$DocumentDeletionSuccessImpl(
        documentId: null == documentId
            ? _value.documentId
            : documentId // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$DocumentDeletionSuccessImpl implements DocumentDeletionSuccess {
  const _$DocumentDeletionSuccessImpl({required this.documentId});

  @override
  final String documentId;

  @override
  String toString() {
    return 'DocumentDeletionState.success(documentId: $documentId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentDeletionSuccessImpl &&
            (identical(other.documentId, documentId) ||
                other.documentId == documentId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, documentId);

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentDeletionSuccessImplCopyWith<_$DocumentDeletionSuccessImpl>
  get copyWith =>
      __$$DocumentDeletionSuccessImplCopyWithImpl<
        _$DocumentDeletionSuccessImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String documentId) deleting,
    required TResult Function(String documentId) success,
    required TResult Function(String message, String documentId) error,
  }) {
    return success(documentId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String documentId)? deleting,
    TResult? Function(String documentId)? success,
    TResult? Function(String message, String documentId)? error,
  }) {
    return success?.call(documentId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String documentId)? deleting,
    TResult Function(String documentId)? success,
    TResult Function(String message, String documentId)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(documentId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentDeletionInitial value) initial,
    required TResult Function(DocumentDeletionDeleting value) deleting,
    required TResult Function(DocumentDeletionSuccess value) success,
    required TResult Function(DocumentDeletionError value) error,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentDeletionInitial value)? initial,
    TResult? Function(DocumentDeletionDeleting value)? deleting,
    TResult? Function(DocumentDeletionSuccess value)? success,
    TResult? Function(DocumentDeletionError value)? error,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentDeletionInitial value)? initial,
    TResult Function(DocumentDeletionDeleting value)? deleting,
    TResult Function(DocumentDeletionSuccess value)? success,
    TResult Function(DocumentDeletionError value)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class DocumentDeletionSuccess implements DocumentDeletionState {
  const factory DocumentDeletionSuccess({required final String documentId}) =
      _$DocumentDeletionSuccessImpl;

  String get documentId;

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentDeletionSuccessImplCopyWith<_$DocumentDeletionSuccessImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DocumentDeletionErrorImplCopyWith<$Res> {
  factory _$$DocumentDeletionErrorImplCopyWith(
    _$DocumentDeletionErrorImpl value,
    $Res Function(_$DocumentDeletionErrorImpl) then,
  ) = __$$DocumentDeletionErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, String documentId});
}

/// @nodoc
class __$$DocumentDeletionErrorImplCopyWithImpl<$Res>
    extends
        _$DocumentDeletionStateCopyWithImpl<$Res, _$DocumentDeletionErrorImpl>
    implements _$$DocumentDeletionErrorImplCopyWith<$Res> {
  __$$DocumentDeletionErrorImplCopyWithImpl(
    _$DocumentDeletionErrorImpl _value,
    $Res Function(_$DocumentDeletionErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? documentId = null}) {
    return _then(
      _$DocumentDeletionErrorImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        documentId: null == documentId
            ? _value.documentId
            : documentId // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$DocumentDeletionErrorImpl implements DocumentDeletionError {
  const _$DocumentDeletionErrorImpl({
    required this.message,
    required this.documentId,
  });

  @override
  final String message;
  @override
  final String documentId;

  @override
  String toString() {
    return 'DocumentDeletionState.error(message: $message, documentId: $documentId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentDeletionErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.documentId, documentId) ||
                other.documentId == documentId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, documentId);

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentDeletionErrorImplCopyWith<_$DocumentDeletionErrorImpl>
  get copyWith =>
      __$$DocumentDeletionErrorImplCopyWithImpl<_$DocumentDeletionErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String documentId) deleting,
    required TResult Function(String documentId) success,
    required TResult Function(String message, String documentId) error,
  }) {
    return error(message, documentId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String documentId)? deleting,
    TResult? Function(String documentId)? success,
    TResult? Function(String message, String documentId)? error,
  }) {
    return error?.call(message, documentId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String documentId)? deleting,
    TResult Function(String documentId)? success,
    TResult Function(String message, String documentId)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, documentId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DocumentDeletionInitial value) initial,
    required TResult Function(DocumentDeletionDeleting value) deleting,
    required TResult Function(DocumentDeletionSuccess value) success,
    required TResult Function(DocumentDeletionError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DocumentDeletionInitial value)? initial,
    TResult? Function(DocumentDeletionDeleting value)? deleting,
    TResult? Function(DocumentDeletionSuccess value)? success,
    TResult? Function(DocumentDeletionError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DocumentDeletionInitial value)? initial,
    TResult Function(DocumentDeletionDeleting value)? deleting,
    TResult Function(DocumentDeletionSuccess value)? success,
    TResult Function(DocumentDeletionError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class DocumentDeletionError implements DocumentDeletionState {
  const factory DocumentDeletionError({
    required final String message,
    required final String documentId,
  }) = _$DocumentDeletionErrorImpl;

  String get message;
  String get documentId;

  /// Create a copy of DocumentDeletionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DocumentDeletionErrorImplCopyWith<_$DocumentDeletionErrorImpl>
  get copyWith => throw _privateConstructorUsedError;
}
