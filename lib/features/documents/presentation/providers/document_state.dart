import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/models.dart';

part 'document_state.freezed.dart';

/// State for document list management
@freezed
class DocumentsState with _$DocumentsState {
  const factory DocumentsState.initial() = DocumentsInitial;

  const factory DocumentsState.loading() = DocumentsLoading;

  const factory DocumentsState.loaded({
    required List<VerificationDocument> documents,
  }) = DocumentsLoaded;

  const factory DocumentsState.error({required String message}) =
      DocumentsError;
}

/// State for document upload management
@freezed
class DocumentUploadState with _$DocumentUploadState {
  const factory DocumentUploadState.initial() = DocumentUploadInitial;

  const factory DocumentUploadState.uploading({
    required DocumentType documentType,
    required double progress,
  }) = DocumentUploadUploading;

  const factory DocumentUploadState.success({
    required VerificationDocument document,
  }) = DocumentUploadSuccess;

  const factory DocumentUploadState.error({
    required String message,
    DocumentType? documentType,
  }) = DocumentUploadError;
}

/// State for verification status management
@freezed
class VerificationStatusState with _$VerificationStatusState {
  const factory VerificationStatusState.initial() = VerificationStatusInitial;

  const factory VerificationStatusState.loading() = VerificationStatusLoading;

  const factory VerificationStatusState.loaded({
    required VerificationStatus status,
  }) = VerificationStatusLoaded;

  const factory VerificationStatusState.error({required String message}) =
      VerificationStatusError;
}

/// State for document deletion
@freezed
class DocumentDeletionState with _$DocumentDeletionState {
  const factory DocumentDeletionState.initial() = DocumentDeletionInitial;

  const factory DocumentDeletionState.deleting({required String documentId}) =
      DocumentDeletionDeleting;

  const factory DocumentDeletionState.success({required String documentId}) =
      DocumentDeletionSuccess;

  const factory DocumentDeletionState.error({
    required String message,
    required String documentId,
  }) = DocumentDeletionError;
}
