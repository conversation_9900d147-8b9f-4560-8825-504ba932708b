import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/documents/document_service.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';
import 'document_state.dart';

/// Notifier for managing document list state
/// Handles fetching, refreshing, and managing the list of uploaded documents
class DocumentsNotifier extends StateNotifier<DocumentsState> {
  final DocumentService _documentService;

  DocumentsNotifier(this._documentService)
    : super(const DocumentsState.initial());

  /// Load all documents for the current driver
  Future<void> loadDocuments() async {
    state = const DocumentsState.loading();

    try {
      final documents = await _documentService.getDocuments();
      state = DocumentsState.loaded(documents: documents);
    } catch (error) {
      final message = _getErrorMessage(error);
      state = DocumentsState.error(message: message);
    }
  }

  /// Refresh the document list
  Future<void> refreshDocuments() async {
    // Don't show loading state for refresh, just update the data
    try {
      final documents = await _documentService.getDocuments();
      state = DocumentsState.loaded(documents: documents);
    } catch (error) {
      final message = _getErrorMessage(error);
      state = DocumentsState.error(message: message);
    }
  }

  /// Add a new document to the list (called after successful upload)
  void addDocument(VerificationDocument document) {
    state.maybeWhen(
      loaded: (documents) {
        final updatedDocuments = [...documents, document];
        state = DocumentsState.loaded(documents: updatedDocuments);
      },
      orElse: () {
        // If not in loaded state, just load all documents
        loadDocuments();
      },
    );
  }

  /// Remove a document from the list (called after successful deletion)
  void removeDocument(String documentId) {
    state.maybeWhen(
      loaded: (documents) {
        final updatedDocuments = documents
            .where((doc) => doc.id != documentId)
            .toList();
        state = DocumentsState.loaded(documents: updatedDocuments);
      },
      orElse: () {
        // If not in loaded state, just load all documents
        loadDocuments();
      },
    );
  }

  /// Update a document in the list (called after status change)
  void updateDocument(VerificationDocument updatedDocument) {
    state.maybeWhen(
      loaded: (documents) {
        final updatedDocuments = documents.map((doc) {
          return doc.id == updatedDocument.id ? updatedDocument : doc;
        }).toList();
        state = DocumentsState.loaded(documents: updatedDocuments);
      },
      orElse: () {
        // If not in loaded state, just load all documents
        loadDocuments();
      },
    );
  }

  /// Get documents by type
  List<VerificationDocument> getDocumentsByType(DocumentType type) {
    return state.maybeWhen(
      loaded: (documents) =>
          documents.where((doc) => doc.documentType == type).toList(),
      orElse: () => [],
    );
  }

  /// Check if a document type is already uploaded
  bool hasDocumentType(DocumentType type) {
    return state.maybeWhen(
      loaded: (documents) => documents.any((doc) => doc.documentType == type),
      orElse: () => false,
    );
  }

  /// Get the count of uploaded documents
  int get documentCount {
    return state.maybeWhen(
      loaded: (documents) => documents.length,
      orElse: () => 0,
    );
  }

  /// Check if all required documents are uploaded
  bool get hasAllRequiredDocuments {
    final requiredTypes = [
      DocumentType.license,
      DocumentType.insurance,
      DocumentType.registration,
      DocumentType.vehiclePhoto,
    ];

    return state.maybeWhen(
      loaded: (documents) {
        final uploadedTypes = documents.map((doc) => doc.documentType).toSet();
        return requiredTypes.every((type) => uploadedTypes.contains(type));
      },
      orElse: () => false,
    );
  }

  /// Get missing required document types
  List<DocumentType> get missingDocumentTypes {
    final requiredTypes = [
      DocumentType.license,
      DocumentType.insurance,
      DocumentType.registration,
      DocumentType.vehiclePhoto,
    ];

    return state.maybeWhen(
      loaded: (documents) {
        final uploadedTypes = documents.map((doc) => doc.documentType).toSet();
        return requiredTypes
            .where((type) => !uploadedTypes.contains(type))
            .toList();
      },
      orElse: () => requiredTypes,
    );
  }

  /// Extract error message from various error types
  String _getErrorMessage(dynamic error) {
    if (error is AppError) {
      return error.when(
        network: (message, details) => message,
        authentication: (message, errorCode) => message,
        validation: (message, fieldErrors) => message,
        location: (message, errorType) => message,
        document: (message, errorType) => message,
        server: (message, statusCode) => message,
        unknown: (message, exception) => message,
      );
    }
    return error.toString();
  }
}
