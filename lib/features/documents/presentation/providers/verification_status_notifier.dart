import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/documents/document_service.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';
import 'document_state.dart';

/// Notifier for managing verification status state
/// Handles verification status tracking and submission
class VerificationStatusNotifier
    extends StateNotifier<VerificationStatusState> {
  final DocumentService _documentService;

  VerificationStatusNotifier(this._documentService)
    : super(const VerificationStatusState.initial());

  /// Load the current verification status
  Future<void> loadVerificationStatus() async {
    state = const VerificationStatusState.loading();

    try {
      final status = await _documentService.getVerificationStatus();
      state = VerificationStatusState.loaded(status: status);
    } catch (error) {
      final message = _getErrorMessage(error);
      state = VerificationStatusState.error(message: message);
    }
  }

  /// Refresh the verification status
  Future<void> refreshVerificationStatus() async {
    // Don't show loading state for refresh, just update the data
    try {
      final status = await _documentService.getVerificationStatus();
      state = VerificationStatusState.loaded(status: status);
    } catch (error) {
      final message = _getErrorMessage(error);
      state = VerificationStatusState.error(message: message);
    }
  }

  /// Submit documents for verification
  Future<bool> submitForVerification() async {
    try {
      await _documentService.submitForVerification();

      // Refresh the status after submission
      await refreshVerificationStatus();

      return true;
    } catch (error) {
      final message = _getErrorMessage(error);
      state = VerificationStatusState.error(message: message);
      return false;
    }
  }

  /// Check if verification is complete
  bool get isVerificationComplete {
    return state.maybeWhen(
      loaded: (status) => status.isComplete,
      orElse: () => false,
    );
  }

  /// Get verification status string
  String get verificationStatus {
    return state.maybeWhen(
      loaded: (status) => status.status,
      orElse: () => 'Unknown',
    );
  }

  /// Get verification message
  String? get verificationMessage {
    return state.maybeWhen(
      loaded: (status) => status.message,
      orElse: () => null,
    );
  }

  /// Get submitted date
  DateTime? get submittedAt {
    return state.maybeWhen(
      loaded: (status) => status.submittedAt,
      orElse: () => null,
    );
  }

  /// Get reviewed date
  DateTime? get reviewedAt {
    return state.maybeWhen(
      loaded: (status) => status.reviewedAt,
      orElse: () => null,
    );
  }

  /// Get all documents from verification status
  List<VerificationDocument> get documents {
    return state.maybeWhen(
      loaded: (status) => status.documents,
      orElse: () => [],
    );
  }

  /// Check if verification is pending
  bool get isPending {
    return verificationStatus.toLowerCase() == 'pending';
  }

  /// Check if verification is approved
  bool get isApproved {
    return verificationStatus.toLowerCase() == 'approved';
  }

  /// Check if verification is rejected
  bool get isRejected {
    return verificationStatus.toLowerCase() == 'rejected';
  }

  /// Check if verification is under review
  bool get isUnderReview {
    return verificationStatus.toLowerCase() == 'under_review' ||
        verificationStatus.toLowerCase() == 'reviewing';
  }

  /// Check if verification has not been submitted yet
  bool get isNotSubmitted {
    return verificationStatus.toLowerCase() == 'not_submitted' ||
        verificationStatus.toLowerCase() == 'draft';
  }

  /// Get documents by status
  List<VerificationDocument> getDocumentsByStatus(DocumentStatus status) {
    return documents.where((doc) => doc.status == status).toList();
  }

  /// Get approved documents
  List<VerificationDocument> get approvedDocuments {
    return getDocumentsByStatus(DocumentStatus.approved);
  }

  /// Get pending documents
  List<VerificationDocument> get pendingDocuments {
    return getDocumentsByStatus(DocumentStatus.pending);
  }

  /// Get rejected documents
  List<VerificationDocument> get rejectedDocuments {
    return getDocumentsByStatus(DocumentStatus.rejected);
  }

  /// Check if all documents are approved
  bool get allDocumentsApproved {
    final docs = documents;
    return docs.isNotEmpty &&
        docs.every((doc) => doc.status == DocumentStatus.approved);
  }

  /// Check if any documents are rejected
  bool get hasRejectedDocuments {
    return documents.any((doc) => doc.status == DocumentStatus.rejected);
  }

  /// Get rejection reasons for rejected documents
  Map<DocumentType, String> get rejectionReasons {
    final rejectedDocs = rejectedDocuments;
    final reasons = <DocumentType, String>{};

    for (final doc in rejectedDocs) {
      if (doc.rejectionReason != null) {
        reasons[doc.documentType] = doc.rejectionReason!;
      }
    }

    return reasons;
  }

  /// Calculate verification progress (0.0 to 1.0)
  double get verificationProgress {
    final docs = documents;
    if (docs.isEmpty) return 0.0;

    final approvedCount = docs
        .where((doc) => doc.status == DocumentStatus.approved)
        .length;
    return approvedCount / docs.length;
  }

  /// Extract error message from various error types
  String _getErrorMessage(dynamic error) {
    if (error is AppError) {
      return error.when(
        network: (message, details) => message,
        authentication: (message, errorCode) => message,
        validation: (message, fieldErrors) => message,
        location: (message, errorType) => message,
        document: (message, errorType) => message,
        server: (message, statusCode) => message,
        unknown: (message, exception) => message,
      );
    }
    return error.toString();
  }
}
