import 'dart:io';
import '../../../shared/models/models.dart';

/// Repository interface for document verification operations
/// Defines the contract for document upload and verification management
abstract class DocumentRepository {
  /// Document Upload Operations
  Future<VerificationDocument> uploadDocument(
    File file,
    DocumentType documentType, {
    String? description,
  });

  /// Document Management
  Future<List<VerificationDocument>> getDocuments();
  Future<void> deleteDocument(String documentId);

  /// Verification Operations
  Future<void> submitVerificationRequest();
  Future<VerificationStatus> getVerificationStatus();

  /// Document Validation
  Future<bool> validateDocument(File file, DocumentType documentType);

  /// Cache Management
  Future<void> clearDocumentCache();
}
