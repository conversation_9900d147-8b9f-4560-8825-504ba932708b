import 'dart:io';
import '../../../shared/models/models.dart';
import '../../../core/errors/app_error.dart';
import '../../../services/api/api_client.dart';
import '../../../services/storage/storage_service.dart';
import '../domain/auth_repository.dart';

/// Implementation of AuthRepository
/// Handles authentication operations with API integration and local storage
class AuthRepositoryImpl implements AuthRepository {
  final ApiClient _apiClient;
  final StorageService _storageService;

  AuthRepositoryImpl({
    required ApiClient apiClient,
    required StorageService storageService,
  }) : _apiClient = apiClient,
       _storageService = storageService;

  @override
  Future<AuthResult> register(UserRegistration registration) async {
    try {
      final response = await _apiClient.post(
        '/auth/register',
        data: registration.toJson(),
      );

      final authResult = AuthResult.fromJson(response);

      // Store token and user data
      await _storageService.storeToken(authResult.accessToken);
      await _storageService.storeUserData(authResult.user.toJson());

      return authResult;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      final response = await _apiClient.post(
        '/auth/login',
        data: {'email': email, 'password': password, 'user_type': 'driver'},
      );

      final authResult = AuthResult.fromJson(response);

      // Store token and user data
      await _storageService.storeToken(authResult.accessToken);
      await _storageService.storeUserData(authResult.user.toJson());

      return authResult;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Try to call logout endpoint if token exists
      final token = await _storageService.getToken();
      if (token != null) {
        try {
          await _apiClient.post('/auth/logout');
        } catch (e) {
          // Continue with local logout even if API call fails
        }
      }

      // Clear all stored authentication data
      await clearAuthData();
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<User> getCurrentUser() async {
    try {
      // First try to get from local storage
      final userData = await _storageService.getUserData();
      if (userData != null) {
        try {
          return User.fromJson(userData);
        } catch (e) {
          // If local data is corrupted, fetch from API
        }
      }

      // Fetch from API
      final response = await _apiClient.get('/auth/profile');
      final user = User.fromJson(response);

      // Update local storage
      await _storageService.storeUserData(user.toJson());

      return user;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<User> updateProfile(Map<String, dynamic> updates) async {
    try {
      final response = await _apiClient.put('/auth/profile', data: updates);

      final user = User.fromJson(response);

      // Update local storage
      await _storageService.storeUserData(user.toJson());

      return user;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<String> uploadProfileImage(String imagePath) async {
    try {
      final file = File(imagePath);
      final response = await _apiClient.uploadFile(
        '/auth/profile/image',
        file,
        fieldName: 'image',
      );

      final imageUrl = response['image_url'] as String;

      // Update user data in storage with new image URL
      final userData = await _storageService.getUserData();
      if (userData != null) {
        userData['profileImageUrl'] = imageUrl;
        await _storageService.storeUserData(userData);
      }

      return imageUrl;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<void> deleteProfileImage() async {
    try {
      await _apiClient.delete('/auth/profile/image');

      // Update user data in storage to remove image URL
      final userData = await _storageService.getUserData();
      if (userData != null) {
        userData.remove('profileImageUrl');
        await _storageService.storeUserData(userData);
      }
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<bool> verifyToken() async {
    try {
      final token = await _storageService.getToken();
      if (token == null) return false;

      final response = await _apiClient.get('/auth/verify-token');
      return response['valid'] == true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<AuthResult> refreshToken() async {
    try {
      final response = await _apiClient.post('/auth/refresh');
      final authResult = AuthResult.fromJson(response);

      // Update stored token
      await _storageService.storeToken(authResult.accessToken);

      return authResult;
    } catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final token = await _storageService.getToken();
      if (token == null) return false;

      return await verifyToken();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<String?> getStoredToken() async {
    try {
      return await _storageService.getToken();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      await Future.wait([
        _storageService.deleteToken(),
        _storageService.deleteUserData(),
      ]);
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// Handle and transform errors into appropriate AppError types
  AppError _handleError(dynamic error) {
    if (error is AppError) {
      return error;
    }

    // Handle different types of errors and convert to AppError
    if (error.toString().contains('network') ||
        error.toString().contains('connection')) {
      return const AppError.network(
        message:
            'Network connection failed. Please check your internet connection.',
      );
    }

    if (error.toString().contains('401') ||
        error.toString().contains('unauthorized')) {
      return const AppError.authentication(
        message: 'Authentication failed. Please login again.',
        errorCode: '401',
      );
    }

    if (error.toString().contains('validation') ||
        error.toString().contains('400')) {
      return const AppError.validation(
        message: 'Invalid data provided.',
        fieldErrors: {},
      );
    }

    return AppError.unknown(
      message: 'An unexpected error occurred.',
      exception: error,
    );
  }
}
