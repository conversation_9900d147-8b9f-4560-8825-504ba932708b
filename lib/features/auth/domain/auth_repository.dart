import '../../../shared/models/models.dart';

/// Repository interface for authentication operations
/// Defines the contract for authentication-related data operations
abstract class AuthRepository {
  /// Register a new driver user
  Future<AuthResult> register(UserRegistration registration);

  /// Login with email and password
  Future<AuthResult> login(String email, String password);

  /// Logout the current user
  Future<void> logout();

  /// Get current user profile
  Future<User> getCurrentUser();

  /// Update user profile
  Future<User> updateProfile(Map<String, dynamic> updates);

  /// Upload profile image
  Future<String> uploadProfileImage(String imagePath);

  /// Delete profile image
  Future<void> deleteProfileImage();

  /// Verify authentication token
  Future<bool> verifyToken();

  /// Refresh authentication token
  Future<AuthResult> refreshToken();

  /// Check if user is authenticated
  Future<bool> isAuthenticated();

  /// Get stored authentication token
  Future<String?> getStoredToken();

  /// Clear all authentication data
  Future<void> clearAuthData();
}
