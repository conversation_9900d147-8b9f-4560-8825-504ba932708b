import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';

part 'auth_state.freezed.dart';

/// Authentication state for the driver app
@freezed
class AuthState with _$AuthState {
  /// Initial state when app starts
  const factory AuthState.initial() = AuthInitial;

  /// Loading state during authentication operations
  const factory AuthState.loading() = AuthLoading;

  /// Authenticated state with user data
  const factory AuthState.authenticated({
    required User user,
    required String token,
  }) = AuthAuthenticated;

  /// Unauthenticated state
  const factory AuthState.unauthenticated() = AuthUnauthenticated;

  /// Error state with error information
  const factory AuthState.error({
    required AppError error,
    AuthState? previousState,
  }) = AuthError;
}

/// Extension methods for AuthState
extension AuthStateX on AuthState {
  /// Check if user is authenticated
  bool get isAuthenticated => this is AuthAuthenticated;

  /// Check if authentication is in progress
  bool get isLoading => this is AuthLoading;

  /// Check if there's an error
  bool get hasError => this is AuthError;

  /// Get the current user if authenticated
  User? get user =>
      maybeWhen(authenticated: (user, token) => user, orElse: () => null);

  /// Get the current token if authenticated
  String? get token =>
      maybeWhen(authenticated: (user, token) => token, orElse: () => null);

  /// Get the current error if in error state
  AppError? get error =>
      maybeWhen(error: (error, previousState) => error, orElse: () => null);
}
