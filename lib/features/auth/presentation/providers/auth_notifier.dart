import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/auth/auth_service.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/errors/app_error.dart';
import 'auth_state.dart';

/// StateNotifier for managing authentication state
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthState.initial());

  /// Initialize authentication on app startup
  Future<void> initializeAuth() async {
    state = const AuthState.loading();

    try {
      final isInitialized = await _authService.initializeAuth();

      if (isInitialized) {
        final user = await _authService.getCurrentUser();
        final token = await _authService.getStoredToken();

        if (user != null && token != null) {
          state = AuthState.authenticated(user: user, token: token);
        } else {
          state = const AuthState.unauthenticated();
        }
      } else {
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(
              message: 'Failed to initialize authentication',
              exception: e,
            );
      state = AuthState.error(error: error);
    }
  }

  /// Register a new driver
  Future<void> register(UserRegistration registration) async {
    if (state.isLoading) return;

    final previousState = state;
    state = const AuthState.loading();

    try {
      final result = await _authService.register(registration);
      state = AuthState.authenticated(
        user: result.user,
        token: result.accessToken,
      );
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(message: 'Registration failed', exception: e);
      state = AuthState.error(error: error, previousState: previousState);
    }
  }

  /// Login with email and password
  Future<void> login(String email, String password) async {
    if (state.isLoading) return;

    final previousState = state;
    state = const AuthState.loading();

    try {
      final result = await _authService.login(email, password);
      state = AuthState.authenticated(
        user: result.user,
        token: result.accessToken,
      );
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(message: 'Login failed', exception: e);
      state = AuthState.error(error: error, previousState: previousState);
    }
  }

  /// Logout the current user
  Future<void> logout() async {
    if (state.isLoading) return;

    state = const AuthState.loading();

    try {
      await _authService.logout();
      state = const AuthState.unauthenticated();
    } catch (e) {
      // Even if logout fails, clear the state
      state = const AuthState.unauthenticated();
    }
  }

  /// Update user profile
  Future<void> updateProfile(Map<String, dynamic> updates) async {
    if (!state.isAuthenticated || state.isLoading) return;

    final currentState = state as AuthAuthenticated;
    state = const AuthState.loading();

    try {
      final updatedUser = await _authService.updateProfile(updates);
      state = AuthState.authenticated(
        user: updatedUser,
        token: currentState.token,
      );
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(message: 'Profile update failed', exception: e);
      state = AuthState.error(error: error, previousState: currentState);
    }
  }

  /// Upload profile image
  Future<void> uploadProfileImage(String imagePath) async {
    if (!state.isAuthenticated || state.isLoading) return;

    final currentState = state as AuthAuthenticated;
    state = const AuthState.loading();

    try {
      final imageUrl = await _authService.uploadProfileImage(imagePath);

      // Update user with new profile image URL
      final updatedUser = currentState.user.copyWith(profileImageUrl: imageUrl);
      state = AuthState.authenticated(
        user: updatedUser,
        token: currentState.token,
      );
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(
              message: 'Profile image upload failed',
              exception: e,
            );
      state = AuthState.error(error: error, previousState: currentState);
    }
  }

  /// Delete profile image
  Future<void> deleteProfileImage() async {
    if (!state.isAuthenticated || state.isLoading) return;

    final currentState = state as AuthAuthenticated;
    state = const AuthState.loading();

    try {
      await _authService.deleteProfileImage();

      // Update user to remove profile image URL
      final updatedUser = currentState.user.copyWith(profileImageUrl: null);
      state = AuthState.authenticated(
        user: updatedUser,
        token: currentState.token,
      );
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(
              message: 'Profile image deletion failed',
              exception: e,
            );
      state = AuthState.error(error: error, previousState: currentState);
    }
  }

  /// Refresh authentication token
  Future<void> refreshToken() async {
    if (!state.isAuthenticated || state.isLoading) return;

    // Get current authenticated state (unused for now)
    // final currentState = state as AuthAuthenticated;

    try {
      final result = await _authService.refreshToken();
      state = AuthState.authenticated(
        user: result.user,
        token: result.accessToken,
      );
    } catch (e) {
      // If refresh fails, logout the user
      state = const AuthState.unauthenticated();
    }
  }

  /// Handle token expiration
  Future<void> handleTokenExpiration() async {
    try {
      final success = await _authService.handleTokenExpiration();

      if (!success) {
        state = const AuthState.unauthenticated();
      } else {
        // Refresh the current user data
        final user = await _authService.getCurrentUser();
        final token = await _authService.getStoredToken();

        if (user != null && token != null) {
          state = AuthState.authenticated(user: user, token: token);
        } else {
          state = const AuthState.unauthenticated();
        }
      }
    } catch (e) {
      state = const AuthState.unauthenticated();
    }
  }

  /// Clear error state and return to previous state
  void clearError() {
    state.maybeWhen(
      error: (error, previousState) {
        if (previousState != null) {
          state = previousState;
        } else {
          state = const AuthState.unauthenticated();
        }
      },
      orElse: () {}, // Do nothing if not in error state
    );
  }

  /// Verify current token
  Future<void> verifyToken() async {
    if (!state.isAuthenticated) return;

    try {
      final isValid = await _authService.verifyToken();

      if (!isValid) {
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      state = const AuthState.unauthenticated();
    }
  }
}
