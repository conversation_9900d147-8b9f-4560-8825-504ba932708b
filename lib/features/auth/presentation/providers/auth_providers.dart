import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/auth/auth_service.dart';
import '../../../../services/auth/auth_service_impl.dart';
import '../../../../features/auth/domain/auth_repository.dart';
import '../../../../features/auth/data/auth_repository_impl.dart';
import '../../../../services/api/api_client.dart';
import '../../../../services/api/dio_api_client.dart';
import '../../../../services/storage/storage_service.dart';
import '../../../../services/storage/secure_storage_service.dart';
import '../../../../shared/models/models.dart';
import 'auth_notifier.dart';
import 'auth_state.dart';

/// Provider for ApiClient
final apiClientProvider = Provider<ApiClient>((ref) {
  return DioApiClient();
});

/// Provider for StorageService
final storageServiceProvider = Provider<StorageService>((ref) {
  return SecureStorageService();
});

/// Provider for AuthRepository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl(
    apiClient: ref.read(apiClientProvider),
    storageService: ref.read(storageServiceProvider),
  );
});

/// Provider for AuthService
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthServiceImpl(
    authRepository: ref.read(authRepositoryProvider),
    apiClient: ref.read(apiClientProvider),
  );
});

/// StateNotifierProvider for authentication state
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref.read(authServiceProvider));
});

/// Provider for current authenticated user
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.user;
});

/// Provider for authentication token
final authTokenProvider = Provider<String?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.token;
});

/// Provider to check if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isAuthenticated;
});

/// Provider to check if authentication is loading
final isAuthLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isLoading;
});

/// Provider for authentication error
final authErrorProvider = Provider<String?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.error?.message;
});

/// FutureProvider for token verification
final tokenVerificationProvider = FutureProvider<bool>((ref) async {
  final authService = ref.read(authServiceProvider);
  return await authService.verifyToken();
});

/// FutureProvider for authentication initialization
final authInitializationProvider = FutureProvider<bool>((ref) async {
  final authService = ref.read(authServiceProvider);
  return await authService.initializeAuth();
});
