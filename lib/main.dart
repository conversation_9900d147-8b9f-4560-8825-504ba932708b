import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'core/di/service_locator.dart';
import 'core/constants/app_constants.dart';
import 'core/routing/app_router.dart';

/// Main entry point for the Lucian Drives Driver app
void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await setupServiceLocator();

  // Run the app with Riverpod
  runApp(const ProviderScope(child: LucianDriverApp()));
}

/// Root application widget for the Lucian Drives Driver app
class LucianDriverApp extends ConsumerWidget {
  const LucianDriverApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appRouter = AppRouter();

    return MaterialApp.router(
      title: AppConstants.appName,
      theme: ThemeData(
        // Primary color scheme based on design requirements (teal #00B5A5)
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF00B5A5),
          brightness: Brightness.light,
        ),
        useMaterial3: true,

        // App bar theme
        appBarTheme: const AppBarTheme(centerTitle: true, elevation: 0),

        // Input decoration theme
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              AppConstants.defaultBorderRadius,
            ),
          ),
          contentPadding: const EdgeInsets.all(AppConstants.defaultPadding),
        ),

        // Elevated button theme
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppConstants.defaultBorderRadius,
              ),
            ),
          ),
        ),
      ),

      // AutoRoute configuration  
      routerConfig: appRouter.config(),

      // Debug banner
      debugShowCheckedModeBanner: false,
    );
  }
} 
