/// API constants for the Lucian Drives driver app
class ApiConstants {
  static const String baseUrl = 'http://localhost:8000/api/v1';

  // Authentication endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String logout = '/auth/logout';
  static const String profile = '/auth/profile';
  static const String profileImage = '/auth/profile/image';
  static const String verifyToken = '/auth/verify-token';

  // Driver-specific endpoints
  static const String driverProfile = '/drivers/profile';
  static const String driverVehicle = '/drivers/vehicle';
  static const String driverAvailability = '/drivers/availability';
  static const String driverLocation = '/drivers/location';
  static const String driverEarnings = '/drivers/earnings';
  static const String driverStats = '/drivers/stats';
  static const String driverVerificationStatus = '/drivers/verification-status';

  // Document management endpoints
  static const String documentsUpload = '/drivers/documents/upload';
  static const String documents = '/drivers/documents';
  static const String verificationRequest = '/drivers/verification/request';

  // Request timeouts (in milliseconds)
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  // HTTP headers
  static const String contentTypeJson = 'application/json';
  static const String contentTypeMultipart = 'multipart/form-data';
  static const String authorizationHeader = 'Authorization';
  static const String bearerPrefix = 'Bearer ';

  // API response keys
  static const String dataKey = 'data';
  static const String messageKey = 'message';
  static const String errorKey = 'error';
  static const String statusKey = 'status';

  /// Get full URL for an endpoint
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }

  /// Get authorization header value
  static String getAuthHeader(String token) {
    return '$bearerPrefix$token';
  }
}
