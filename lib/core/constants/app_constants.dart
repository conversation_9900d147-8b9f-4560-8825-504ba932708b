/// Application constants for the Lucian Drives driver app
class AppConstants {
  // App Information
  static const String appName = 'Lucian Drives - Driver';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';

  // User Types
  static const String userTypeDriver = 'driver';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String driverPreferencesKey = 'driver_preferences';
  static const String driverProfileKey = 'driver_profile';
  static const String vehicleInfoKey = 'vehicle_info';
  static const String availabilityStatusKey = 'availability_status';

  // Location Settings
  static const double locationUpdateDistanceFilter = 10.0; // meters
  static const int locationUpdateIntervalMs = 5000; // 5 seconds
  static const double locationAccuracyThreshold = 50.0; // meters
  static const int locationTimeoutMs = 10000; // 10 seconds

  // Document Settings
  static const int maxDocumentSizeMB = 5;
  static const int maxDocumentSizeBytes = 5 * 1024 * 1024; // 5MB in bytes
  static const List<String> allowedDocumentExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.pdf',
  ];
  static const List<String> allowedDocumentMimeTypes = [
    'image/jpeg',
    'image/png',
    'application/pdf',
  ];

  // Document Types
  static const String documentTypeLicense = 'license';
  static const String documentTypeInsurance = 'insurance';
  static const String documentTypeRegistration = 'registration';
  static const String documentTypeVehiclePhoto = 'vehicle_photo';

  // Document Status
  static const String documentStatusPending = 'pending';
  static const String documentStatusApproved = 'approved';
  static const String documentStatusRejected = 'rejected';

  // UI Constants (based on design.json)
  static const double defaultPadding = 16.0;
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  static const int animationDurationMs = 300;
  static const double minTouchTarget = 44.0;

  // Spacing (based on design.json)
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 16.0;
  static const double spacingLg = 24.0;
  static const double spacingXl = 32.0;
  static const double spacing2xl = 48.0;
  static const double spacing3xl = 64.0;

  // Layout Constants
  static const double statusBarHeight = 44.0;
  static const double tabBarHeight = 83.0;
  static const double headerHeight = 56.0;
  static const double containerPadding = 16.0;
  static const double sectionSpacing = 24.0;

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int maxNameLength = 50;
  static const int maxEmailLength = 100;
  static const int maxPhoneLength = 20;
  static const int maxLicenseNumberLength = 20;
  static const int maxVehicleMakeLength = 30;
  static const int maxVehicleModelLength = 30;
  static const int maxLicensePlateLength = 15;

  // Driver Status
  static const String driverStatusOffline = 'offline';
  static const String driverStatusOnline = 'online';
  static const String driverStatusBusy = 'busy';

  // Verification Status
  static const String verificationStatusNotStarted = 'not_started';
  static const String verificationStatusPending = 'pending';
  static const String verificationStatusUnderReview = 'under_review';
  static const String verificationStatusApproved = 'approved';
  static const String verificationStatusRejected = 'rejected';

  // Error Messages
  static const String networkErrorMessage =
      'Network connection error. Please check your internet connection.';
  static const String serverErrorMessage =
      'Server error. Please try again later.';
  static const String authErrorMessage =
      'Authentication failed. Please login again.';
  static const String locationErrorMessage =
      'Location access is required to go online.';
  static const String documentUploadErrorMessage =
      'Failed to upload document. Please try again.';

  // Success Messages
  static const String profileUpdatedMessage = 'Profile updated successfully';
  static const String documentUploadedMessage =
      'Document uploaded successfully';
  static const String availabilityUpdatedMessage =
      'Availability status updated';

  // Retry Settings
  static const int maxRetryAttempts = 3;
  static const int retryDelayMs = 1000; // 1 second

  // Cache Settings
  static const int cacheExpirationHours = 24;
  static const int maxCacheSize = 100; // Maximum number of cached items
}
