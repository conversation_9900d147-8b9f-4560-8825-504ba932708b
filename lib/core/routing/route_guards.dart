import 'package:auto_route/auto_route.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/auth/presentation/providers/auth_providers.dart';
import 'app_router.dart';

/// Guard to protect routes that require authentication
class AuthGuard extends AutoRouteGuard {
  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) {
    // Get the provider container from the router context
    final container = ProviderScope.containerOf(router.navigatorKey.currentContext!);
    
    // Check if user is authenticated
    final isAuthenticated = container.read(isAuthenticatedProvider);
    
    if (isAuthenticated) {
      // User is authenticated, allow navigation
      resolver.next();
    } else {
      // User is not authenticated, redirect to welcome screen
      resolver.redirect(const WelcomeRoute());
    }
  }
}

/// Guard to protect routes that require driver verification
/// For now, this just checks authentication - full verification logic
/// will be implemented when driver profile system is complete
class VerificationGuard extends AutoRouteGuard {
  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) {
    // Get the provider container from the router context
    final container = ProviderScope.containerOf(router.navigatorKey.currentContext!);
    
    // Check if user is authenticated first
    final isAuthenticated = container.read(isAuthenticatedProvider);
    
    if (!isAuthenticated) {
      // User is not authenticated, redirect to welcome screen
      resolver.redirect(const WelcomeRoute());
      return;
    }
    
    // For now, allow navigation - verification logic will be implemented later
    // when the driver profile and verification systems are fully implemented
    resolver.next();
  }
}