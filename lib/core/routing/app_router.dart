import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../features/auth/presentation/screens/splash_screen.dart';
import '../../features/auth/presentation/screens/welcome_screen.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/auth/presentation/screens/register_screen.dart';
import '../../features/driver/presentation/screens/profile_setup_screen.dart';
import '../../features/driver/presentation/screens/vehicle_setup_screen.dart';
import '../../features/driver/presentation/screens/profile_screen.dart';
import '../../features/driver/presentation/screens/earnings_screen.dart';
import '../../features/documents/presentation/screens/documents_screen.dart';
import '../../features/documents/presentation/screens/document_upload_screen.dart';
import '../../features/documents/presentation/screens/verification_status_screen.dart';
import '../../features/dashboard/presentation/screens/dashboard_screen.dart';
import 'route_guards.dart';

part 'app_router.gr.dart';

/// AutoRoute configuration for the Lucian Driver app
@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
    // Splash route - initial route with token verification
    AutoRoute(page: SplashRoute.page, path: '/splash', initial: true),

    // Authentication routes
    AutoRoute(page: WelcomeRoute.page, path: '/welcome'),
    AutoRoute(page: LoginRoute.page, path: '/login'),
    AutoRoute(page: RegisterRoute.page, path: '/register'),

    // Driver onboarding routes (require authentication)
    AutoRoute(
      page: ProfileSetupRoute.page,
      path: '/profile-setup',
      guards: [AuthGuard()],
    ),
    AutoRoute(
      page: VehicleSetupRoute.page,
      path: '/vehicle-setup',
      guards: [AuthGuard()],
    ),

    // Main app routes with nested navigation
    AutoRoute(
      page: DashboardRoute.page,
      path: '/dashboard',
      guards: [AuthGuard(), VerificationGuard()],
      children: [
        AutoRoute(page: HomeRoute.page, path: '/home'),
        AutoRoute(page: EarningsRoute.page, path: '/earnings'),
        AutoRoute(page: ProfileRoute.page, path: '/profile'),
        AutoRoute(page: DocumentsRoute.page, path: '/documents'),
      ],
    ),

    // Document management routes
    AutoRoute(
      page: DocumentUploadRoute.page,
      path: '/document-upload',
      guards: [AuthGuard()],
    ),
    AutoRoute(
      page: VerificationStatusRoute.page,
      path: '/verification-status',
      guards: [AuthGuard()],
    ),

    // Fallback route
    AutoRoute(page: NotFoundRoute.page, path: '*'),
  ];
}

// Route pages - these will be implemented in the next subtasks

/// Splash screen route page
@RoutePage(name: 'SplashRoute')
class SplashPage extends StatelessWidget {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const SplashScreen();
  }
}

/// Welcome screen route page
@RoutePage(name: 'WelcomeRoute')
class WelcomePage extends StatelessWidget {
  const WelcomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const WelcomeScreen();
  }
}

/// Login screen route page
@RoutePage(name: 'LoginRoute')
class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const LoginScreen();
  }
}

/// Register screen route page
@RoutePage(name: 'RegisterRoute')
class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const RegisterScreen();
  }
}

/// Profile setup route page
@RoutePage(name: 'ProfileSetupRoute')
class ProfileSetupPage extends StatelessWidget {
  const ProfileSetupPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfileSetupScreen();
  }
}

/// Vehicle setup route page
@RoutePage(name: 'VehicleSetupRoute')
class VehicleSetupPage extends StatelessWidget {
  const VehicleSetupPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const VehicleSetupScreen();
  }
}

/// Dashboard route page for nested navigation
@RoutePage(name: 'DashboardRoute')
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const DashboardScreen();
  }
}

/// Home screen route page
@RoutePage(name: 'HomeRoute')
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const HomeScreen();
  }
}

/// Earnings screen route page
@RoutePage(name: 'EarningsRoute')
class EarningsPage extends StatelessWidget {
  const EarningsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const EarningsScreen();
  }
}

/// Profile screen route page
@RoutePage(name: 'ProfileRoute')
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfileScreen();
  }
}

/// Documents screen route page
@RoutePage(name: 'DocumentsRoute')
class DocumentsPage extends StatelessWidget {
  const DocumentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const DocumentsScreen();
  }
}

/// Document upload route page
@RoutePage(name: 'DocumentUploadRoute')
class DocumentUploadPage extends StatelessWidget {
  const DocumentUploadPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const DocumentUploadScreen();
  }
}

/// Verification status route page
@RoutePage(name: 'VerificationStatusRoute')
class VerificationStatusPage extends StatelessWidget {
  const VerificationStatusPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const VerificationStatusScreen();
  }
}

/// Not found route page
@RoutePage(name: 'NotFoundRoute')
class NotFoundPage extends StatelessWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: const Center(child: Text('The requested page was not found.')),
    );
  }
}
