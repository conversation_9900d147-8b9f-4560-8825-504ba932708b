import 'dart:io';
import 'package:dio/dio.dart';
import 'app_error.dart';
import '../constants/app_constants.dart';

/// Error handler utility for the Lucian Drives driver app
/// Converts various error types to AppError instances
class ErrorHandler {
  /// Convert DioException to AppError
  static AppError handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const AppError.network(
          message: 'Connection timeout. Please check your internet connection.',
          details: 'Request timed out',
        );

      case DioExceptionType.connectionError:
        return const AppError.network(
          message: AppConstants.networkErrorMessage,
          details: 'Failed to connect to server',
        );

      case DioExceptionType.badResponse:
        return _handleHttpError(error.response);

      case DioExceptionType.cancel:
        return const AppError.network(
          message: 'Request was cancelled',
          details: 'Request cancelled by user',
        );

      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return const AppError.network(
            message: AppConstants.networkErrorMessage,
            details: 'No internet connection',
          );
        }
        return AppError.unknown(
          message: 'An unexpected error occurred',
          exception: error.error,
        );

      default:
        return AppError.unknown(
          message: 'An unexpected error occurred',
          exception: error,
        );
    }
  }

  /// Handle HTTP response errors
  static AppError _handleHttpError(Response? response) {
    if (response == null) {
      return const AppError.server(
        message: AppConstants.serverErrorMessage,
        statusCode: 0,
      );
    }

    final statusCode = response.statusCode ?? 0;
    final data = response.data;

    // Extract error message from response
    String message = AppConstants.serverErrorMessage;
    String? errorCode;
    Map<String, String> fieldErrors = {};

    if (data is Map<String, dynamic>) {
      message = data['message'] ?? data['error'] ?? message;
      errorCode = data['error_code']?.toString();

      // Handle validation errors
      if (data['field_errors'] is Map) {
        fieldErrors = Map<String, String>.from(data['field_errors']);
      }
    }

    switch (statusCode) {
      case 400:
        if (fieldErrors.isNotEmpty) {
          return AppError.validation(
            message: message,
            fieldErrors: fieldErrors,
          );
        }
        return AppError.server(message: message, statusCode: statusCode);

      case 401:
        return AppError.authentication(
          message: message.isEmpty ? AppConstants.authErrorMessage : message,
          errorCode: errorCode,
        );

      case 403:
        return AppError.authentication(
          message: message.isEmpty ? 'Access denied' : message,
          errorCode: errorCode,
        );

      case 404:
        return AppError.server(
          message: message.isEmpty ? 'Resource not found' : message,
          statusCode: statusCode,
        );

      case 422:
        return AppError.validation(message: message, fieldErrors: fieldErrors);

      case 429:
        return AppError.server(
          message: message.isEmpty
              ? 'Too many requests. Please try again later.'
              : message,
          statusCode: statusCode,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return AppError.server(
          message: AppConstants.serverErrorMessage,
          statusCode: statusCode,
        );

      default:
        return AppError.server(message: message, statusCode: statusCode);
    }
  }

  /// Handle location-related errors
  static AppError handleLocationError(Object error) {
    if (error.toString().contains('permission')) {
      return const AppError.location(
        message: AppConstants.locationErrorMessage,
        errorType: LocationErrorType.permissionDenied,
      );
    }

    if (error.toString().contains('disabled')) {
      return const AppError.location(
        message:
            'Location services are disabled. Please enable them in settings.',
        errorType: LocationErrorType.serviceDisabled,
      );
    }

    if (error.toString().contains('timeout')) {
      return const AppError.location(
        message: 'Location request timed out. Please try again.',
        errorType: LocationErrorType.timeout,
      );
    }

    return const AppError.location(
      message: 'Unable to get location. Please try again.',
      errorType: LocationErrorType.unavailable,
    );
  }

  /// Handle document upload errors
  static AppError handleDocumentError(Object error) {
    final errorMessage = error.toString().toLowerCase();

    if (errorMessage.contains('size') || errorMessage.contains('large')) {
      return const AppError.document(
        message:
            'File size exceeds the maximum limit of ${AppConstants.maxDocumentSizeMB}MB',
        errorType: DocumentErrorType.fileTooLarge,
      );
    }

    if (errorMessage.contains('format') || errorMessage.contains('type')) {
      return const AppError.document(
        message: 'Invalid file format. Please use JPG, PNG, or PDF files.',
        errorType: DocumentErrorType.invalidFormat,
      );
    }

    if (errorMessage.contains('compression')) {
      return const AppError.document(
        message: 'Failed to compress image. Please try with a different image.',
        errorType: DocumentErrorType.compressionFailed,
      );
    }

    return const AppError.document(
      message: AppConstants.documentUploadErrorMessage,
      errorType: DocumentErrorType.uploadFailed,
    );
  }

  /// Get user-friendly error message
  static String getUserFriendlyMessage(AppError error) {
    return error.when(
      network: (message, details) => message,
      authentication: (message, errorCode) => message,
      validation: (message, fieldErrors) => message,
      location: (message, errorType) => message,
      document: (message, errorType) => message,
      server: (message, statusCode) => message,
      unknown: (message, exception) => message,
    );
  }

  /// Check if error is retryable
  static bool isRetryable(AppError error) {
    return error.when(
      network: (message, details) => true,
      authentication: (message, errorCode) => false,
      validation: (message, fieldErrors) => false,
      location: (message, errorType) =>
          errorType != LocationErrorType.permissionDenied,
      document: (message, errorType) =>
          errorType == DocumentErrorType.uploadFailed,
      server: (message, statusCode) => statusCode >= 500,
      unknown: (message, exception) => true,
    );
  }

  /// Get error severity level
  static ErrorSeverity getErrorSeverity(AppError error) {
    return error.when(
      network: (message, details) => ErrorSeverity.warning,
      authentication: (message, errorCode) => ErrorSeverity.error,
      validation: (message, fieldErrors) => ErrorSeverity.warning,
      location: (message, errorType) => ErrorSeverity.warning,
      document: (message, errorType) => ErrorSeverity.warning,
      server: (message, statusCode) =>
          statusCode >= 500 ? ErrorSeverity.error : ErrorSeverity.warning,
      unknown: (message, exception) => ErrorSeverity.error,
    );
  }
}

/// Error severity levels
enum ErrorSeverity { info, warning, error, critical }
