import 'package:flutter/material.dart';
import 'app_error.dart';
import 'error_handler.dart';
import '../../shared/widgets/error_display_widget.dart';

/// Extensions for AppError to provide additional functionality
extension AppErrorExtensions on AppError {
  /// Get user-friendly message
  String get userMessage => ErrorHandler.getUserFriendlyMessage(this);

  /// Check if error is retryable
  bool get isRetryable => ErrorHandler.isRetryable(this);

  /// Get error severity
  ErrorSeverity get severity => ErrorHandler.getErrorSeverity(this);

  /// Check if error is network-related
  bool get isNetworkError => when(
    network: (message, details) => true,
    authentication: (message, errorCode) => false,
    validation: (message, fieldErrors) => false,
    location: (message, errorType) => false,
    document: (message, errorType) => false,
    server: (message, statusCode) => false,
    unknown: (message, exception) => false,
  );

  /// Check if error is authentication-related
  bool get isAuthError => when(
    network: (message, details) => false,
    authentication: (message, errorCode) => true,
    validation: (message, fieldErrors) => false,
    location: (message, errorType) => false,
    document: (message, errorType) => false,
    server: (message, statusCode) => statusCode == 401 || statusCode == 403,
    unknown: (message, exception) => false,
  );

  /// Check if error is validation-related
  bool get isValidationError => when(
    network: (message, details) => false,
    authentication: (message, errorCode) => false,
    validation: (message, fieldErrors) => true,
    location: (message, errorType) => false,
    document: (message, errorType) => false,
    server: (message, statusCode) => statusCode == 422,
    unknown: (message, exception) => false,
  );

  /// Check if error is location-related
  bool get isLocationError => when(
    network: (message, details) => false,
    authentication: (message, errorCode) => false,
    validation: (message, fieldErrors) => false,
    location: (message, errorType) => true,
    document: (message, errorType) => false,
    server: (message, statusCode) => false,
    unknown: (message, exception) => false,
  );

  /// Check if error is document-related
  bool get isDocumentError => when(
    network: (message, details) => false,
    authentication: (message, errorCode) => false,
    validation: (message, fieldErrors) => false,
    location: (message, errorType) => false,
    document: (message, errorType) => true,
    server: (message, statusCode) => false,
    unknown: (message, exception) => false,
  );

  /// Get field errors for validation errors
  Map<String, String> get fieldErrors => when(
    network: (message, details) => {},
    authentication: (message, errorCode) => {},
    validation: (message, fieldErrors) => fieldErrors,
    location: (message, errorType) => {},
    document: (message, errorType) => {},
    server: (message, statusCode) => {},
    unknown: (message, exception) => {},
  );

  /// Get error code for authentication errors
  String? get errorCode => when(
    network: (message, details) => null,
    authentication: (message, errorCode) => errorCode,
    validation: (message, fieldErrors) => null,
    location: (message, errorType) => null,
    document: (message, errorType) => null,
    server: (message, statusCode) => statusCode.toString(),
    unknown: (message, exception) => null,
  );

  /// Get status code for server errors
  int? get statusCode => when(
    network: (message, details) => null,
    authentication: (message, errorCode) => null,
    validation: (message, fieldErrors) => null,
    location: (message, errorType) => null,
    document: (message, errorType) => null,
    server: (message, statusCode) => statusCode,
    unknown: (message, exception) => null,
  );

  /// Convert to widget for display
  Widget toWidget({
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
    bool showRetryButton = true,
    bool showDismissButton = false,
    EdgeInsets? padding,
  }) {
    return ErrorDisplayWidget(
      error: this,
      onRetry: onRetry,
      onDismiss: onDismiss,
      showRetryButton: showRetryButton,
      showDismissButton: showDismissButton,
      padding: padding,
    );
  }

  /// Convert to compact widget for inline display
  Widget toCompactWidget({VoidCallback? onRetry}) {
    return CompactErrorWidget(error: this, onRetry: onRetry);
  }

  /// Show as snackbar
  void showSnackBar(
    BuildContext context, {
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) {
    final messenger = ScaffoldMessenger.of(context);
    messenger.hideCurrentSnackBar();

    final snackBar = SnackBar(
      content: Text(userMessage),
      duration: duration,
      action: isRetryable && onRetry != null
          ? SnackBarAction(label: 'Retry', onPressed: onRetry)
          : null,
      behavior: SnackBarBehavior.floating,
    );

    messenger.showSnackBar(snackBar);
  }

  /// Show as dialog
  Future<void> showErrorDialog(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
    bool barrierDismissible = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AlertDialog(
        title: Text(_getDialogTitle()),
        content: Text(userMessage),
        actions: [
          if (onDismiss != null)
            TextButton(onPressed: onDismiss, child: const Text('Dismiss')),
          if (isRetryable && onRetry != null)
            TextButton(onPressed: onRetry, child: const Text('Retry')),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  String _getDialogTitle() {
    switch (severity) {
      case ErrorSeverity.info:
        return 'Information';
      case ErrorSeverity.warning:
        return 'Warning';
      case ErrorSeverity.error:
        return 'Error';
      case ErrorSeverity.critical:
        return 'Critical Error';
    }
  }
}

/// Extensions for handling multiple errors
extension AppErrorListExtensions on List<AppError> {
  /// Get the most severe error
  AppError? get mostSevere {
    if (isEmpty) return null;

    return reduce((current, next) {
      final currentSeverity = current.severity;
      final nextSeverity = next.severity;

      final severityOrder = [
        ErrorSeverity.info,
        ErrorSeverity.warning,
        ErrorSeverity.error,
        ErrorSeverity.critical,
      ];

      final currentIndex = severityOrder.indexOf(currentSeverity);
      final nextIndex = severityOrder.indexOf(nextSeverity);

      return nextIndex > currentIndex ? next : current;
    });
  }

  /// Get all validation errors
  List<AppError> get validationErrors {
    return where((error) => error.isValidationError).toList();
  }

  /// Get all network errors
  List<AppError> get networkErrors {
    return where((error) => error.isNetworkError).toList();
  }

  /// Get all authentication errors
  List<AppError> get authErrors {
    return where((error) => error.isAuthError).toList();
  }

  /// Get all location errors
  List<AppError> get locationErrors {
    return where((error) => error.isLocationError).toList();
  }

  /// Get all document errors
  List<AppError> get documentErrors {
    return where((error) => error.isDocumentError).toList();
  }

  /// Check if any error is retryable
  bool get hasRetryableError {
    return any((error) => error.isRetryable);
  }

  /// Get combined field errors from all validation errors
  Map<String, String> get combinedFieldErrors {
    final Map<String, String> combined = {};
    for (final error in validationErrors) {
      combined.addAll(error.fieldErrors);
    }
    return combined;
  }
}
