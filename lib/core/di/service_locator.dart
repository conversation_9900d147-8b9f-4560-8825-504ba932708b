import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

// Import services
import '../../services/api/api_client.dart';
import '../../services/api/dio_api_client.dart';
import '../../services/storage/storage_service.dart';
import '../../services/storage/secure_storage_service.dart';
import '../../services/storage/driver_storage_helper.dart';
import '../../services/driver/driver_service.dart';
import '../../services/driver/driver_service_impl.dart';
import '../../services/documents/document_service.dart';
import '../../services/documents/document_service_impl.dart';
import '../../services/earnings/earnings_service.dart';
import '../../services/earnings/earnings_service_impl.dart';
import '../../services/location/location_service.dart';
import '../../services/location/location_service_impl.dart';

// Import repositories
import '../../features/auth/domain/auth_repository.dart';
import '../../features/auth/data/auth_repository_impl.dart';
import '../../features/driver/domain/driver_repository.dart';
import '../../features/driver/data/driver_repository_impl.dart';
import '../../features/documents/domain/document_repository.dart';
import '../../features/documents/data/document_repository_impl.dart';

// import '../services/auth/auth_service.dart';
// import '../services/location/location_service.dart';
// import '../services/driver/driver_service.dart';
// import '../services/document/document_service.dart';

/// Service locator for dependency injection
final GetIt getIt = GetIt.instance;

/// Initialize all dependencies for the driver app
Future<void> setupServiceLocator() async {
  // Core dependencies
  _registerCoreDependencies();

  // Services
  _registerServices();

  // Repositories (will be added in later tasks)
  _registerRepositories();
}

void _registerCoreDependencies() {
  // HTTP client
  getIt.registerLazySingleton<Dio>(() {
    final dio = Dio();
    dio.options.baseUrl = 'http://localhost:8000/api/v1';
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);

    // Add interceptors for logging and authentication (will be implemented later)
    // dio.interceptors.add(AuthInterceptor());
    // dio.interceptors.add(LoggingInterceptor());

    return dio;
  });

  // Secure storage
  getIt.registerLazySingleton<FlutterSecureStorage>(() {
    return const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    );
  });
}

void _registerServices() {
  // API Client
  getIt.registerLazySingleton<ApiClient>(() => DioApiClient());

  // Storage Service
  getIt.registerLazySingleton<StorageService>(() => SecureStorageService());

  // Driver Storage Helper
  getIt.registerLazySingleton<DriverStorageHelper>(
    () => DriverStorageHelper(getIt<StorageService>()),
  );

  // Authentication Service (will be implemented in later tasks)
  // getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl(
  //   apiClient: getIt<ApiClient>(),
  //   storageService: getIt<StorageService>(),
  // ));

  // Location Service
  getIt.registerLazySingleton<LocationService>(
    () => LocationServiceImpl(apiClient: getIt<ApiClient>()),
  );

  // Driver Service
  getIt.registerLazySingleton<DriverService>(
    () => DriverServiceImpl(
      apiClient: getIt<ApiClient>(),
      storageHelper: getIt<DriverStorageHelper>(),
    ),
  );

  // Document Service
  getIt.registerLazySingleton<DocumentService>(
    () => DocumentServiceImpl(apiClient: getIt<ApiClient>()),
  );

  // Earnings Service
  getIt.registerLazySingleton<EarningsService>(
    () => EarningsServiceImpl(
      apiClient: getIt<ApiClient>(),
      storageHelper: getIt<DriverStorageHelper>(),
    ),
  );
}

void _registerRepositories() {
  // Auth Repository
  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      apiClient: getIt<ApiClient>(),
      storageService: getIt<StorageService>(),
    ),
  );

  // Driver Repository
  getIt.registerLazySingleton<DriverRepository>(
    () => DriverRepositoryImpl(
      apiClient: getIt<ApiClient>(),
      storageHelper: getIt<DriverStorageHelper>(),
    ),
  );

  // Document Repository
  getIt.registerLazySingleton<DocumentRepository>(
    () => DocumentRepositoryImpl(
      apiClient: getIt<ApiClient>(),
      storageService: getIt<StorageService>(),
    ),
  );
}

/// Clean up all registered dependencies
void resetServiceLocator() {
  getIt.reset();
}
