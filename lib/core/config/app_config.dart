/// Application configuration for the Lucian Drives driver app
class AppConfig {
  // Environment configuration
  static const String environment = String.fromEnvironment(
    'ENV',
    defaultValue: 'development',
  );
  static const bool isProduction = environment == 'production';
  static const bool isDevelopment = environment == 'development';

  // API configuration
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://localhost:8000/api/v1',
  );

  // Feature flags
  static const bool enableLogging = !isProduction;
  static const bool enableDebugMode = isDevelopment;

  // App metadata
  static const String appName = 'Lucian Drives - Driver';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';

  /// Get the current configuration as a map
  static Map<String, dynamic> toMap() {
    return {
      'environment': environment,
      'isProduction': isProduction,
      'isDevelopment': isDevelopment,
      'apiBaseUrl': apiBaseUrl,
      'enableLogging': enableLogging,
      'enableDebugMode': enableDebugMode,
      'appName': appName,
      'appVersion': appVersion,
      'buildNumber': buildNumber,
    };
  }
}
