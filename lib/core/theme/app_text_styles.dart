import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Text styles for the Lucian Drives driver app
/// Based on design.json typography specifications
class AppTextStyles {
  // Font family
  static const String primaryFontFamily = 'Roboto';
  static const String secondaryFontFamily = 'Inter';

  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semibold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;

  // Heading styles
  static const TextStyle heading1 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 32,
    fontWeight: bold,
    color: AppColors.textPrimary,
    height: 1.2,
  );

  static const TextStyle heading2 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 24,
    fontWeight: semibold,
    color: AppColors.textPrimary,
    height: 1.2,
  );

  static const TextStyle heading3 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 20,
    fontWeight: semibold,
    color: AppColors.textPrimary,
    height: 1.2,
  );

  static const TextStyle heading4 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 18,
    fontWeight: medium,
    color: AppColors.textPrimary,
    height: 1.2,
  );

  // Body text styles
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.textPrimary,
    height: 1.4,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: regular,
    color: AppColors.textPrimary,
    height: 1.4,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.textSecondary,
    height: 1.4,
  );

  // Button text styles
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: semibold,
    color: AppColors.textOnPrimary,
    height: 1.2,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.textOnPrimary,
    height: 1.2,
  );

  static const TextStyle buttonSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: medium,
    color: AppColors.textOnPrimary,
    height: 1.2,
  );

  // Label styles
  static const TextStyle labelLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.textPrimary,
    height: 1.2,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: medium,
    color: AppColors.textSecondary,
    height: 1.2,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 10,
    fontWeight: medium,
    color: AppColors.textSecondary,
    height: 1.2,
  );

  // Caption styles
  static const TextStyle caption = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.textSecondary,
    height: 1.4,
  );

  static const TextStyle overline = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 10,
    fontWeight: medium,
    color: AppColors.textSecondary,
    height: 1.6,
    letterSpacing: 1.5,
  );

  // Driver-specific text styles
  static const TextStyle availabilityStatus = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: semibold,
    height: 1.2,
  );

  static const TextStyle earningsAmount = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 24,
    fontWeight: bold,
    color: AppColors.earningsGreen,
    height: 1.2,
  );

  static const TextStyle documentStatus = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: medium,
    height: 1.2,
  );

  static const TextStyle navigationTitle = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 18,
    fontWeight: semibold,
    color: AppColors.textPrimary,
    height: 1.2,
  );

  static const TextStyle tabLabel = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 10,
    fontWeight: medium,
    height: 1.2,
  );

  // Input field styles
  static const TextStyle inputText = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.textPrimary,
    height: 1.4,
  );

  static const TextStyle inputHint = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.textHint,
    height: 1.4,
  );

  static const TextStyle inputLabel = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.textSecondary,
    height: 1.2,
  );

  static const TextStyle inputError = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.error,
    height: 1.4,
  );
}
