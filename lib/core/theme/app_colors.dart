import 'package:flutter/material.dart';

/// Color palette for the Lucian Drives driver app
/// Based on design.json specifications with driver-focused optimizations
class AppColors {
  // Primary Colors - Teal theme for driver app
  static const Color primary = Color(0xFF00B5A5);
  static const Color primaryDark = Color(0xFF008B7A);
  static const Color primaryLight = Color(0xFF4DD0C7);

  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color mediumGray = Color(0xFFE0E0E0);
  static const Color darkGray = Color(0xFF666666);
  static const Color charcoal = Color(0xFF2C2C2C);
  static const Color black = Color(0xFF000000);

  // Accent Colors
  static const Color blue = Color(0xFF007AFF);
  static const Color yellow = Color(0xFFFFD700);
  static const Color green = Color(0xFF34C759);
  static const Color orange = Color(0xFFFF9500);

  // Status Colors
  static const Color success = Color(0xFF34C759);
  static const Color warning = Color(0xFFFF9500);
  static const Color error = Color(0xFFFF3B30);
  static const Color info = Color(0xFF007AFF);

  // Driver-specific colors
  static const Color onlineStatus = Color(0xFF34C759);
  static const Color offlineStatus = Color(0xFF666666);
  static const Color earningsGreen = Color(0xFF34C759);
  static const Color documentPending = Color(0xFFFF9500);
  static const Color documentApproved = Color(0xFF34C759);
  static const Color documentRejected = Color(0xFFFF3B30);

  // Background colors
  static const Color background = Color(0xFFFFFFFF);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);

  // Text colors
  static const Color textPrimary = Color(0xFF000000);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textHint = Color(0xFF666666);

  // Border colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderFocus = Color(0xFF00B5A5);

  // Shadow colors
  static const Color shadowLight = Color(0x0D000000); // rgba(0, 0, 0, 0.05)
  static const Color shadowMedium = Color(0x1A000000); // rgba(0, 0, 0, 0.1)
  static const Color shadowDark = Color(0x26000000); // rgba(0, 0, 0, 0.15)

  // Overlay colors
  static const Color overlay = Color(0x80000000); // rgba(0, 0, 0, 0.5)
  static const Color overlayLight = Color(0x4D000000); // rgba(0, 0, 0, 0.3)

  /// Get Material color swatch for primary color
  static MaterialColor get primarySwatch {
    return MaterialColor(0xFF00B5A5, <int, Color>{
      50: const Color(0xFFE0F7F5),
      100: const Color(0xFFB3EBE6),
      200: const Color(0xFF80DDD5),
      300: const Color(0xFF4DD0C7),
      400: const Color(0xFF26C5BB),
      500: primary,
      600: const Color(0xFF00A396),
      700: const Color(0xFF008B7A),
      800: const Color(0xFF00735F),
      900: const Color(0xFF004D3F),
    });
  }
}
