import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';

/// Accessibility utilities for WCAG AA compliance
/// Provides helpers for screen reader support, touch targets, and color contrast
class AccessibilityUtils {
  /// Minimum touch target size according to WCAG AA guidelines
  static const double minTouchTargetSize = 44.0;

  /// Minimum color contrast ratio for WCAG AA compliance
  static const double minContrastRatio = 4.5;

  /// Minimum color contrast ratio for large text (WCAG AA)
  static const double minLargeTextContrastRatio = 3.0;

  /// Large text threshold (18pt or 14pt bold)
  static const double largeTextSize = 18.0;

  /// Check if a touch target meets minimum size requirements
  static bool isTouchTargetAccessible(Size size) {
    return size.width >= minTouchTargetSize &&
        size.height >= minTouchTargetSize;
  }

  /// Calculate color contrast ratio between two colors
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);

    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculate relative luminance of a color
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent((color.r * 255.0).round() / 255.0);
    final g = _linearizeColorComponent((color.g * 255.0).round() / 255.0);
    final b = _linearizeColorComponent((color.b * 255.0).round() / 255.0);

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearize color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return math.pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Check if color combination meets WCAG AA contrast requirements
  static bool meetsContrastRequirements(
    Color foreground,
    Color background, {
    bool isLargeText = false,
  }) {
    final contrastRatio = calculateContrastRatio(foreground, background);
    final requiredRatio = isLargeText
        ? minLargeTextContrastRatio
        : minContrastRatio;
    return contrastRatio >= requiredRatio;
  }

  /// Get accessible color variant if current combination doesn't meet requirements
  static Color getAccessibleColor(
    Color originalColor,
    Color backgroundColor, {
    bool isLargeText = false,
  }) {
    if (meetsContrastRequirements(
      originalColor,
      backgroundColor,
      isLargeText: isLargeText,
    )) {
      return originalColor;
    }

    // Try darker variant
    Color darkerColor = _darkenColor(originalColor, 0.2);
    if (meetsContrastRequirements(
      darkerColor,
      backgroundColor,
      isLargeText: isLargeText,
    )) {
      return darkerColor;
    }

    // Try lighter variant
    Color lighterColor = _lightenColor(originalColor, 0.2);
    if (meetsContrastRequirements(
      lighterColor,
      backgroundColor,
      isLargeText: isLargeText,
    )) {
      return lighterColor;
    }

    // Fallback to high contrast colors
    final backgroundLuminance = _calculateLuminance(backgroundColor);
    return backgroundLuminance > 0.5 ? AppColors.black : AppColors.white;
  }

  /// Darken a color by a given factor
  static Color _darkenColor(Color color, double factor) {
    final hsl = HSLColor.fromColor(color);
    return hsl
        .withLightness((hsl.lightness * (1 - factor)).clamp(0.0, 1.0))
        .toColor();
  }

  /// Lighten a color by a given factor
  static Color _lightenColor(Color color, double factor) {
    final hsl = HSLColor.fromColor(color);
    return hsl
        .withLightness(
          (hsl.lightness + (1 - hsl.lightness) * factor).clamp(0.0, 1.0),
        )
        .toColor();
  }

  /// Create semantic label for screen readers
  static String createSemanticLabel({
    required String label,
    String? value,
    String? hint,
    String? state,
  }) {
    final parts = <String>[label];

    if (value != null && value.isNotEmpty) {
      parts.add(value);
    }

    if (state != null && state.isNotEmpty) {
      parts.add(state);
    }

    if (hint != null && hint.isNotEmpty) {
      parts.add(hint);
    }

    return parts.join(', ');
  }

  /// Provide haptic feedback for accessibility
  static void provideHapticFeedback() {
    HapticFeedback.selectionClick();
  }

  /// Driver-specific accessibility labels
  static String getAvailabilityLabel(bool isOnline) {
    return isOnline
        ? 'Driver status: Online, ready to accept rides'
        : 'Driver status: Offline, not accepting rides';
  }

  static String getDocumentStatusLabel(String status, String documentType) {
    switch (status.toLowerCase()) {
      case 'approved':
        return '$documentType: Approved and verified';
      case 'rejected':
        return '$documentType: Rejected, needs attention';
      case 'pending':
      default:
        return '$documentType: Pending verification';
    }
  }

  static String getEarningsLabel(String amount, String period) {
    return 'Earnings for $period: $amount';
  }

  static String getLocationLabel(bool hasPermission) {
    return hasPermission
        ? 'Location access: Enabled'
        : 'Location access: Disabled, tap to enable';
  }
}

/// Accessible widget wrapper that ensures minimum touch target size
class AccessibleTouchTarget extends StatelessWidget {
  const AccessibleTouchTarget({
    super.key,
    required this.child,
    this.onTap,
    this.semanticLabel,
    this.semanticHint,
    this.minSize = AccessibilityUtils.minTouchTargetSize,
  });

  final Widget child;
  final VoidCallback? onTap;
  final String? semanticLabel;
  final String? semanticHint;
  final double minSize;

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      child: GestureDetector(
        onTap: onTap,
        child: ConstrainedBox(
          constraints: BoxConstraints(minWidth: minSize, minHeight: minSize),
          child: child,
        ),
      ),
    );
  }
}

/// Accessible button that ensures proper semantics and touch targets
class AccessibleButton extends StatelessWidget {
  const AccessibleButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.semanticLabel,
    this.semanticHint,
    this.isEnabled = true,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
  });

  final Widget child;
  final VoidCallback? onPressed;
  final String? semanticLabel;
  final String? semanticHint;
  final bool isEnabled;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final BorderRadius? borderRadius;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    final effectiveForegroundColor = foregroundColor ?? AppColors.textPrimary;
    final effectiveBackgroundColor = backgroundColor ?? AppColors.white;

    // Ensure color contrast compliance
    final accessibleForegroundColor = AccessibilityUtils.getAccessibleColor(
      effectiveForegroundColor,
      effectiveBackgroundColor,
    );

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      enabled: isEnabled,
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          minWidth: AccessibilityUtils.minTouchTargetSize,
          minHeight: AccessibilityUtils.minTouchTargetSize,
        ),
        child: Material(
          color: effectiveBackgroundColor,
          borderRadius: borderRadius ?? BorderRadius.circular(8),
          child: InkWell(
            onTap: isEnabled ? onPressed : null,
            borderRadius: borderRadius ?? BorderRadius.circular(8),
            child: Padding(
              padding: padding ?? const EdgeInsets.all(12),
              child: DefaultTextStyle(
                style: TextStyle(color: accessibleForegroundColor),
                child: child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Accessible text field with proper semantics
class AccessibleTextField extends StatelessWidget {
  const AccessibleTextField({
    super.key,
    required this.controller,
    this.labelText,
    this.hintText,
    this.errorText,
    this.semanticLabel,
    this.semanticHint,
    this.onChanged,
    this.keyboardType,
    this.textInputAction,
    this.obscureText = false,
    this.enabled = true,
  });

  final TextEditingController controller;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final String? semanticLabel;
  final String? semanticHint;
  final ValueChanged<String>? onChanged;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool obscureText;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final effectiveLabel =
        semanticLabel ?? labelText ?? hintText ?? 'Text input';
    final effectiveHint =
        semanticHint ?? (errorText != null ? 'Error: $errorText' : null);

    return Semantics(
      label: effectiveLabel,
      hint: effectiveHint,
      textField: true,
      enabled: enabled,
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          minHeight: AccessibilityUtils.minTouchTargetSize,
        ),
        child: TextFormField(
          controller: controller,
          onChanged: onChanged,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          obscureText: obscureText,
          enabled: enabled,
          decoration: InputDecoration(
            labelText: labelText,
            hintText: hintText,
            errorText: errorText,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ),
    );
  }
}

/// Driver-specific accessible components
class DriverAccessibilityComponents {
  /// Accessible availability toggle
  static Widget availabilityToggle({
    required bool isOnline,
    required ValueChanged<bool> onChanged,
    bool isEnabled = true,
  }) {
    final label = AccessibilityUtils.getAvailabilityLabel(isOnline);
    final hint = isEnabled
        ? 'Double tap to ${isOnline ? 'go offline' : 'go online'}'
        : 'Availability toggle disabled';

    return Semantics(
      label: label,
      hint: hint,
      toggled: isOnline,
      enabled: isEnabled,
      child: AccessibleTouchTarget(
        onTap: isEnabled ? () => onChanged(!isOnline) : null,
        child: Switch(value: isOnline, onChanged: isEnabled ? onChanged : null),
      ),
    );
  }

  /// Accessible document status indicator
  static Widget documentStatusIndicator({
    required String status,
    required String documentType,
    VoidCallback? onTap,
  }) {
    final label = AccessibilityUtils.getDocumentStatusLabel(
      status,
      documentType,
    );
    final hint = onTap != null ? 'Double tap to view details' : null;

    return Semantics(
      label: label,
      hint: hint,
      button: onTap != null,
      child: AccessibleTouchTarget(
        onTap: onTap,
        semanticLabel: label,
        semanticHint: hint,
        child: Row(
          children: [
            Icon(
              status.toLowerCase() == 'approved'
                  ? Icons.check_circle
                  : status.toLowerCase() == 'rejected'
                  ? Icons.error
                  : Icons.schedule,
              color: status.toLowerCase() == 'approved'
                  ? AppColors.success
                  : status.toLowerCase() == 'rejected'
                  ? AppColors.error
                  : AppColors.warning,
            ),
            const SizedBox(width: 8),
            Text(documentType),
          ],
        ),
      ),
    );
  }

  /// Accessible earnings display
  static Widget earningsDisplay({
    required String amount,
    required String period,
    VoidCallback? onTap,
  }) {
    final label = AccessibilityUtils.getEarningsLabel(amount, period);
    final hint = onTap != null ? 'Double tap to view earnings details' : null;

    return Semantics(
      label: label,
      hint: hint,
      button: onTap != null,
      child: AccessibleTouchTarget(
        onTap: onTap,
        semanticLabel: label,
        semanticHint: hint,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              amount,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.earningsGreen,
              ),
            ),
            Text(
              period,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Extension to add accessibility helpers to existing widgets
extension AccessibilityExtensions on Widget {
  /// Add semantic label to any widget
  Widget withSemantics({
    String? label,
    String? hint,
    bool? button,
    bool? enabled,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: button,
      enabled: enabled,
      child: this,
    );
  }

  /// Ensure minimum touch target size
  Widget withMinTouchTarget({
    double minSize = AccessibilityUtils.minTouchTargetSize,
  }) {
    return ConstrainedBox(
      constraints: BoxConstraints(minWidth: minSize, minHeight: minSize),
      child: this,
    );
  }
}
