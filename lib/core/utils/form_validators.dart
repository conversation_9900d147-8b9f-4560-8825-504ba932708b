/// Form validation utilities for driver data
class FormValidators {
  FormValidators._();

  /// Validates driver license number
  static String? validateLicenseNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'License number is required';
    }

    final trimmed = value.trim();
    if (trimmed.length < 5) {
      return 'License number must be at least 5 characters';
    }

    if (trimmed.length > 20) {
      return 'License number must be less than 20 characters';
    }

    // Basic alphanumeric validation
    if (!RegExp(r'^[a-zA-Z0-9\-\s]+$').hasMatch(trimmed)) {
      return 'License number can only contain letters, numbers, hyphens, and spaces';
    }

    return null;
  }

  /// Validates Stripe account ID (optional field)
  static String? validateStripeAccountId(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Optional field
    }

    final trimmed = value.trim();

    // Basic Stripe account ID format validation (acct_xxxx)
    if (!RegExp(r'^acct_[a-zA-Z0-9]{16,}$').hasMatch(trimmed)) {
      return 'Invalid Stripe account ID format (should start with "acct_")';
    }

    return null;
  }

  /// Validates vehicle make
  static String? validateVehicleMake(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Vehicle make is required';
    }

    final trimmed = value.trim();
    if (trimmed.length < 2) {
      return 'Vehicle make must be at least 2 characters';
    }

    if (trimmed.length > 30) {
      return 'Vehicle make must be less than 30 characters';
    }

    // Only letters, spaces, and hyphens allowed
    if (!RegExp(r'^[a-zA-Z\s\-]+$').hasMatch(trimmed)) {
      return 'Vehicle make can only contain letters, spaces, and hyphens';
    }

    return null;
  }

  /// Validates vehicle model
  static String? validateVehicleModel(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Vehicle model is required';
    }

    final trimmed = value.trim();
    if (trimmed.isEmpty) {
      return 'Vehicle model is required';
    }

    if (trimmed.length > 30) {
      return 'Vehicle model must be less than 30 characters';
    }

    // Letters, numbers, spaces, and hyphens allowed
    if (!RegExp(r'^[a-zA-Z0-9\s\-]+$').hasMatch(trimmed)) {
      return 'Vehicle model can only contain letters, numbers, spaces, and hyphens';
    }

    return null;
  }

  /// Validates vehicle year
  static String? validateVehicleYear(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Year is required';
    }

    final year = int.tryParse(value.trim());
    if (year == null) {
      return 'Please enter a valid year';
    }

    final currentYear = DateTime.now().year;
    if (year < 1990) {
      return 'Vehicle must be 1990 or newer';
    }

    if (year > currentYear + 1) {
      return 'Year cannot be in the future';
    }

    return null;
  }

  /// Validates vehicle color
  static String? validateVehicleColor(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Vehicle color is required';
    }

    final trimmed = value.trim();
    if (trimmed.length < 3) {
      return 'Vehicle color must be at least 3 characters';
    }

    if (trimmed.length > 20) {
      return 'Vehicle color must be less than 20 characters';
    }

    // Only letters and spaces allowed
    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(trimmed)) {
      return 'Vehicle color can only contain letters and spaces';
    }

    return null;
  }

  /// Validates license plate
  static String? validateLicensePlate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'License plate is required';
    }

    final trimmed = value.trim().toUpperCase();
    if (trimmed.length < 3) {
      return 'License plate must be at least 3 characters';
    }

    if (trimmed.length > 10) {
      return 'License plate must be less than 10 characters';
    }

    // Letters, numbers, and hyphens allowed
    if (!RegExp(r'^[A-Z0-9\-]+$').hasMatch(trimmed)) {
      return 'License plate can only contain letters, numbers, and hyphens';
    }

    return null;
  }

  /// Validates email format
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }

    final trimmed = value.trim();
    if (!RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(trimmed)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validates password strength
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }

    if (value.length > 128) {
      return 'Password must be less than 128 characters';
    }

    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }

    // Check for at least one lowercase letter
    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter';
    }

    // Check for at least one number
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }

    return null;
  }

  /// Validates phone number (basic validation)
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }

    final trimmed = value.trim();

    // Remove common formatting characters
    final cleaned = trimmed.replaceAll(RegExp(r'[\s\-\(\)\+]'), '');

    if (cleaned.length < 10) {
      return 'Phone number must be at least 10 digits';
    }

    if (cleaned.length > 15) {
      return 'Phone number must be less than 15 digits';
    }

    // Only digits allowed after cleaning
    if (!RegExp(r'^[0-9]+$').hasMatch(cleaned)) {
      return 'Phone number can only contain digits';
    }

    return null;
  }

  /// Validates required text field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validates text length
  static String? validateLength(
    String? value,
    String fieldName, {
    int? minLength,
    int? maxLength,
  }) {
    if (value == null) return null;

    final trimmed = value.trim();

    if (minLength != null && trimmed.length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }

    if (maxLength != null && trimmed.length > maxLength) {
      return '$fieldName must be less than $maxLength characters';
    }

    return null;
  }

  /// Combines multiple validators
  static String? combineValidators(
    String? value,
    List<String? Function(String?)> validators,
  ) {
    for (final validator in validators) {
      final error = validator(value);
      if (error != null) return error;
    }
    return null;
  }
}
