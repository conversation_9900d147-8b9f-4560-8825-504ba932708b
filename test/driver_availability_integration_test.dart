import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:lucian_drives_app/features/driver/presentation/providers/driver_availability_notifier.dart';
import 'package:lucian_drives_app/features/driver/presentation/providers/driver_profile_state.dart';
import 'package:lucian_drives_app/shared/models/models.dart';

void main() {
  group('Driver Availability Location Integration', () {
    test('should have proper state transitions for availability', () {
      // Test that the DriverAvailabilityState has all required states
      const offlineState = DriverAvailabilityState.offline();
      const goingOnlineState = DriverAvailabilityState.goingOnline();
      const goingOfflineState = DriverAvailabilityState.goingOffline();
      const errorState = DriverAvailabilityState.error(message: 'Test error');

      expect(offlineState, isA<DriverAvailabilityOffline>());
      expect(goingOnlineState, isA<DriverAvailabilityGoingOnline>());
      expect(goingOfflineState, isA<DriverAvailabilityGoingOffline>());
      expect(errorState, isA<DriverAvailabilityError>());
    });

    test('should handle online state with location', () {
      final onlineState = DriverAvailabilityState.online(
        location: LocationInfo(
          latitude: 0.0,
          longitude: 0.0,
          updatedAt: DateTime.now(),
        ),
      );
      expect(onlineState, isA<DriverAvailabilityOnline>());

      onlineState.whenOrNull(
        online: (location) {
          // Location should be provided
          expect(location, isNotNull);
          expect(location.latitude, equals(0.0));
          expect(location.longitude, equals(0.0));
        },
      );
    });

    test('should provide correct availability status through providers', () {
      final container = ProviderContainer();

      // Test initial state
      final initialOnlineStatus = container.read(isDriverOnlineProvider);
      final initialOfflineStatus = container.read(isDriverOfflineProvider);
      final initialChangingStatus = container.read(
        isAvailabilityChangingProvider,
      );

      expect(initialOnlineStatus, isFalse);
      expect(initialOfflineStatus, isTrue);
      expect(initialChangingStatus, isFalse);

      container.dispose();
    });

    test('should handle location provider correctly', () {
      final container = ProviderContainer();

      // Test initial location
      final initialLocation = container.read(currentDriverLocationProvider);
      expect(initialLocation, isNull);

      container.dispose();
    });

    test(
      'should have proper integration between availability and location tracking',
      () {
        // Test that the notifier has all required methods for integration
        final container = ProviderContainer();
        final notifier = container.read(driverAvailabilityProvider.notifier);

        // Verify notifier has required methods
        expect(notifier.isOnline, isFalse);
        expect(notifier.isOffline, isTrue);
        expect(notifier.isChanging, isFalse);
        expect(notifier.currentLocation, isNull);

        container.dispose();
      },
    );

    test('should handle error states correctly', () {
      const errorState = DriverAvailabilityState.error(
        message: 'Location permission denied',
      );

      errorState.whenOrNull(
        error: (message) {
          expect(message, equals('Location permission denied'));
        },
      );
    });

    test('should support state pattern matching', () {
      final states = [
        const DriverAvailabilityState.offline(),
        const DriverAvailabilityState.goingOnline(),
        DriverAvailabilityState.online(
          location: LocationInfo(
            latitude: 0.0,
            longitude: 0.0,
            updatedAt: DateTime.now(),
          ),
        ),
        const DriverAvailabilityState.goingOffline(),
        const DriverAvailabilityState.error(message: 'Test error'),
      ];

      for (final state in states) {
        // Test that each state can be pattern matched
        final result = state.when(
          offline: () => 'offline',
          goingOnline: () => 'going_online',
          online: (location) => 'online',
          goingOffline: () => 'going_offline',
          error: (message) => 'error',
        );

        expect(result, isA<String>());
      }
    });

    test('should maintain consistency between state and provider values', () {
      final container = ProviderContainer();

      // Get the current state
      final state = container.read(driverAvailabilityProvider);
      final isOnline = container.read(isDriverOnlineProvider);
      final isOffline = container.read(isDriverOfflineProvider);
      final isChanging = container.read(isAvailabilityChangingProvider);

      // Verify consistency
      if (state is DriverAvailabilityOnline) {
        expect(isOnline, isTrue);
        expect(isOffline, isFalse);
        expect(isChanging, isFalse);
      } else if (state is DriverAvailabilityOffline) {
        expect(isOnline, isFalse);
        expect(isOffline, isTrue);
        expect(isChanging, isFalse);
      } else if (state is DriverAvailabilityGoingOnline ||
          state is DriverAvailabilityGoingOffline) {
        expect(isChanging, isTrue);
      }

      container.dispose();
    });
  });
}
