import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lucian_drives_app/shared/widgets/custom_button.dart';
import 'package:lucian_drives_app/shared/widgets/custom_text_field.dart';
import 'package:lucian_drives_app/shared/widgets/design_system_components.dart';
import 'package:lucian_drives_app/shared/widgets/loading_components.dart';
import 'package:lucian_drives_app/core/utils/accessibility_utils.dart';

void main() {
  group('UI Components Tests', () {
    testWidgets('CustomButton renders correctly', (WidgetTester tester) async {
      bool buttonPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Test Button',
              onPressed: () {
                buttonPressed = true;
              },
            ),
          ),
        ),
      );

      expect(find.text('Test Button'), findsOneWidget);
      
      await tester.tap(find.text('Test Button'));
      expect(buttonPressed, isTrue);
    });

    testWidgets('CustomTextField renders correctly', (WidgetTester tester) async {
      final controller = TextEditingController();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomTextField(
              label: 'Test Field',
              controller: controller,
            ),
          ),
        ),
      );

      expect(find.text('Test Field'), findsOneWidget);
      
      await tester.enterText(find.byType(TextFormField), 'test input');
      expect(controller.text, equals('test input'));
    });

    testWidgets('DSCard renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DSCard(
              child: Text('Card Content'),
            ),
          ),
        ),
      );

      expect(find.text('Card Content'), findsOneWidget);
      expect(find.byType(Card), findsOneWidget);
    });

    testWidgets('DSLoadingIndicator renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DSLoadingIndicator(),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('DSBadge renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DSBadge(text: '5'),
          ),
        ),
      );

      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('AccessibleTouchTarget ensures minimum size', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AccessibleTouchTarget(
              child: Container(
                width: 20,
                height: 20,
                color: Colors.red,
              ),
            ),
          ),
        ),
      );

      final constrainedBox = tester.widget<ConstrainedBox>(
        find.byType(ConstrainedBox),
      );
      
      expect(constrainedBox.constraints.minWidth, equals(AccessibilityUtils.minTouchTargetSize));
      expect(constrainedBox.constraints.minHeight, equals(AccessibilityUtils.minTouchTargetSize));
    });

    test('AccessibilityUtils color contrast calculation', () {
      const white = Colors.white;
      const black = Colors.black;
      
      final contrastRatio = AccessibilityUtils.calculateContrastRatio(black, white);
      expect(contrastRatio, greaterThan(AccessibilityUtils.minContrastRatio));
      
      final meetsRequirements = AccessibilityUtils.meetsContrastRequirements(black, white);
      expect(meetsRequirements, isTrue);
    });

    test('AccessibilityUtils semantic label creation', () {
      final label = AccessibilityUtils.createSemanticLabel(
        label: 'Button',
        value: 'Submit',
        hint: 'Double tap to submit',
        state: 'Enabled',
      );
      
      expect(label, equals('Button, Submit, Enabled, Double tap to submit'));
    });

    test('AccessibilityUtils driver-specific labels', () {
      final onlineLabel = AccessibilityUtils.getAvailabilityLabel(true);
      expect(onlineLabel, contains('Online'));
      expect(onlineLabel, contains('ready to accept rides'));
      
      final offlineLabel = AccessibilityUtils.getAvailabilityLabel(false);
      expect(offlineLabel, contains('Offline'));
      expect(offlineLabel, contains('not accepting rides'));
    });
  });
}
