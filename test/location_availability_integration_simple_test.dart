import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:lucian_drives_app/features/driver/presentation/providers/driver_availability_notifier.dart';
import 'package:lucian_drives_app/features/driver/presentation/providers/driver_profile_state.dart';
import 'package:lucian_drives_app/shared/models/models.dart';

void main() {
  group('Location Tracking Integration Verification', () {
    test('DriverAvailabilityNotifier integrates location tracking correctly', () {
      // This test verifies that the integration code exists and compiles correctly

      // Verify that DriverAvailabilityState has the correct structure
      const offlineState = DriverAvailabilityState.offline();
      expect(offlineState, isA<DriverAvailabilityOffline>());

      const goingOnlineState = DriverAvailabilityState.goingOnline();
      expect(goingOnlineState, isA<DriverAvailabilityGoingOnline>());

      final locationInfo = LocationInfo(
        latitude: 14.0101,
        longitude: -60.9875,
        updatedAt: DateTime.now(),
        heading: 0.0,
        speed: 0.0,
        accuracy: 5.0,
      );

      final onlineState = DriverAvailabilityState.online(
        location: locationInfo,
      );
      expect(onlineState, isA<DriverAvailabilityOnline>());

      const goingOfflineState = DriverAvailabilityState.goingOffline();
      expect(goingOfflineState, isA<DriverAvailabilityGoingOffline>());

      const errorState = DriverAvailabilityState.error(message: 'Test error');
      expect(errorState, isA<DriverAvailabilityError>());
    });

    test('LocationInfo model has required fields for integration', () {
      final locationInfo = LocationInfo(
        latitude: 14.0101,
        longitude: -60.9875,
        updatedAt: DateTime.now(),
        heading: 90.0,
        speed: 25.0,
        accuracy: 3.0,
      );

      expect(locationInfo.latitude, equals(14.0101));
      expect(locationInfo.longitude, equals(-60.9875));
      expect(locationInfo.heading, equals(90.0));
      expect(locationInfo.speed, equals(25.0));
      expect(locationInfo.accuracy, equals(3.0));
      expect(locationInfo.updatedAt, isA<DateTime>());
    });

    test('LocationUpdate model has required fields for API integration', () {
      final locationUpdate = LocationUpdate(
        latitude: 14.0101,
        longitude: -60.9875,
        heading: 90.0,
        speed: 25.0,
        accuracy: 3.0,
      );

      expect(locationUpdate.latitude, equals(14.0101));
      expect(locationUpdate.longitude, equals(-60.9875));
      expect(locationUpdate.heading, equals(90.0));
      expect(locationUpdate.speed, equals(25.0));
      expect(locationUpdate.accuracy, equals(3.0));
    });

    test('Provider integration structure is correct', () {
      // Verify that the provider exists and can be instantiated
      expect(driverAvailabilityProvider, isA<StateNotifierProvider>());
      expect(isDriverOnlineProvider, isA<Provider>());
      expect(isDriverOfflineProvider, isA<Provider>());
      expect(isAvailabilityChangingProvider, isA<Provider>());
      expect(currentDriverLocationProvider, isA<Provider>());
    });
  });
}
