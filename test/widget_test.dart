// Basic Flutter widget test for Lucian Drives Driver app.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:lucian_drives_app/main.dart';

void main() {
  testWidgets('App loads successfully', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: Lucian<PERSON>riverApp()));

    // Verify that the app title is displayed
    expect(find.text('Lucian Drives - Driver'), findsOneWidget);

    // Verify that the main app content is displayed
    expect(find.text('Lucian Drives'), findsOneWidget);
    expect(find.text('Driver App'), findsOneWidget);
    expect(find.text('Project setup complete!'), findsOneWidget);
  });
}
