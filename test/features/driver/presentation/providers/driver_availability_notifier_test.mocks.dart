// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in lucian_drives_app/test/features/driver/presentation/providers/driver_availability_notifier_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:geolocator/geolocator.dart' as _i3;
import 'package:lucian_drives_app/services/driver/driver_service.dart' as _i4;
import 'package:lucian_drives_app/services/location/location_service.dart'
    as _i6;
import 'package:lucian_drives_app/shared/models/models.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDriverProfile_0 extends _i1.SmartFake implements _i2.DriverProfile {
  _FakeDriverProfile_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVehicleInfo_1 extends _i1.SmartFake implements _i2.VehicleInfo {
  _FakeVehicleInfo_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEarningsInfo_2 extends _i1.SmartFake implements _i2.EarningsInfo {
  _FakeEarningsInfo_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDriverStats_3 extends _i1.SmartFake implements _i2.DriverStats {
  _FakeDriverStats_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVerificationStatus_4 extends _i1.SmartFake
    implements _i2.VerificationStatus {
  _FakeVerificationStatus_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePosition_5 extends _i1.SmartFake implements _i3.Position {
  _FakePosition_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [DriverService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDriverService extends _i1.Mock implements _i4.DriverService {
  MockDriverService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.DriverProfile> getDriverProfile() =>
      (super.noSuchMethod(
            Invocation.method(#getDriverProfile, []),
            returnValue: _i5.Future<_i2.DriverProfile>.value(
              _FakeDriverProfile_0(
                this,
                Invocation.method(#getDriverProfile, []),
              ),
            ),
          )
          as _i5.Future<_i2.DriverProfile>);

  @override
  _i5.Future<_i2.DriverProfile> createDriverProfile(
    _i2.DriverProfileCreate? profileData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createDriverProfile, [profileData]),
            returnValue: _i5.Future<_i2.DriverProfile>.value(
              _FakeDriverProfile_0(
                this,
                Invocation.method(#createDriverProfile, [profileData]),
              ),
            ),
          )
          as _i5.Future<_i2.DriverProfile>);

  @override
  _i5.Future<_i2.DriverProfile> updateDriverProfile(
    _i2.DriverProfileUpdate? profileData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateDriverProfile, [profileData]),
            returnValue: _i5.Future<_i2.DriverProfile>.value(
              _FakeDriverProfile_0(
                this,
                Invocation.method(#updateDriverProfile, [profileData]),
              ),
            ),
          )
          as _i5.Future<_i2.DriverProfile>);

  @override
  _i5.Future<_i2.VehicleInfo> getVehicleInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getVehicleInfo, []),
            returnValue: _i5.Future<_i2.VehicleInfo>.value(
              _FakeVehicleInfo_1(this, Invocation.method(#getVehicleInfo, [])),
            ),
          )
          as _i5.Future<_i2.VehicleInfo>);

  @override
  _i5.Future<_i2.VehicleInfo> addVehicleInfo(
    _i2.VehicleInfoCreate? vehicleData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#addVehicleInfo, [vehicleData]),
            returnValue: _i5.Future<_i2.VehicleInfo>.value(
              _FakeVehicleInfo_1(
                this,
                Invocation.method(#addVehicleInfo, [vehicleData]),
              ),
            ),
          )
          as _i5.Future<_i2.VehicleInfo>);

  @override
  _i5.Future<_i2.VehicleInfo> updateVehicleInfo(
    _i2.VehicleInfoUpdate? vehicleData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateVehicleInfo, [vehicleData]),
            returnValue: _i5.Future<_i2.VehicleInfo>.value(
              _FakeVehicleInfo_1(
                this,
                Invocation.method(#updateVehicleInfo, [vehicleData]),
              ),
            ),
          )
          as _i5.Future<_i2.VehicleInfo>);

  @override
  _i5.Future<void> updateAvailability(bool? isAvailable) =>
      (super.noSuchMethod(
            Invocation.method(#updateAvailability, [isAvailable]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> getAvailabilityStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailabilityStatus, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> updateLocation(_i2.LocationUpdate? locationData) =>
      (super.noSuchMethod(
            Invocation.method(#updateLocation, [locationData]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i2.LocationInfo?> getLastKnownLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getLastKnownLocation, []),
            returnValue: _i5.Future<_i2.LocationInfo?>.value(),
          )
          as _i5.Future<_i2.LocationInfo?>);

  @override
  _i5.Future<_i2.EarningsInfo> getEarnings() =>
      (super.noSuchMethod(
            Invocation.method(#getEarnings, []),
            returnValue: _i5.Future<_i2.EarningsInfo>.value(
              _FakeEarningsInfo_2(this, Invocation.method(#getEarnings, [])),
            ),
          )
          as _i5.Future<_i2.EarningsInfo>);

  @override
  _i5.Future<_i2.DriverStats> getDriverStats() =>
      (super.noSuchMethod(
            Invocation.method(#getDriverStats, []),
            returnValue: _i5.Future<_i2.DriverStats>.value(
              _FakeDriverStats_3(this, Invocation.method(#getDriverStats, [])),
            ),
          )
          as _i5.Future<_i2.DriverStats>);

  @override
  _i5.Future<_i2.VerificationStatus> getVerificationStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getVerificationStatus, []),
            returnValue: _i5.Future<_i2.VerificationStatus>.value(
              _FakeVerificationStatus_4(
                this,
                Invocation.method(#getVerificationStatus, []),
              ),
            ),
          )
          as _i5.Future<_i2.VerificationStatus>);

  @override
  _i5.Future<bool> hasCompleteProfile() =>
      (super.noSuchMethod(
            Invocation.method(#hasCompleteProfile, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);
}

/// A class which mocks [LocationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLocationService extends _i1.Mock implements _i6.LocationService {
  MockLocationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isTrackingLocation =>
      (super.noSuchMethod(
            Invocation.getter(#isTrackingLocation),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<bool> requestLocationPermission() =>
      (super.noSuchMethod(
            Invocation.method(#requestLocationPermission, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<_i3.LocationPermission> checkLocationPermission() =>
      (super.noSuchMethod(
            Invocation.method(#checkLocationPermission, []),
            returnValue: _i5.Future<_i3.LocationPermission>.value(
              _i3.LocationPermission.denied,
            ),
          )
          as _i5.Future<_i3.LocationPermission>);

  @override
  _i5.Future<bool> isLocationServiceEnabled() =>
      (super.noSuchMethod(
            Invocation.method(#isLocationServiceEnabled, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<_i3.Position> getCurrentPosition() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentPosition, []),
            returnValue: _i5.Future<_i3.Position>.value(
              _FakePosition_5(this, Invocation.method(#getCurrentPosition, [])),
            ),
          )
          as _i5.Future<_i3.Position>);

  @override
  _i5.Stream<_i3.Position> getLocationStream() =>
      (super.noSuchMethod(
            Invocation.method(#getLocationStream, []),
            returnValue: _i5.Stream<_i3.Position>.empty(),
          )
          as _i5.Stream<_i3.Position>);

  @override
  _i5.Future<void> startLocationTracking() =>
      (super.noSuchMethod(
            Invocation.method(#startLocationTracking, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> stopLocationTracking() =>
      (super.noSuchMethod(
            Invocation.method(#stopLocationTracking, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateDriverLocation(
    double? latitude,
    double? longitude, {
    double? heading,
    double? speed,
    double? accuracy,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #updateDriverLocation,
              [latitude, longitude],
              {#heading: heading, #speed: speed, #accuracy: accuracy},
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}
