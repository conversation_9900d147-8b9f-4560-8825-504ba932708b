import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:geolocator/geolocator.dart';

import 'package:lucian_drives_app/features/driver/presentation/providers/driver_availability_notifier.dart';
import 'package:lucian_drives_app/features/driver/presentation/providers/driver_profile_state.dart';
import 'package:lucian_drives_app/services/driver/driver_service.dart';
import 'package:lucian_drives_app/services/location/location_service.dart';

import 'driver_availability_notifier_test.mocks.dart';

@GenerateMocks([DriverService, LocationService])
void main() {
  group('DriverAvailabilityNotifier - Location Integration Tests', () {
    late MockDriverService mockDriverService;
    late MockLocationService mockLocationService;
    late ProviderContainer container;
    late DriverAvailabilityNotifier notifier;

    setUp(() {
      mockDriverService = MockDriverService();
      mockLocationService = MockLocationService();

      container = ProviderContainer(
        overrides: [
          driverAvailabilityProvider.overrideWith(
            (ref) => DriverAvailabilityNotifier(
              mockDriverService,
              mockLocationService,
              ref,
            ),
          ),
        ],
      );

      notifier = container.read(driverAvailabilityProvider.notifier);
    });

    tearDown(() {
      container.dispose();
    });

    group('goOnline', () {
      test(
        'should start location tracking when going online successfully',
        () async {
          // Arrange
          final mockPosition = Position(
            latitude: 14.0101,
            longitude: -60.9875,
            timestamp: DateTime.now(),
            accuracy: 5.0,
            altitude: 0.0,
            heading: 0.0,
            speed: 0.0,
            speedAccuracy: 0.0,
            altitudeAccuracy: 0.0,
            headingAccuracy: 0.0,
          );

          when(
            mockLocationService.requestLocationPermission(),
          ).thenAnswer((_) async => true);
          when(
            mockLocationService.isLocationServiceEnabled(),
          ).thenAnswer((_) async => true);
          when(
            mockLocationService.startLocationTracking(),
          ).thenAnswer((_) async => {});
          when(
            mockLocationService.getCurrentPosition(),
          ).thenAnswer((_) async => mockPosition);
          when(
            mockDriverService.updateAvailability(true),
          ).thenAnswer((_) async => {});

          // Act
          await notifier.goOnline();

          // Assert
          final state = container.read(driverAvailabilityProvider);
          expect(state, isA<DriverAvailabilityOnline>());

          verify(mockLocationService.requestLocationPermission()).called(1);
          verify(mockLocationService.isLocationServiceEnabled()).called(1);
          verify(mockLocationService.startLocationTracking()).called(1);
          verify(mockLocationService.getCurrentPosition()).called(1);
          verify(mockDriverService.updateAvailability(true)).called(1);
        },
      );

      test('should handle location permission denied error', () async {
        // Arrange
        when(
          mockLocationService.requestLocationPermission(),
        ).thenAnswer((_) async => false);

        // Act
        await notifier.goOnline();

        // Assert
        final state = container.read(driverAvailabilityProvider);
        expect(state, isA<DriverAvailabilityError>());

        final errorState = state as DriverAvailabilityError;
        expect(errorState.message, contains('Location permission is required'));

        verify(mockLocationService.requestLocationPermission()).called(1);
        verifyNever(mockLocationService.startLocationTracking());
        verifyNever(mockDriverService.updateAvailability(any));
      });

      test('should handle location service disabled error', () async {
        // Arrange
        when(
          mockLocationService.requestLocationPermission(),
        ).thenAnswer((_) async => true);
        when(
          mockLocationService.isLocationServiceEnabled(),
        ).thenAnswer((_) async => false);

        // Act
        await notifier.goOnline();

        // Assert
        final state = container.read(driverAvailabilityProvider);
        expect(state, isA<DriverAvailabilityError>());

        final errorState = state as DriverAvailabilityError;
        expect(errorState.message, contains('Location services are disabled'));

        verify(mockLocationService.requestLocationPermission()).called(1);
        verify(mockLocationService.isLocationServiceEnabled()).called(1);
        verifyNever(mockLocationService.startLocationTracking());
        verifyNever(mockDriverService.updateAvailability(any));
      });
    });

    group('goOffline', () {
      test('should stop location tracking when going offline', () async {
        // Arrange
        when(
          mockLocationService.stopLocationTracking(),
        ).thenAnswer((_) async => {});
        when(
          mockDriverService.updateAvailability(false),
        ).thenAnswer((_) async => {});

        // Act
        await notifier.goOffline();

        // Assert
        final state = container.read(driverAvailabilityProvider);
        expect(state, isA<DriverAvailabilityOffline>());

        verify(mockLocationService.stopLocationTracking()).called(1);
        verify(mockDriverService.updateAvailability(false)).called(1);
      });

      test('should handle errors when going offline', () async {
        // Arrange
        when(
          mockLocationService.stopLocationTracking(),
        ).thenThrow(Exception('Failed to stop location tracking'));

        // Act
        await notifier.goOffline();

        // Assert
        final state = container.read(driverAvailabilityProvider);
        expect(state, isA<DriverAvailabilityError>());

        verify(mockLocationService.stopLocationTracking()).called(1);
      });
    });

    group('initializeAvailability', () {
      test(
        'should start location tracking if driver was previously online',
        () async {
          // Arrange
          final mockPosition = Position(
            latitude: 14.0101,
            longitude: -60.9875,
            timestamp: DateTime.now(),
            accuracy: 5.0,
            altitude: 0.0,
            heading: 0.0,
            speed: 0.0,
            speedAccuracy: 0.0,
            altitudeAccuracy: 0.0,
            headingAccuracy: 0.0,
          );

          when(
            mockDriverService.getAvailabilityStatus(),
          ).thenAnswer((_) async => true);
          when(
            mockLocationService.requestLocationPermission(),
          ).thenAnswer((_) async => true);
          when(
            mockLocationService.startLocationTracking(),
          ).thenAnswer((_) async => {});
          when(
            mockLocationService.getCurrentPosition(),
          ).thenAnswer((_) async => mockPosition);

          // Act
          await notifier.initializeAvailability();

          // Assert
          final state = container.read(driverAvailabilityProvider);
          expect(state, isA<DriverAvailabilityOnline>());

          verify(mockDriverService.getAvailabilityStatus()).called(1);
          verify(mockLocationService.requestLocationPermission()).called(1);
          verify(mockLocationService.startLocationTracking()).called(1);
        },
      );

      test('should remain offline if driver was previously offline', () async {
        // Arrange
        when(
          mockDriverService.getAvailabilityStatus(),
        ).thenAnswer((_) async => false);

        // Act
        await notifier.initializeAvailability();

        // Assert
        final state = container.read(driverAvailabilityProvider);
        expect(state, isA<DriverAvailabilityOffline>());

        verify(mockDriverService.getAvailabilityStatus()).called(1);
        verifyNever(mockLocationService.startLocationTracking());
      });
    });

    group('toggleAvailability', () {
      test('should go online when currently offline', () async {
        // Arrange
        final mockPosition = Position(
          latitude: 14.0101,
          longitude: -60.9875,
          timestamp: DateTime.now(),
          accuracy: 5.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0,
        );

        when(
          mockLocationService.requestLocationPermission(),
        ).thenAnswer((_) async => true);
        when(
          mockLocationService.isLocationServiceEnabled(),
        ).thenAnswer((_) async => true);
        when(
          mockLocationService.startLocationTracking(),
        ).thenAnswer((_) async => {});
        when(
          mockLocationService.getCurrentPosition(),
        ).thenAnswer((_) async => mockPosition);
        when(
          mockDriverService.updateAvailability(true),
        ).thenAnswer((_) async => {});

        // Act
        await notifier.toggleAvailability();

        // Assert
        final state = container.read(driverAvailabilityProvider);
        expect(state, isA<DriverAvailabilityOnline>());

        verify(mockLocationService.startLocationTracking()).called(1);
        verify(mockDriverService.updateAvailability(true)).called(1);
      });
    });

    group('handleLocationPermissionError', () {
      test(
        'should provide appropriate error message for denied permission',
        () async {
          // Arrange
          when(
            mockLocationService.checkLocationPermission(),
          ).thenAnswer((_) async => LocationPermission.denied);

          // Act
          await notifier.handleLocationPermissionError();

          // Assert
          final state = container.read(driverAvailabilityProvider);
          expect(state, isA<DriverAvailabilityError>());

          final errorState = state as DriverAvailabilityError;
          expect(
            errorState.message,
            contains('grant location access when prompted'),
          );
        },
      );

      test(
        'should provide appropriate error message for permanently denied permission',
        () async {
          // Arrange
          when(
            mockLocationService.checkLocationPermission(),
          ).thenAnswer((_) async => LocationPermission.deniedForever);

          // Act
          await notifier.handleLocationPermissionError();

          // Assert
          final state = container.read(driverAvailabilityProvider);
          expect(state, isA<DriverAvailabilityError>());

          final errorState = state as DriverAvailabilityError;
          expect(errorState.message, contains('permanently denied'));
        },
      );
    });
  });
}
