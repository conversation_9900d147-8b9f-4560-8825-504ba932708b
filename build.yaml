targets:
  $default:
    builders:
      json_serializable:
        options:
          # Options for json_serializable
          any_map: false
          checked: false
          create_factory: true
          create_to_json: true
          disallow_unrecognized_keys: false
          explicit_to_json: false
          field_rename: none
          generic_argument_factories: false
          ignore_unannotated: false
          include_if_null: true
      freezed:
        options:
          # Options for freezed
          map: true
          make_copy_with: true
          equal: true
          to_string: true
      auto_route_generator:
        options:
          # Options for auto_route
          generate_for_dir: ['lib']
      riverpod_generator:
        options:
          # Options for riverpod_generator
          generate_riverpod_annotation: true