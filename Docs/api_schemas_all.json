{"openapi": "3.1.0", "info": {"title": "<PERSON>s", "description": "Backend API for St. Lucia Ride-Share MVP - Tourist-focused ride-sharing platform with fixed pricing", "version": "1.0.0"}, "components": {"schemas": {"AdminProfile": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "permissions": {"items": {"type": "string"}, "type": "array", "title": "Permissions", "description": "Admin permissions"}, "last_activity": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Activity", "description": "Last activity timestamp"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "Profile creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["user_id"], "title": "AdminProfile", "description": "Admin-specific profile model."}, "Body_upload_profile_image_api_v1_auth_profile_image_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_profile_image_api_v1_auth_profile_image_post"}, "Body_upload_verification_document_api_v1_drivers_documents_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}, "document_type": {"type": "string", "title": "Document Type", "description": "Document type (license, insurance, etc.)"}}, "type": "object", "required": ["file", "document_type"], "title": "Body_upload_verification_document_api_v1_drivers_documents_upload_post"}, "CancellationReason": {"type": "string", "enum": ["rider_cancelled", "driver_cancelled", "no_driver_available", "payment_failed", "weather_conditions", "vehicle_breakdown", "emergency", "other"], "title": "CancellationReason", "description": "Cancellation reason enumeration."}, "DriverAvailableRidesResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Response message", "default": "Available rides retrieved successfully"}, "data": {"items": {"$ref": "#/components/schemas/Ride"}, "type": "array", "title": "Data", "description": "Available rides"}, "total": {"type": "integer", "title": "Total", "description": "Total number of available rides", "default": 0}, "nearby_count": {"type": "integer", "title": "Nearby Count", "description": "Number of nearby rides", "default": 0}, "distance_filtered": {"type": "integer", "title": "Distance Filtered", "description": "Number of rides filtered by distance", "default": 0}}, "type": "object", "title": "DriverAvailableRidesResponse", "description": "Response schema for available rides for drivers."}, "DriverOnboardingRefreshRequest": {"properties": {"refresh_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh Url", "description": "Onboarding refresh URL"}, "return_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Return Url", "description": "Onboarding return URL"}}, "type": "object", "title": "DriverOnboardingRefreshRequest", "description": "Request schema for refreshing driver onboarding link."}, "DriverOnboardingRefreshResponse": {"properties": {"onboarding_link": {"type": "string", "title": "Onboarding Link", "description": "New onboarding link URL"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "Onboarding link expiration"}}, "type": "object", "required": ["onboarding_link", "expires_at"], "title": "DriverOnboardingRefreshResponse", "description": "Response schema for driver onboarding refresh."}, "DriverOnboardingStartRequest": {"properties": {"country": {"type": "string", "title": "Country", "description": "Driver's country", "default": "US"}, "type": {"type": "string", "title": "Type", "description": "Stripe account type", "default": "express"}, "refresh_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh Url", "description": "Onboarding refresh URL"}, "return_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Return Url", "description": "Onboarding return URL"}}, "type": "object", "title": "DriverOnboardingStartRequest", "description": "Request schema for starting driver onboarding."}, "DriverOnboardingStartResponse": {"properties": {"account_id": {"type": "string", "title": "Account Id", "description": "Stripe Connect account ID"}, "onboarding_link": {"type": "string", "title": "Onboarding Link", "description": "Onboarding link URL"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "Onboarding link expiration"}}, "type": "object", "required": ["account_id", "onboarding_link", "expires_at"], "title": "DriverOnboardingStartResponse", "description": "Response schema for driver onboarding start."}, "DriverOnboardingStatus": {"type": "string", "enum": ["not_started", "pending", "verified", "rejected", "restricted"], "title": "DriverOnboardingStatus", "description": "Driver onboarding status enumeration."}, "DriverOnboardingStatusResponse": {"properties": {"account_id": {"type": "string", "title": "Account Id", "description": "Stripe Connect account ID"}, "status": {"$ref": "#/components/schemas/DriverOnboardingStatus", "description": "Onboarding status"}, "charges_enabled": {"type": "boolean", "title": "Charges Enabled", "description": "Whether charges are enabled"}, "payouts_enabled": {"type": "boolean", "title": "Payouts Enabled", "description": "Whether payouts are enabled"}, "currently_due": {"items": {"type": "string"}, "type": "array", "title": "Currently Due", "description": "Currently due requirements"}, "eventually_due": {"items": {"type": "string"}, "type": "array", "title": "Eventually Due", "description": "Eventually due requirements"}, "past_due": {"items": {"type": "string"}, "type": "array", "title": "Past Due", "description": "Past due requirements"}, "onboarding_link": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Onboarding Link", "description": "Onboarding link if needed"}, "onboarding_link_expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Onboarding Link Expires At", "description": "Onboarding link expiration"}}, "type": "object", "required": ["account_id", "status", "charges_enabled", "payouts_enabled", "currently_due", "eventually_due", "past_due"], "title": "DriverOnboardingStatusResponse", "description": "Response schema for driver onboarding status."}, "DriverProfile": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "license_number": {"type": "string", "maxLength": 50, "minLength": 1, "title": "License Number", "description": "Driver's license number"}, "vehicle_info": {"$ref": "#/components/schemas/VehicleInfo", "description": "Vehicle information"}, "is_verified": {"type": "boolean", "title": "Is Verified", "description": "Driver verification status", "default": false}, "is_available": {"type": "boolean", "title": "Is Available", "description": "Driver availability status", "default": false}, "rating": {"type": "number", "maximum": 5, "minimum": 0, "title": "Rating", "description": "Average rating", "default": 0}, "total_rides": {"type": "integer", "minimum": 0, "title": "Total Rides", "description": "Total number of rides", "default": 0}, "current_location": {"anyOf": [{"$ref": "#/components/schemas/LocationInfo"}, {"type": "null"}], "description": "Current location"}, "stripe_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stripe Account Id", "description": "Stripe Connect account ID"}, "earnings": {"additionalProperties": {"type": "number"}, "type": "object", "title": "Earnings", "description": "Driver earnings"}, "verification_documents": {"items": {"type": "string"}, "type": "array", "title": "Verification Documents", "description": "Verification document URLs"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "Profile creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["user_id", "license_number", "vehicle_info"], "title": "DriverProfile", "description": "Driver-specific profile model."}, "DriverProfileCreate": {"properties": {"license_number": {"type": "string", "maxLength": 50, "minLength": 1, "title": "License Number", "description": "Driver's license number"}, "vehicle_info": {"$ref": "#/components/schemas/VehicleInfo", "description": "Vehicle information"}}, "type": "object", "required": ["license_number", "vehicle_info"], "title": "DriverProfileCreate", "description": "<PERSON><PERSON><PERSON> for creating driver profile."}, "DriverProfileResponse": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "license_number": {"type": "string", "title": "License Number", "description": "Driver's license number"}, "vehicle_info": {"anyOf": [{"$ref": "#/components/schemas/VehicleInfo"}, {"type": "null"}], "description": "Vehicle information"}, "is_verified": {"type": "boolean", "title": "Is Verified", "description": "Driver verification status"}, "is_available": {"type": "boolean", "title": "Is Available", "description": "Driver availability status"}, "rating": {"type": "number", "maximum": 5, "minimum": 0, "title": "Rating", "description": "Average rating"}, "total_rides": {"type": "integer", "minimum": 0, "title": "Total Rides", "description": "Total number of rides"}, "current_location": {"anyOf": [{"$ref": "#/components/schemas/LocationInfo"}, {"type": "null"}], "description": "Current location"}, "stripe_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stripe Account Id", "description": "Stripe Connect account ID"}, "earnings": {"additionalProperties": {"type": "number"}, "type": "object", "title": "Earnings", "description": "Driver earnings"}, "verification_documents": {"items": {"type": "string"}, "type": "array", "title": "Verification Documents", "description": "Verification document URLs"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "Profile creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["user_id", "license_number", "is_verified", "is_available", "rating", "total_rides", "earnings", "verification_documents"], "title": "DriverProfileResponse", "description": "Schema for driver profile response."}, "DriverProfileUpdate": {"properties": {"license_number": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "License Number", "description": "Driver's license number"}, "vehicle_info": {"anyOf": [{"$ref": "#/components/schemas/VehicleInfo"}, {"type": "null"}], "description": "Vehicle information"}, "is_available": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Available", "description": "Driver availability status"}, "current_location": {"anyOf": [{"$ref": "#/components/schemas/LocationInfo"}, {"type": "null"}], "description": "Current location"}}, "type": "object", "title": "DriverProfileUpdate", "description": "<PERSON><PERSON><PERSON> for updating driver profile."}, "EarningsBalanceResponse": {"properties": {"available_balance": {"type": "integer", "title": "Available Balance", "description": "Available balance in cents"}, "pending_balance": {"type": "integer", "title": "Pending Balance", "description": "Pending balance in cents"}, "last_payout_amount": {"type": "integer", "title": "Last Payout Amount", "description": "Last payout amount in cents"}, "last_payout_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Payout Date", "description": "Last payout date"}, "next_payout_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Payout Date", "description": "Next scheduled payout date"}}, "type": "object", "required": ["available_balance", "pending_balance", "last_payout_amount"], "title": "EarningsBalanceResponse", "description": "Response schema for earnings balance."}, "EarningsResponse": {"properties": {"total_earnings": {"type": "integer", "title": "Total Earnings", "description": "Total earnings in cents"}, "available_earnings": {"type": "integer", "title": "Available Earnings", "description": "Available earnings for payout in cents"}, "pending_earnings": {"type": "integer", "title": "Pending Earnings", "description": "Pending earnings in cents"}, "current_week_earnings": {"type": "integer", "title": "Current Week Earnings", "description": "Current week earnings in cents"}, "current_month_earnings": {"type": "integer", "title": "Current Month Earnings", "description": "Current month earnings in cents"}, "current_year_earnings": {"type": "integer", "title": "Current Year Earnings", "description": "Current year earnings in cents"}, "total_rides": {"type": "integer", "title": "Total Rides", "description": "Total number of rides"}, "completed_rides": {"type": "integer", "title": "Completed Rides", "description": "Number of completed rides"}, "next_payout_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Payout Date", "description": "Next scheduled payout date"}}, "type": "object", "required": ["total_earnings", "available_earnings", "pending_earnings", "current_week_earnings", "current_month_earnings", "current_year_earnings", "total_rides", "completed_rides"], "title": "EarningsResponse", "description": "Response schema for driver earnings."}, "EmergencyContact": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Emergency contact name"}, "phone": {"type": "string", "maxLength": 20, "minLength": 10, "title": "Phone", "description": "Emergency contact phone number"}, "relationship": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Relationship", "description": "Relationship to user"}}, "type": "object", "required": ["name", "phone"], "title": "EmergencyContact", "description": "Emergency contact information model."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "LocationAccuracy": {"type": "string", "enum": ["high", "medium", "low"], "title": "LocationAccuracy", "description": "Location accuracy levels."}, "LocationCoordinates": {"properties": {"latitude": {"type": "number", "maximum": 90, "minimum": -90, "title": "Latitude", "description": "Latitude coordinate"}, "longitude": {"type": "number", "maximum": 180, "minimum": -180, "title": "Longitude", "description": "Longitude coordinate"}}, "type": "object", "required": ["latitude", "longitude"], "title": "LocationCoordinates", "description": "Geographic coordinates model."}, "LocationCoordinatesRequest": {"properties": {"latitude": {"type": "number", "maximum": 90, "minimum": -90, "title": "Latitude", "description": "Latitude coordinate"}, "longitude": {"type": "number", "maximum": 180, "minimum": -180, "title": "Longitude", "description": "Longitude coordinate"}}, "type": "object", "required": ["latitude", "longitude"], "title": "LocationCoordinatesRequest", "description": "Location coordinates request schema."}, "LocationETARequest": {"properties": {"destination": {"$ref": "#/components/schemas/LocationCoordinatesRequest", "description": "Destination coordinates"}, "current_traffic": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Current Traffic", "description": "Whether to include current traffic conditions", "default": true}}, "type": "object", "required": ["destination"], "title": "LocationETARequest", "description": "Location ETA calculation request schema."}, "LocationETAResponse": {"properties": {"estimated_time_minutes": {"type": "integer", "title": "Estimated Time Minutes", "description": "Estimated time in minutes"}, "estimated_distance_meters": {"type": "number", "title": "Estimated Distance Meters", "description": "Estimated distance in meters"}, "current_location": {"$ref": "#/components/schemas/LocationCoordinates", "description": "Current driver location"}, "destination": {"$ref": "#/components/schemas/LocationCoordinates", "description": "Destination coordinates"}, "last_updated": {"type": "string", "format": "date-time", "title": "Last Updated", "description": "When ETA was calculated"}, "traffic_considered": {"type": "boolean", "title": "Traffic Considered", "description": "Whether traffic was considered"}}, "type": "object", "required": ["estimated_time_minutes", "estimated_distance_meters", "current_location", "destination", "last_updated", "traffic_considered"], "title": "LocationETAResponse", "description": "Location ETA calculation response schema."}, "LocationHistoryListResponse": {"properties": {"locations": {"items": {"$ref": "#/components/schemas/LocationHistoryResponse"}, "type": "array", "title": "Locations", "description": "List of location history records"}, "total": {"type": "integer", "title": "Total", "description": "Total number of records"}, "limit": {"type": "integer", "title": "Limit", "description": "Applied limit"}, "offset": {"type": "integer", "title": "Offset", "description": "Applied offset"}, "has_more": {"type": "boolean", "title": "Has <PERSON>", "description": "Whether there are more records available"}}, "type": "object", "required": ["locations", "total", "limit", "offset", "has_more"], "title": "LocationHistoryListResponse", "description": "Location history list response schema."}, "LocationHistoryResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "History ID"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}, "driver_id": {"type": "string", "title": "Driver Id", "description": "Driver user ID"}, "coordinates": {"$ref": "#/components/schemas/LocationCoordinates", "description": "Historical coordinates"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "Historical timestamp"}, "distance_from_previous": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Distance From Previous", "description": "Distance from previous location in meters"}, "time_from_previous": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Time From Previous", "description": "Time from previous location in seconds"}, "average_speed": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Speed", "description": "Average speed between locations in km/h"}, "location_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location Type", "description": "Location type (pickup, dropoff, waypoint)"}, "accuracy": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Accuracy", "description": "Location accuracy in meters"}}, "type": "object", "required": ["id", "ride_id", "driver_id", "coordinates", "timestamp"], "title": "LocationHistoryResponse", "description": "Location history response schema."}, "LocationInfo": {"properties": {"latitude": {"type": "number", "maximum": 90, "minimum": -90, "title": "Latitude", "description": "Latitude coordinate"}, "longitude": {"type": "number", "maximum": 180, "minimum": -180, "title": "Longitude", "description": "Longitude coordinate"}, "address": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Address", "description": "Human-readable address"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["latitude", "longitude"], "title": "LocationInfo", "description": "Location information model."}, "LocationPingRequest": {"properties": {"test_coordinates": {"$ref": "#/components/schemas/LocationCoordinatesRequest", "description": "Test coordinates"}}, "type": "object", "required": ["test_coordinates"], "title": "LocationPingRequest", "description": "Location ping request schema for testing connectivity."}, "LocationPingResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Whether ping was successful"}, "response_time_ms": {"type": "integer", "title": "Response Time Ms", "description": "Response time in milliseconds"}, "server_time": {"type": "string", "format": "date-time", "title": "Server Time", "description": "Server timestamp"}, "client_coordinates": {"$ref": "#/components/schemas/LocationCoordinates", "description": "Received coordinates"}, "is_within_service_area": {"type": "boolean", "title": "Is Within Service Area", "description": "Whether coordinates are within service area"}}, "type": "object", "required": ["success", "response_time_ms", "server_time", "client_coordinates", "is_within_service_area"], "title": "LocationPingResponse", "description": "Location ping response schema."}, "LocationStatsResponse": {"properties": {"total_updates": {"type": "integer", "title": "Total Updates", "description": "Total location updates"}, "average_accuracy": {"type": "number", "title": "Average Accuracy", "description": "Average accuracy in meters"}, "distance_traveled": {"type": "number", "title": "Distance Traveled", "description": "Total distance traveled in meters"}, "tracking_duration": {"type": "integer", "title": "Tracking Duration", "description": "Total tracking duration in seconds"}, "last_update": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Update", "description": "Last location update timestamp"}, "update_frequency": {"type": "number", "title": "Update Frequency", "description": "Average updates per minute"}, "coverage_percentage": {"type": "number", "title": "Coverage Percentage", "description": "Percentage of expected tracking time covered"}}, "type": "object", "required": ["total_updates", "average_accuracy", "distance_traveled", "tracking_duration", "update_frequency", "coverage_percentage"], "title": "LocationStatsResponse", "description": "Location statistics response schema."}, "LocationTrackingControlRequest": {"properties": {"is_active": {"type": "boolean", "title": "Is Active", "description": "Whether to activate or deactivate tracking"}, "update_interval": {"anyOf": [{"type": "integer", "maximum": 60, "minimum": 1}, {"type": "null"}], "title": "Update Interval", "description": "Update interval in seconds"}}, "type": "object", "required": ["is_active"], "title": "LocationTrackingControlRequest", "description": "Location tracking control request schema."}, "LocationTrackingControlResponse": {"properties": {"ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}, "driver_id": {"type": "string", "title": "Driver Id", "description": "Driver user ID"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Current tracking status"}, "update_interval": {"type": "integer", "title": "Update Interval", "description": "Current update interval in seconds"}, "last_update": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Update", "description": "Last location update timestamp"}, "tracking_started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Tracking Started At", "description": "Tracking start timestamp"}}, "type": "object", "required": ["ride_id", "driver_id", "is_active", "update_interval"], "title": "LocationTrackingControlResponse", "description": "Location tracking control response schema."}, "LocationUpdateRequest": {"properties": {"coordinates": {"$ref": "#/components/schemas/LocationCoordinatesRequest", "description": "Current coordinates"}, "heading": {"anyOf": [{"type": "number", "maximum": 360, "minimum": 0}, {"type": "null"}], "title": "Heading", "description": "Direction heading in degrees"}, "speed": {"anyOf": [{"type": "number", "maximum": 200, "minimum": 0}, {"type": "null"}], "title": "Speed", "description": "Speed in km/h"}, "accuracy": {"anyOf": [{"type": "number", "maximum": 10000, "minimum": 0}, {"type": "null"}], "title": "Accuracy", "description": "Location accuracy in meters"}, "altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Altitude", "description": "Altitude in meters"}, "battery_level": {"anyOf": [{"type": "integer", "maximum": 100, "minimum": 0}, {"type": "null"}], "title": "Battery Level", "description": "Device battery level"}}, "type": "object", "required": ["coordinates"], "title": "LocationUpdateRequest", "description": "Location update request schema."}, "LocationUpdateResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Update ID"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}, "driver_id": {"type": "string", "title": "Driver Id", "description": "Driver user ID"}, "coordinates": {"$ref": "#/components/schemas/LocationCoordinates", "description": "Current coordinates"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "Update timestamp"}, "heading": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Heading", "description": "Direction heading in degrees"}, "speed": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Speed", "description": "Speed in km/h"}, "accuracy": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Accuracy", "description": "Location accuracy in meters"}, "altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Altitude", "description": "Altitude in meters"}, "accuracy_level": {"$ref": "#/components/schemas/LocationAccuracy", "description": "Accuracy level"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether location tracking is active"}, "battery_level": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Battery Level", "description": "Device battery level"}}, "type": "object", "required": ["id", "ride_id", "driver_id", "coordinates", "timestamp", "accuracy_level", "is_active"], "title": "LocationUpdateResponse", "description": "Location update response schema."}, "LocationValidationResponse": {"properties": {"is_valid": {"type": "boolean", "title": "Is <PERSON>", "description": "Whether location is valid"}, "is_within_service_area": {"type": "boolean", "title": "Is Within Service Area", "description": "Whether location is within service area"}, "accuracy_level": {"$ref": "#/components/schemas/LocationAccuracy", "description": "Accuracy level"}, "accuracy_description": {"type": "string", "title": "Accuracy Description", "description": "Human-readable accuracy description"}, "distance_from_previous": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Distance From Previous", "description": "Distance from previous location in meters"}, "warnings": {"items": {"type": "string"}, "type": "array", "title": "Warnings", "description": "Validation warnings"}}, "type": "object", "required": ["is_valid", "is_within_service_area", "accuracy_level", "accuracy_description"], "title": "LocationValidationResponse", "description": "Location validation response schema."}, "MessageAttachment": {"properties": {"id": {"type": "string", "title": "Id", "description": "Attachment ID"}, "file_name": {"type": "string", "title": "File Name", "description": "Original file name"}, "file_type": {"type": "string", "title": "File Type", "description": "File MIME type"}, "file_size": {"type": "integer", "minimum": 0, "title": "File Size", "description": "File size in bytes"}, "file_url": {"type": "string", "title": "File Url", "description": "File storage URL"}, "thumbnail_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Thumbnail URL for images/videos"}, "duration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration", "description": "Duration in seconds for audio/video"}}, "type": "object", "required": ["file_name", "file_type", "file_size", "file_url"], "title": "MessageAttachment", "description": "Message attachment model."}, "MessageHistoryResponse": {"properties": {"messages": {"items": {"$ref": "#/components/schemas/app__schemas__message__MessageResponse"}, "type": "array", "title": "Messages", "description": "List of messages"}, "total": {"type": "integer", "title": "Total", "description": "Total number of messages"}, "limit": {"type": "integer", "title": "Limit", "description": "Applied limit"}, "offset": {"type": "integer", "title": "Offset", "description": "Applied offset"}, "has_more": {"type": "boolean", "title": "Has <PERSON>", "description": "Whether there are more messages available"}}, "type": "object", "required": ["messages", "total", "limit", "offset", "has_more"], "title": "MessageHistoryResponse", "description": "Message history response schema."}, "MessageMetadata": {"properties": {"device_info": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Device Info", "description": "Device information"}, "app_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "App Version", "description": "App version"}, "location": {"anyOf": [{"additionalProperties": {"type": "number"}, "type": "object"}, {"type": "null"}], "title": "Location", "description": "Location when message was sent"}, "reply_to": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reply To", "description": "ID of message being replied to"}, "forwarded_from": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Forwarded From", "description": "ID of original message if forwarded"}, "edited_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Edited At", "description": "When message was last edited"}, "translation": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "Translation", "description": "Message translations"}}, "type": "object", "title": "MessageMetadata", "description": "Message metadata model."}, "MessagePriority": {"type": "string", "enum": ["low", "normal", "high", "urgent"], "title": "MessagePriority", "description": "Message priority enumeration."}, "MessageSendRequest": {"properties": {"content": {"type": "string", "maxLength": 1000, "title": "Content", "description": "Message content"}, "message_type": {"$ref": "#/components/schemas/MessageType", "description": "Message type", "default": "text"}, "priority": {"$ref": "#/components/schemas/MessagePriority", "description": "Message priority", "default": "normal"}, "reply_to": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reply To", "description": "ID of message being replied to"}, "template_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Template Id", "description": "Template ID if using template"}, "template_variables": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "Template Variables", "description": "Template variable values"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "Message expiration timestamp"}}, "type": "object", "required": ["content"], "title": "MessageSendRequest", "description": "Message send request schema."}, "MessageSendResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Message ID"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}, "sender_id": {"type": "string", "title": "Sender Id", "description": "Sender user ID"}, "sender_type": {"$ref": "#/components/schemas/SenderType", "description": "Sender type"}, "recipient_id": {"type": "string", "title": "Recipient Id", "description": "Recipient user ID"}, "recipient_type": {"$ref": "#/components/schemas/SenderType", "description": "Recipient type"}, "content": {"type": "string", "title": "Content", "description": "Message content"}, "message_type": {"$ref": "#/components/schemas/MessageType", "description": "Message type"}, "status": {"$ref": "#/components/schemas/MessageStatus", "description": "Message status"}, "priority": {"$ref": "#/components/schemas/MessagePriority", "description": "Message priority"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Message creation timestamp"}, "is_system_message": {"type": "boolean", "title": "Is System Message", "description": "Whether message is system-generated"}, "template_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Template Id", "description": "Template ID if message uses template"}}, "type": "object", "required": ["id", "ride_id", "sender_id", "sender_type", "recipient_id", "recipient_type", "content", "message_type", "status", "priority", "created_at", "is_system_message"], "title": "MessageSendResponse", "description": "Message send response schema."}, "MessageStatsResponse": {"properties": {"ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}, "total_messages": {"type": "integer", "title": "Total Messages", "description": "Total messages sent"}, "messages_by_type": {"additionalProperties": {"type": "integer"}, "propertyNames": {"$ref": "#/components/schemas/MessageType"}, "type": "object", "title": "Messages By Type", "description": "Messages by type"}, "messages_by_sender": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Messages By Sender", "description": "Messages by sender"}, "average_response_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Response Time", "description": "Average response time in seconds"}, "first_message_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "First Message At", "description": "First message timestamp"}, "last_message_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Message At", "description": "Last message timestamp"}, "delivery_rate": {"type": "number", "title": "Delivery Rate", "description": "Message delivery rate percentage"}, "read_rate": {"type": "number", "title": "Read Rate", "description": "Message read rate percentage"}, "template_usage": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Template Usage", "description": "Template usage counts"}}, "type": "object", "required": ["ride_id", "total_messages", "delivery_rate", "read_rate"], "title": "MessageStatsResponse", "description": "Message statistics response schema."}, "MessageStatus": {"type": "string", "enum": ["sent", "delivered", "read", "failed"], "title": "MessageStatus", "description": "Message status enumeration."}, "MessageStatusUpdateResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Message ID"}, "status": {"$ref": "#/components/schemas/MessageStatus", "description": "Updated message status"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Status update timestamp"}, "delivered_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Delivered At", "description": "Message delivery timestamp"}, "read_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Read At", "description": "Message read timestamp"}}, "type": "object", "required": ["id", "status", "updated_at"], "title": "MessageStatusUpdateResponse", "description": "Message status update response schema."}, "MessageThreadResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Thread ID"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}, "participants": {"items": {"type": "string"}, "type": "array", "title": "Participants", "description": "Participant user IDs"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Thread creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Thread last update timestamp"}, "last_message_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Message Id", "description": "Last message ID in thread"}, "last_message_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Message At", "description": "Last message timestamp"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether thread is active"}, "is_archived": {"type": "boolean", "title": "Is Archived", "description": "Whether thread is archived"}, "total_messages": {"type": "integer", "title": "Total Messages", "description": "Total messages in thread"}, "unread_counts": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Unread Counts", "description": "Unread message counts per participant"}}, "type": "object", "required": ["id", "ride_id", "participants", "created_at", "updated_at", "is_active", "is_archived", "total_messages"], "title": "MessageThreadResponse", "description": "Message thread response schema."}, "MessageType": {"type": "string", "enum": ["text", "system", "location", "image", "voice", "template"], "title": "MessageType", "description": "Message type enumeration."}, "MessageTypingIndicatorRequest": {"properties": {"is_typing": {"type": "boolean", "title": "Is <PERSON>ping", "description": "Whether user is typing"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}}, "type": "object", "required": ["is_typing", "ride_id"], "title": "MessageTypingIndicatorRequest", "description": "Message typing indicator request schema."}, "MessageTypingIndicatorResponse": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "is_typing": {"type": "boolean", "title": "Is <PERSON>ping", "description": "Whether user is typing"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "Indicator timestamp"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "Indicator expiration timestamp"}}, "type": "object", "required": ["user_id", "is_typing", "ride_id", "timestamp", "expires_at"], "title": "MessageTypingIndicatorResponse", "description": "Message typing indicator response schema."}, "MessageValidationRequest": {"properties": {"content": {"type": "string", "title": "Content", "description": "Message content to validate"}, "message_type": {"$ref": "#/components/schemas/MessageType", "description": "Message type", "default": "text"}, "sender_type": {"$ref": "#/components/schemas/SenderType", "description": "Sender type"}}, "type": "object", "required": ["content", "sender_type"], "title": "MessageValidationRequest", "description": "Message validation request schema."}, "MessageValidationResponse": {"properties": {"is_valid": {"type": "boolean", "title": "Is <PERSON>", "description": "Whether message is valid"}, "errors": {"items": {"type": "string"}, "type": "array", "title": "Errors", "description": "Validation errors"}, "warnings": {"items": {"type": "string"}, "type": "array", "title": "Warnings", "description": "Validation warnings"}, "suggested_priority": {"$ref": "#/components/schemas/MessagePriority", "description": "Suggested message priority"}, "content_length": {"type": "integer", "title": "Content Length", "description": "Content length in characters"}, "estimated_send_time": {"type": "number", "title": "Estimated Send Time", "description": "Estimated send time in seconds"}}, "type": "object", "required": ["is_valid", "suggested_priority", "content_length", "estimated_send_time"], "title": "MessageValidationResponse", "description": "Message validation response schema."}, "PaymentConfirmRequest": {"properties": {"payment_intent_id": {"type": "string", "title": "Payment Intent Id", "description": "Payment intent ID to confirm"}, "payment_method_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Method Id", "description": "Payment method ID"}}, "type": "object", "required": ["payment_intent_id"], "title": "PaymentConfirmRequest", "description": "Request schema for confirming a payment."}, "PaymentConfirmResponse": {"properties": {"payment_id": {"type": "string", "title": "Payment Id", "description": "Payment ID"}, "status": {"$ref": "#/components/schemas/app__models__payment__PaymentStatus", "description": "Payment status"}, "amount": {"type": "integer", "title": "Amount", "description": "Payment amount in cents"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Payment currency", "default": "usd"}, "platform_fee": {"type": "integer", "title": "Platform Fee", "description": "Platform fee in cents"}, "driver_earnings": {"type": "integer", "title": "Driver Earnings", "description": "Driver earnings in cents"}, "processed_at": {"type": "string", "format": "date-time", "title": "Processed At", "description": "Payment processing timestamp"}}, "type": "object", "required": ["payment_id", "status", "amount", "platform_fee", "driver_earnings", "processed_at"], "title": "PaymentConfirmResponse", "description": "Response schema for payment confirmation."}, "PaymentHistoryListResponse": {"properties": {"payments": {"items": {"$ref": "#/components/schemas/PaymentHistoryResponse"}, "type": "array", "title": "Payments", "description": "List of payments"}, "total": {"type": "integer", "title": "Total", "description": "Total number of payments"}, "has_more": {"type": "boolean", "title": "Has <PERSON>", "description": "Whether there are more payments"}}, "type": "object", "required": ["payments", "total", "has_more"], "title": "PaymentHistoryListResponse", "description": "Response schema for payment history list."}, "PaymentHistoryResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Payment ID"}, "amount": {"type": "integer", "title": "Amount", "description": "Payment amount in cents"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Payment currency", "default": "usd"}, "description": {"type": "string", "title": "Description", "description": "Payment description"}, "status": {"$ref": "#/components/schemas/app__models__payment__PaymentStatus", "description": "Payment status"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Payment creation timestamp"}, "processed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Processed At", "description": "Payment processing timestamp"}, "ride_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ride Id", "description": "Related ride ID"}, "payment_method_last_four": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Method Last Four", "description": "Payment method last four digits"}}, "type": "object", "required": ["id", "amount", "description", "status", "created_at"], "title": "PaymentHistoryResponse", "description": "Response schema for payment history."}, "PaymentIntentCreateRequest": {"properties": {"ride_id": {"type": "string", "title": "Ride Id", "description": "Ride ID for the payment"}, "amount": {"type": "integer", "exclusiveMinimum": 0, "title": "Amount", "description": "Payment amount in cents"}, "payment_method_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Method Id", "description": "Payment method ID"}, "save_payment_method": {"type": "boolean", "title": "Save Payment Method", "description": "Whether to save the payment method", "default": false}, "metadata": {"additionalProperties": true, "type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Additional metadata"}}, "type": "object", "required": ["ride_id", "amount"], "title": "PaymentIntentCreateRequest", "description": "Request schema for creating a payment intent."}, "PaymentIntentCreateResponse": {"properties": {"payment_intent_id": {"type": "string", "title": "Payment Intent Id", "description": "Stripe payment intent ID"}, "client_secret": {"type": "string", "title": "Client Secret", "description": "Client secret for payment confirmation"}, "amount": {"type": "integer", "title": "Amount", "description": "Payment amount in cents"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Payment currency", "default": "usd"}, "status": {"type": "string", "title": "Status", "description": "Payment intent status"}, "platform_fee": {"type": "integer", "title": "Platform Fee", "description": "Platform fee in cents"}, "driver_earnings": {"type": "integer", "title": "Driver Earnings", "description": "Driver earnings in cents"}}, "type": "object", "required": ["payment_intent_id", "client_secret", "amount", "status", "platform_fee", "driver_earnings"], "title": "PaymentIntentCreateResponse", "description": "Response schema for payment intent creation."}, "PaymentMethodCreateRequest": {"properties": {"payment_method_id": {"type": "string", "title": "Payment Method Id", "description": "Stripe payment method ID"}, "is_default": {"type": "boolean", "title": "<PERSON>", "description": "Whether this is the default payment method", "default": false}}, "type": "object", "required": ["payment_method_id"], "title": "PaymentMethodCreateRequest", "description": "Request schema for creating a payment method."}, "PaymentMethodListResponse": {"properties": {"payment_methods": {"items": {"$ref": "#/components/schemas/PaymentMethodResponse"}, "type": "array", "title": "Payment Methods", "description": "List of payment methods"}, "total": {"type": "integer", "title": "Total", "description": "Total number of payment methods"}, "has_default": {"type": "boolean", "title": "<PERSON>", "description": "Whether user has a default payment method"}}, "type": "object", "required": ["payment_methods", "total", "has_default"], "title": "PaymentMethodListResponse", "description": "Response schema for payment method list."}, "PaymentMethodResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Payment method ID"}, "type": {"$ref": "#/components/schemas/PaymentMethodType", "description": "Payment method type"}, "last_four": {"type": "string", "title": "Last Four", "description": "Last four digits"}, "brand": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Brand", "description": "Payment method brand"}, "exp_month": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Exp Month", "description": "Expiration month"}, "exp_year": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Exp Year", "description": "Expiration year"}, "is_default": {"type": "boolean", "title": "<PERSON>", "description": "Whether this is the default payment method"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether this payment method is active"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}}, "type": "object", "required": ["id", "type", "last_four", "is_default", "is_active", "created_at"], "title": "PaymentMethodResponse", "description": "Response schema for payment method."}, "PaymentMethodType": {"type": "string", "enum": ["card", "bank_account", "digital_wallet"], "title": "PaymentMethodType", "description": "Payment method type enumeration."}, "PaymentStatsResponse": {"properties": {"total_payments": {"type": "integer", "title": "Total Payments", "description": "Total number of payments"}, "successful_payments": {"type": "integer", "title": "Successful Payments", "description": "Number of successful payments"}, "failed_payments": {"type": "integer", "title": "Failed Payments", "description": "Number of failed payments"}, "total_revenue": {"type": "integer", "title": "Total Revenue", "description": "Total revenue in cents"}, "platform_revenue": {"type": "integer", "title": "Platform Revenue", "description": "Platform revenue in cents"}, "driver_earnings": {"type": "integer", "title": "Driver Earnings", "description": "Total driver earnings in cents"}, "total_payouts": {"type": "integer", "title": "Total Payouts", "description": "Total number of payouts"}, "successful_payouts": {"type": "integer", "title": "Successful Payouts", "description": "Number of successful payouts"}, "failed_payouts": {"type": "integer", "title": "Failed Payouts", "description": "Number of failed payouts"}, "average_payment_amount": {"type": "number", "title": "Average Payment Amount", "description": "Average payment amount"}, "average_payout_amount": {"type": "number", "title": "Average Payout Amount", "description": "Average payout amount"}, "platform_fee_percentage": {"type": "number", "title": "Platform Fee Percentage", "description": "Platform fee percentage"}, "payment_success_rate": {"type": "number", "title": "Payment Success Rate", "description": "Payment success rate"}, "payout_success_rate": {"type": "number", "title": "Payout Success Rate", "description": "Payout success rate"}}, "type": "object", "required": ["total_payments", "successful_payments", "failed_payments", "total_revenue", "platform_revenue", "driver_earnings", "total_payouts", "successful_payouts", "failed_payouts", "average_payment_amount", "average_payout_amount", "platform_fee_percentage", "payment_success_rate", "payout_success_rate"], "title": "PaymentStatsResponse", "description": "Response schema for payment statistics."}, "PayoutListResponse": {"properties": {"payouts": {"items": {"$ref": "#/components/schemas/PayoutResponse"}, "type": "array", "title": "Payouts", "description": "List of payouts"}, "total": {"type": "integer", "title": "Total", "description": "Total number of payouts"}, "has_more": {"type": "boolean", "title": "Has <PERSON>", "description": "Whether there are more payouts"}}, "type": "object", "required": ["payouts", "total", "has_more"], "title": "PayoutListResponse", "description": "Response schema for payout list."}, "PayoutResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Payout ID"}, "stripe_payout_id": {"type": "string", "title": "Stripe Payout Id", "description": "Stripe payout ID"}, "amount": {"type": "integer", "title": "Amount", "description": "Payout amount in cents"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Payout currency"}, "method": {"type": "string", "title": "Method", "description": "Payout method"}, "status": {"$ref": "#/components/schemas/PayoutStatus", "description": "Payout status"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "arrival_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Arrival Date", "description": "Expected arrival date"}}, "type": "object", "required": ["id", "stripe_payout_id", "amount", "currency", "method", "status", "created_at"], "title": "PayoutResponse", "description": "Response schema for payout."}, "PayoutStatus": {"type": "string", "enum": ["pending", "in_transit", "paid", "failed", "cancelled"], "title": "PayoutStatus", "description": "Payout status enumeration."}, "ProfileImageUpload": {"properties": {"image_url": {"type": "string", "title": "Image Url", "description": "Uploaded image URL"}, "message": {"type": "string", "title": "Message", "description": "Upload status message"}}, "type": "object", "required": ["image_url", "message"], "title": "ProfileImageUpload", "description": "Schema for profile image upload response."}, "RatingCreate": {"properties": {"stars": {"type": "integer", "maximum": 5, "minimum": 1, "title": "Stars", "description": "Star rating (1-5 stars)"}, "feedback": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Optional written feedback"}, "is_anonymous": {"type": "boolean", "title": "Is Anonymous", "description": "Whether rating is anonymous", "default": false}, "rating_type": {"$ref": "#/components/schemas/RatingType", "description": "Type of rating"}}, "type": "object", "required": ["stars", "rating_type"], "title": "RatingCreate", "description": "Schema for creating a new rating."}, "RatingDeleteResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Deletion success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Deletion message"}, "rating_id": {"type": "string", "title": "Rating Id", "description": "Deleted rating ID"}}, "type": "object", "required": ["message", "rating_id"], "title": "RatingDeleteResponse", "description": "Schema for rating deletion response."}, "RatingEligibilityResponse": {"properties": {"ride_id": {"type": "string", "title": "Ride Id", "description": "Ride ID"}, "user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "can_rate": {"type": "boolean", "title": "Can Rate", "description": "Whether user can rate"}, "rating_type": {"anyOf": [{"$ref": "#/components/schemas/RatingType"}, {"type": "null"}], "description": "Type of rating allowed"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "Reason if rating not allowed"}, "existing_rating": {"anyOf": [{"$ref": "#/components/schemas/RatingResponse"}, {"type": "null"}], "description": "Existing rating if present"}, "time_limit_expires": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Time Limit Expires", "description": "Time limit for rating"}}, "type": "object", "required": ["ride_id", "user_id", "can_rate"], "title": "RatingEligibilityResponse", "description": "Schema for rating eligibility response."}, "RatingHistoryResponse": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "ratings_given": {"items": {"$ref": "#/components/schemas/RatingResponse"}, "type": "array", "title": "Ratings Given", "description": "Ratings given by user"}, "ratings_received": {"items": {"$ref": "#/components/schemas/RatingResponse"}, "type": "array", "title": "Ratings Received", "description": "Ratings received by user"}, "total_given": {"type": "integer", "minimum": 0, "title": "Total Given", "description": "Total ratings given"}, "total_received": {"type": "integer", "minimum": 0, "title": "Total Received", "description": "Total ratings received"}, "average_given": {"type": "number", "maximum": 5, "minimum": 0, "title": "Average Given", "description": "Average rating given"}, "average_received": {"type": "number", "maximum": 5, "minimum": 0, "title": "Average Received", "description": "Average rating received"}, "last_activity": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Activity", "description": "Last rating activity"}}, "type": "object", "required": ["user_id", "ratings_given", "ratings_received", "total_given", "total_received", "average_given", "average_received"], "title": "RatingHistoryResponse", "description": "Schema for rating history response."}, "RatingResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Rating ID"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Ride ID"}, "rater_id": {"type": "string", "title": "Rater Id", "description": "User who gives the rating"}, "ratee_id": {"type": "string", "title": "Ratee Id", "description": "User who receives the rating"}, "rating_type": {"$ref": "#/components/schemas/RatingType", "description": "Type of rating"}, "stars": {"type": "integer", "maximum": 5, "minimum": 1, "title": "Stars", "description": "Star rating (1-5 stars)"}, "feedback": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Optional written feedback"}, "is_anonymous": {"type": "boolean", "title": "Is Anonymous", "description": "Whether rating is anonymous"}, "status": {"$ref": "#/components/schemas/RatingStatus", "description": "Rating status"}, "created_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Created At", "description": "Rating creation timestamp"}, "updated_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["id", "ride_id", "rater_id", "ratee_id", "rating_type", "stars", "is_anonymous", "status"], "title": "RatingResponse", "description": "Schema for rating response."}, "RatingStatsResponse": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "user_type": {"type": "string", "title": "User Type", "description": "User type (rider/driver)"}, "average_rating": {"type": "number", "maximum": 5, "minimum": 0, "title": "Average Rating", "description": "Average rating"}, "total_ratings": {"type": "integer", "minimum": 0, "title": "Total Ratings", "description": "Total number of ratings received"}, "rating_distribution": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Rating Distribution", "description": "Rating distribution by star count"}, "last_updated": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Updated", "description": "Last statistics update"}}, "type": "object", "required": ["user_id", "user_type", "average_rating", "total_ratings", "rating_distribution"], "title": "RatingStatsResponse", "description": "Schema for rating statistics response."}, "RatingStatus": {"type": "string", "enum": ["active", "updated", "deleted"], "title": "RatingStatus", "description": "Rating status enumeration."}, "RatingSuccessResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Operation success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Success message"}, "rating": {"anyOf": [{"$ref": "#/components/schemas/RatingResponse"}, {"type": "null"}], "description": "Created/updated rating"}}, "type": "object", "required": ["message"], "title": "RatingSuccessResponse", "description": "Schema for rating success responses."}, "RatingSummaryResponse": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "user_type": {"type": "string", "title": "User Type", "description": "User type"}, "current_rating": {"type": "number", "maximum": 5, "minimum": 0, "title": "Current Rating", "description": "Current average rating"}, "total_ratings_received": {"type": "integer", "minimum": 0, "title": "Total Ratings Received", "description": "Total ratings received"}, "total_ratings_given": {"type": "integer", "minimum": 0, "title": "Total Ratings Given", "description": "Total ratings given"}, "recent_ratings": {"items": {"$ref": "#/components/schemas/RatingResponse"}, "type": "array", "title": "Recent Ratings", "description": "Recent ratings (last 10)"}, "rating_trend": {"type": "string", "title": "Rating Trend", "description": "Rating trend (improving, declining, stable)"}, "last_rating_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Rating Date", "description": "Last rating date"}}, "type": "object", "required": ["user_id", "user_type", "current_rating", "total_ratings_received", "total_ratings_given", "recent_ratings", "rating_trend"], "title": "RatingSummaryResponse", "description": "Schema for rating summary response."}, "RatingType": {"type": "string", "enum": ["rider_to_driver", "driver_to_rider"], "title": "RatingType", "description": "Rating type enumeration."}, "RatingUpdate": {"properties": {"stars": {"anyOf": [{"type": "integer", "maximum": 5, "minimum": 1}, {"type": "null"}], "title": "Stars", "description": "Star rating (1-5 stars)"}, "feedback": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Optional written feedback"}, "is_anonymous": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Anonymous", "description": "Whether rating is anonymous"}}, "type": "object", "title": "RatingUpdate", "description": "Schema for updating an existing rating."}, "RatingValidationErrorResponse": {"properties": {"error": {"type": "string", "title": "Error", "description": "Error message"}, "details": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Details", "description": "Validation error details"}, "rating_data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Rating Data", "description": "Original rating data"}}, "type": "object", "required": ["error", "details"], "title": "RatingValidationErrorResponse", "description": "Schema for rating validation error responses."}, "RefundCreateRequest": {"properties": {"payment_id": {"type": "string", "title": "Payment Id", "description": "Payment ID to refund"}, "amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Amount", "description": "Refund amount in cents (full refund if not specified)"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "Refund reason"}, "metadata": {"additionalProperties": true, "type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Additional metadata"}}, "type": "object", "required": ["payment_id"], "title": "RefundCreateRequest", "description": "Request schema for creating a refund."}, "RefundResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Refund ID"}, "payment_id": {"type": "string", "title": "Payment Id", "description": "Original payment ID"}, "amount": {"type": "integer", "title": "Amount", "description": "Refund amount in cents"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Refund currency", "default": "usd"}, "status": {"type": "string", "title": "Status", "description": "Refund status"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "Refund reason"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}}, "type": "object", "required": ["id", "payment_id", "amount", "status", "created_at"], "title": "RefundResponse", "description": "Response schema for refund."}, "Ride": {"properties": {"id": {"type": "string", "title": "Id", "description": "Unique ride ID"}, "rider_id": {"type": "string", "title": "Rider Id", "description": "Rider user ID"}, "driver_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Driver Id", "description": "Driver user ID"}, "status": {"$ref": "#/components/schemas/RideStatus", "description": "Current ride status", "default": "requested"}, "ride_type": {"$ref": "#/components/schemas/RideType", "description": "Type of ride", "default": "point_to_point"}, "pickup_location": {"$ref": "#/components/schemas/RideLocation", "description": "Pickup location"}, "dropoff_location": {"$ref": "#/components/schemas/RideLocation", "description": "Dropoff location"}, "fixed_fare": {"type": "number", "exclusiveMinimum": 0, "title": "Fixed Fare", "description": "Fixed fare for the ride"}, "payment_status": {"$ref": "#/components/schemas/app__models__ride__PaymentStatus", "description": "Payment status", "default": "pending"}, "payment_intent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Intent Id", "description": "Stripe payment intent ID"}, "distance_km": {"anyOf": [{"type": "number", "minimum": 0}, {"type": "null"}], "title": "Distance Km", "description": "Distance in kilometers"}, "estimated_duration": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "Estimated Duration", "description": "Estimated duration in minutes"}, "actual_duration": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "Actual Duration", "description": "Actual duration in minutes"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Ride creation timestamp"}, "accepted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Accepted At", "description": "Ride acceptance timestamp"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At", "description": "Ride start timestamp"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At", "description": "Ride completion timestamp"}, "cancelled_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Cancelled At", "description": "Ride cancellation timestamp"}, "special_instructions": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Special Instructions", "description": "Special instructions"}, "cancellation_reason": {"anyOf": [{"$ref": "#/components/schemas/CancellationReason"}, {"type": "null"}], "description": "Reason for cancellation"}, "cancellation_details": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Cancellation Details", "description": "Detailed cancellation reason"}, "notified_drivers": {"items": {"type": "string"}, "type": "array", "title": "Notified Drivers", "description": "List of notified driver IDs"}, "matching_started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Matching Started At", "description": "Matching process start time"}, "matching_completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Matching Completed At", "description": "Matching process completion time"}, "ratings": {"anyOf": [{"$ref": "#/components/schemas/RideRating"}, {"type": "null"}], "description": "Ride ratings"}, "app_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "App Version", "description": "App version used for booking"}, "platform": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Platform", "description": "Platform used (ios/android/web)"}}, "type": "object", "required": ["rider_id", "pickup_location", "dropoff_location", "fixed_fare"], "title": "Ride", "description": "Main ride model."}, "RideAcceptanceRequest": {"properties": {"estimated_arrival": {"anyOf": [{"type": "integer", "maximum": 120, "minimum": 0}, {"type": "null"}], "title": "Estimated Arrival", "description": "Estimated arrival time in minutes"}, "notes": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Notes", "description": "Notes for the rider"}}, "type": "object", "title": "RideAcceptanceRequest", "description": "<PERSON><PERSON><PERSON> for driver accepting a ride."}, "RideCancellationRequest": {"properties": {"reason": {"$ref": "#/components/schemas/CancellationReason", "description": "Reason for cancellation"}, "details": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Details", "description": "Detailed cancellation reason"}}, "type": "object", "required": ["reason"], "title": "RideCancellationRequest", "description": "<PERSON><PERSON><PERSON> for cancelling a ride."}, "RideHistory": {"properties": {"ride_id": {"type": "string", "title": "Ride Id", "description": "Ride ID"}, "user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "user_type": {"type": "string", "title": "User Type", "description": "User type (rider/driver)"}, "pickup_location": {"$ref": "#/components/schemas/RideLocation", "description": "Pickup location"}, "dropoff_location": {"$ref": "#/components/schemas/RideLocation", "description": "Dropoff location"}, "fare": {"type": "number", "title": "Fare", "description": "Ride fare"}, "distance_km": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Distance Km", "description": "Distance in kilometers"}, "duration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration", "description": "Duration in minutes"}, "other_user_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Other User Name", "description": "Other user's name (driver/rider)"}, "rating_given": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rating Given", "description": "Rating given by this user"}, "rating_received": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rating Received", "description": "Rating received by this user"}, "status": {"$ref": "#/components/schemas/RideStatus", "description": "Final ride status"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Ride creation timestamp"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At", "description": "Ride completion timestamp"}}, "type": "object", "required": ["ride_id", "user_id", "user_type", "pickup_location", "dropoff_location", "fare", "status", "created_at"], "title": "RideHistory", "description": "Ride history model for displaying past rides."}, "RideHistoryResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Response message", "default": "Ride history retrieved successfully"}, "data": {"items": {"$ref": "#/components/schemas/RideHistory"}, "type": "array", "title": "Data", "description": "List of ride history"}, "total": {"type": "integer", "title": "Total", "description": "Total number of rides in history", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Limit applied to results", "default": 20}, "offset": {"type": "integer", "title": "Offset", "description": "Offset applied to results", "default": 0}, "has_more": {"type": "boolean", "title": "Has <PERSON>", "description": "Whether there are more results", "default": false}}, "type": "object", "title": "RideHistoryResponse", "description": "Response schema for ride history."}, "RideLocation": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Location name"}, "address": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Address", "description": "Full address"}, "latitude": {"type": "number", "maximum": 90, "minimum": -90, "title": "Latitude", "description": "Latitude coordinate"}, "longitude": {"type": "number", "maximum": 180, "minimum": -180, "title": "Longitude", "description": "Longitude coordinate"}, "instructions": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Instructions", "description": "Special pickup/dropoff instructions"}}, "type": "object", "required": ["name", "latitude", "longitude"], "title": "RideLocation", "description": "Extended location model for rides."}, "RideLocationRequest": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Location name"}, "address": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Address", "description": "Full address"}, "latitude": {"type": "number", "maximum": 90, "minimum": -90, "title": "Latitude", "description": "Latitude coordinate"}, "longitude": {"type": "number", "maximum": 180, "minimum": -180, "title": "Longitude", "description": "Longitude coordinate"}, "instructions": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Instructions", "description": "Special pickup/dropoff instructions"}}, "type": "object", "required": ["name", "latitude", "longitude"], "title": "RideLocationRequest", "description": "Request schema for ride location."}, "RideRating": {"properties": {"rider_rating": {"anyOf": [{"type": "integer", "maximum": 5, "minimum": 1}, {"type": "null"}], "title": "Rider Rating", "description": "Rating given by rider to driver"}, "driver_rating": {"anyOf": [{"type": "integer", "maximum": 5, "minimum": 1}, {"type": "null"}], "title": "Driver Rating", "description": "Rating given by driver to rider"}, "rider_feedback": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "<PERSON>", "description": "Feedback from rider"}, "driver_feedback": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Driver <PERSON>", "description": "Feedback from driver"}}, "type": "object", "title": "RideRating", "description": "Ride rating model."}, "RideReceipt": {"properties": {"id": {"type": "string", "title": "Id", "description": "Receipt ID"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Ride ID"}, "user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "user_type": {"type": "string", "title": "User Type", "description": "User type"}, "pickup_location": {"type": "string", "title": "Pickup Location", "description": "Pickup location name"}, "dropoff_location": {"type": "string", "title": "Dropoff Location", "description": "Dropoff location name"}, "ride_date": {"type": "string", "format": "date-time", "title": "Ride Date", "description": "Ride date"}, "base_fare": {"type": "number", "title": "Base Fare", "description": "Base fare"}, "platform_fee": {"type": "number", "title": "Platform Fee", "description": "Platform fee", "default": 0}, "total_fare": {"type": "number", "title": "Total Fare", "description": "Total fare"}, "payment_method": {"type": "string", "title": "Payment Method", "description": "Payment method"}, "payment_status": {"$ref": "#/components/schemas/app__models__ride__PaymentStatus", "description": "Payment status"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transaction Id", "description": "Payment transaction ID"}, "driver_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Driver Name", "description": "Driver name"}, "driver_rating": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Driver Rating", "description": "Driver rating"}, "rider_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rider Name", "description": "Rider name"}, "rider_rating": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Rider Rating", "description": "Rider rating"}, "issued_at": {"type": "string", "format": "date-time", "title": "Issued At", "description": "Receipt issue timestamp"}}, "type": "object", "required": ["ride_id", "user_id", "user_type", "pickup_location", "dropoff_location", "ride_date", "base_fare", "total_fare", "payment_method", "payment_status"], "title": "RideReceipt", "description": "Ride receipt model."}, "RideReceiptResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Response message", "default": "Receipt generated successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/RideReceipt"}, {"type": "null"}], "description": "Ride receipt data"}}, "type": "object", "title": "RideReceiptResponse", "description": "Response schema for ride receipt."}, "RideRequestCreate": {"properties": {"pickup_location": {"$ref": "#/components/schemas/RideLocationRequest", "description": "Pickup location details"}, "dropoff_location": {"$ref": "#/components/schemas/RideLocationRequest", "description": "Dropoff location details"}, "ride_type": {"$ref": "#/components/schemas/RideType", "description": "Type of ride", "default": "point_to_point"}, "special_instructions": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Special Instructions", "description": "Special instructions for driver"}, "preferred_driver_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferred Driver Id", "description": "Preferred driver ID"}}, "type": "object", "required": ["pickup_location", "dropoff_location"], "title": "RideRequestCreate", "description": "Schema for creating a new ride request."}, "RideResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Response message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/Ride"}, {"type": "null"}], "description": "Ride data"}}, "type": "object", "title": "RideResponse", "description": "Response schema for ride operations."}, "RideSearchRequest": {"properties": {"status": {"anyOf": [{"$ref": "#/components/schemas/RideStatus"}, {"type": "null"}], "description": "Filter by ride status"}, "start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date", "description": "Start date filter"}, "end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date", "description": "End date filter"}, "pickup_location": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Pickup Location", "description": "Filter by pickup location"}, "dropoff_location": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Dropoff Location", "description": "Filter by dropoff location"}, "min_fare": {"anyOf": [{"type": "number", "minimum": 0}, {"type": "null"}], "title": "<PERSON>", "description": "Minimum fare filter"}, "max_fare": {"anyOf": [{"type": "number", "minimum": 0}, {"type": "null"}], "title": "<PERSON>", "description": "Maximum fare filter"}, "limit": {"type": "integer", "maximum": 100, "minimum": 1, "title": "Limit", "description": "Number of results to return", "default": 20}, "offset": {"type": "integer", "minimum": 0, "title": "Offset", "description": "Number of results to skip", "default": 0}, "sort_by": {"type": "string", "title": "Sort By", "description": "Sort field", "default": "created_at"}, "sort_order": {"type": "string", "title": "Sort Order", "description": "Sort order (asc/desc)", "default": "desc"}}, "type": "object", "title": "RideSearchRequest", "description": "<PERSON><PERSON><PERSON> for searching rides."}, "RideSearchResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Response message", "default": "Search completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/Ride"}, "type": "array", "title": "Data", "description": "Search results"}, "total": {"type": "integer", "title": "Total", "description": "Total number of matching results", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Limit applied to results", "default": 20}, "offset": {"type": "integer", "title": "Offset", "description": "Offset applied to results", "default": 0}, "has_more": {"type": "boolean", "title": "Has <PERSON>", "description": "Whether there are more results", "default": false}, "filters_applied": {"additionalProperties": true, "type": "object", "title": "Filters Applied", "description": "Applied filters"}}, "type": "object", "title": "RideSearchResponse", "description": "Response schema for ride search."}, "RideStats": {"properties": {"total_rides": {"type": "integer", "title": "Total Rides", "description": "Total number of rides", "default": 0}, "completed_rides": {"type": "integer", "title": "Completed Rides", "description": "Number of completed rides", "default": 0}, "cancelled_rides": {"type": "integer", "title": "Cancelled Rides", "description": "Number of cancelled rides", "default": 0}, "average_fare": {"type": "number", "title": "Average Fare", "description": "Average fare per ride", "default": 0}, "total_distance": {"type": "number", "title": "Total Distance", "description": "Total distance traveled", "default": 0}, "average_rating": {"type": "number", "title": "Average Rating", "description": "Average rating", "default": 0}, "completion_rate": {"type": "number", "title": "Completion Rate", "description": "Ride completion rate", "default": 0}}, "type": "object", "title": "RideStats", "description": "Ride statistics model."}, "RideStatsResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Response message", "default": "Statistics retrieved successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/RideStats"}, {"type": "null"}], "description": "Ride statistics"}}, "type": "object", "title": "RideStatsResponse", "description": "Response schema for ride statistics."}, "RideStatus": {"type": "string", "enum": ["requested", "accepted", "in_progress", "completed", "cancelled"], "title": "RideStatus", "description": "Ride status enumeration."}, "RideStatusUpdateRequest": {"properties": {"status": {"$ref": "#/components/schemas/RideStatus", "description": "New ride status"}, "notes": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Notes", "description": "Status update notes"}}, "type": "object", "required": ["status"], "title": "RideStatusUpdateRequest", "description": "Schema for updating ride status."}, "RideType": {"type": "string", "enum": ["point_to_point", "scheduled", "return"], "title": "RideType", "description": "Ride type enumeration."}, "RiderProfile": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "rating": {"type": "number", "maximum": 5, "minimum": 0, "title": "Rating", "description": "Average rating", "default": 0}, "total_rides": {"type": "integer", "minimum": 0, "title": "Total Rides", "description": "Total number of rides", "default": 0}, "payment_methods": {"items": {"type": "string"}, "type": "array", "title": "Payment Methods", "description": "Payment method IDs"}, "emergency_contact": {"anyOf": [{"$ref": "#/components/schemas/EmergencyContact"}, {"type": "null"}], "description": "Emergency contact info"}, "ride_preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Ride Preferences", "description": "Ride preferences"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "Profile creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["user_id"], "title": "RiderProfile", "description": "Rider-specific profile model."}, "RiderProfileCreate": {"properties": {"emergency_contact": {"anyOf": [{"$ref": "#/components/schemas/EmergencyContact"}, {"type": "null"}], "description": "Emergency contact info"}, "ride_preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Ride Preferences", "description": "Ride preferences"}}, "type": "object", "title": "RiderProfileCreate", "description": "<PERSON><PERSON>a for creating rider profile."}, "RiderProfileResponse": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "rating": {"type": "number", "maximum": 5, "minimum": 0, "title": "Rating", "description": "Average rating"}, "total_rides": {"type": "integer", "minimum": 0, "title": "Total Rides", "description": "Total number of rides"}, "payment_methods": {"items": {"type": "string"}, "type": "array", "title": "Payment Methods", "description": "Payment method IDs"}, "emergency_contact": {"anyOf": [{"$ref": "#/components/schemas/EmergencyContact"}, {"type": "null"}], "description": "Emergency contact info"}, "ride_preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Ride Preferences", "description": "Ride preferences"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "Profile creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["user_id", "rating", "total_rides", "payment_methods"], "title": "RiderProfileResponse", "description": "Schema for rider profile response."}, "RiderProfileUpdate": {"properties": {"emergency_contact": {"anyOf": [{"$ref": "#/components/schemas/EmergencyContact"}, {"type": "null"}], "description": "Emergency contact info"}, "ride_preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Ride Preferences", "description": "Ride preferences"}}, "type": "object", "title": "RiderProfileUpdate", "description": "<PERSON><PERSON>a for updating rider profile."}, "SenderType": {"type": "string", "enum": ["rider", "driver", "system"], "title": "SenderType", "description": "Sender type enumeration."}, "TokenResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token", "description": "Access token"}, "token_type": {"type": "string", "title": "Token Type", "description": "Token type", "default": "bearer"}, "expires_in": {"type": "integer", "title": "Expires In", "description": "Token expiration time in seconds"}, "user": {"$ref": "#/components/schemas/UserResponse", "description": "User information"}}, "type": "object", "required": ["access_token", "expires_in", "user"], "title": "TokenResponse", "description": "<PERSON><PERSON>a for token response."}, "TripHistory": {"properties": {"ride_id": {"type": "string", "title": "Ride Id", "description": "Ride ID"}, "user_id": {"type": "string", "title": "User Id", "description": "User ID"}, "user_type": {"type": "string", "title": "User Type", "description": "User type (rider/driver)"}, "pickup_location": {"$ref": "#/components/schemas/LocationInfo", "description": "Pickup location info"}, "dropoff_location": {"$ref": "#/components/schemas/LocationInfo", "description": "Dropoff location info"}, "fare": {"type": "number", "title": "Fare", "description": "Trip fare"}, "distance": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Distance", "description": "Trip distance in kilometers"}, "duration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration", "description": "Trip duration in minutes"}, "driver_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Driver Name", "description": "Driver name"}, "rider_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rider Name", "description": "Rider name"}, "rating_given": {"anyOf": [{"type": "integer", "maximum": 5, "minimum": 1}, {"type": "null"}], "title": "Rating Given", "description": "Rating given by this user"}, "rating_received": {"anyOf": [{"type": "integer", "maximum": 5, "minimum": 1}, {"type": "null"}], "title": "Rating Received", "description": "Rating received by this user"}, "status": {"type": "string", "title": "Status", "description": "Trip status"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Trip creation timestamp"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At", "description": "Trip completion timestamp"}}, "type": "object", "required": ["ride_id", "user_id", "user_type", "pickup_location", "dropoff_location", "fare", "status", "created_at"], "title": "TripHistory", "description": "Trip history model for displaying comprehensive trip information."}, "TripHistoryResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Response message", "default": "Trip history retrieved successfully"}, "data": {"items": {"$ref": "#/components/schemas/TripHistory"}, "type": "array", "title": "Data", "description": "List of trip history"}, "total": {"type": "integer", "title": "Total", "description": "Total number of trips in history", "default": 0}, "limit": {"type": "integer", "title": "Limit", "description": "Limit applied to results", "default": 20}, "offset": {"type": "integer", "title": "Offset", "description": "Offset applied to results", "default": 0}, "has_more": {"type": "boolean", "title": "Has <PERSON>", "description": "Whether there are more results", "default": false}}, "type": "object", "title": "TripHistoryResponse", "description": "Response schema for trip history."}, "User": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id", "description": "User ID"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "User email address"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20, "minLength": 10}, {"type": "null"}], "title": "Phone", "description": "Phone number"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "User full name"}, "user_type": {"$ref": "#/components/schemas/UserType", "description": "User type"}, "profile_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Profile Image", "description": "Profile image URL"}, "language": {"type": "string", "title": "Language", "description": "Preferred language", "default": "en"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "User active status", "default": true}, "status": {"$ref": "#/components/schemas/UserStatus", "description": "User status", "default": "active"}, "created_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Created At", "description": "Account creation timestamp"}, "updated_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}, "firebase_uid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firebase Uid", "description": "Firebase Auth UID"}, "email_verified": {"type": "boolean", "title": "<PERSON><PERSON>", "description": "Email verification status", "default": false}, "last_login": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Login", "description": "Last login timestamp"}}, "type": "object", "required": ["email", "name", "user_type"], "title": "User", "description": "Complete user model."}, "UserCreate": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User email address"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "User full name"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20, "minLength": 10}, {"type": "null"}], "title": "Phone", "description": "Phone number"}, "user_type": {"$ref": "#/components/schemas/UserType", "description": "User type"}, "language": {"type": "string", "title": "Language", "description": "Preferred language", "default": "en"}, "password": {"type": "string", "maxLength": 128, "minLength": 8, "title": "Password", "description": "User password"}, "confirm_password": {"type": "string", "maxLength": 128, "minLength": 8, "title": "Confirm Password", "description": "Password confirmation"}}, "type": "object", "required": ["email", "name", "user_type", "password", "confirm_password"], "title": "UserCreate", "description": "Schema for user registration."}, "UserLogin": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User email address"}, "password": {"type": "string", "minLength": 1, "title": "Password", "description": "User password"}}, "type": "object", "required": ["email", "password"], "title": "UserLogin", "description": "Schema for user login."}, "UserResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "User ID"}, "email": {"type": "string", "title": "Email", "description": "User email address"}, "name": {"type": "string", "title": "Name", "description": "User full name"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "Phone number"}, "user_type": {"$ref": "#/components/schemas/UserType", "description": "User type"}, "profile_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Profile Image", "description": "Profile image URL"}, "language": {"type": "string", "title": "Language", "description": "Preferred language"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "User active status"}, "status": {"$ref": "#/components/schemas/UserStatus", "description": "User status"}, "email_verified": {"type": "boolean", "title": "<PERSON><PERSON>", "description": "Email verification status"}, "created_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Created At", "description": "Account creation timestamp"}, "updated_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["id", "email", "name", "user_type", "language", "is_active", "status", "email_verified"], "title": "UserResponse", "description": "Schema for user response."}, "UserStatus": {"type": "string", "enum": ["active", "suspended", "pending_verification", "inactive"], "title": "UserStatus", "description": "User status enumeration."}, "UserType": {"type": "string", "enum": ["rider", "driver", "admin"], "title": "UserType", "description": "User type enumeration."}, "UserUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "User full name"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20, "minLength": 10}, {"type": "null"}], "title": "Phone", "description": "Phone number"}, "language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Language", "description": "Preferred language"}, "profile_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Profile Image", "description": "Profile image URL"}}, "type": "object", "title": "UserUpdate", "description": "Schema for user profile updates."}, "UserWithProfile": {"properties": {"user": {"$ref": "#/components/schemas/User"}, "rider_profile": {"anyOf": [{"$ref": "#/components/schemas/RiderProfile"}, {"type": "null"}]}, "driver_profile": {"anyOf": [{"$ref": "#/components/schemas/DriverProfile"}, {"type": "null"}]}, "admin_profile": {"anyOf": [{"$ref": "#/components/schemas/AdminProfile"}, {"type": "null"}]}}, "type": "object", "required": ["user"], "title": "UserWithProfile", "description": "Complete user model with profile information."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VehicleInfo": {"properties": {"make": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Make", "description": "Vehicle make"}, "model": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Model", "description": "Vehicle model"}, "year": {"type": "integer", "maximum": 2025, "minimum": 1990, "title": "Year", "description": "Vehicle year"}, "color": {"type": "string", "maxLength": 30, "minLength": 1, "title": "Color", "description": "Vehicle color"}, "license_plate": {"type": "string", "maxLength": 20, "minLength": 1, "title": "License Plate", "description": "License plate number"}}, "type": "object", "required": ["make", "model", "year", "color", "license_plate"], "title": "VehicleInfo", "description": "Vehicle information model for drivers."}, "WebhookEventResponse": {"properties": {"received": {"type": "boolean", "title": "Received", "description": "Whether event was received", "default": true}, "processed": {"type": "boolean", "title": "Processed", "description": "Whether event was processed"}, "message": {"type": "string", "title": "Message", "description": "Processing message"}}, "type": "object", "required": ["processed", "message"], "title": "WebhookEventResponse", "description": "Response schema for webhook events."}, "app__models__payment__PaymentStatus": {"type": "string", "enum": ["pending", "processing", "succeeded", "failed", "refunded", "cancelled"], "title": "PaymentStatus", "description": "Payment status enumeration."}, "app__models__ride__PaymentStatus": {"type": "string", "enum": ["pending", "processing", "completed", "failed", "refunded"], "title": "PaymentStatus", "description": "Payment status enumeration."}, "app__schemas__message__MessageResponse": {"properties": {"id": {"type": "string", "title": "Id", "description": "Message ID"}, "ride_id": {"type": "string", "title": "Ride Id", "description": "Associated ride ID"}, "sender_id": {"type": "string", "title": "Sender Id", "description": "Sender user ID"}, "sender_type": {"$ref": "#/components/schemas/SenderType", "description": "Sender type"}, "recipient_id": {"type": "string", "title": "Recipient Id", "description": "Recipient user ID"}, "recipient_type": {"$ref": "#/components/schemas/SenderType", "description": "Recipient type"}, "content": {"type": "string", "title": "Content", "description": "Message content"}, "message_type": {"$ref": "#/components/schemas/MessageType", "description": "Message type"}, "status": {"$ref": "#/components/schemas/MessageStatus", "description": "Message status"}, "priority": {"$ref": "#/components/schemas/MessagePriority", "description": "Message priority"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Message creation timestamp"}, "delivered_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Delivered At", "description": "Message delivery timestamp"}, "read_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Read At", "description": "Message read timestamp"}, "attachments": {"items": {"$ref": "#/components/schemas/MessageAttachment"}, "type": "array", "title": "Attachments", "description": "Message attachments"}, "metadata": {"anyOf": [{"$ref": "#/components/schemas/MessageMetadata"}, {"type": "null"}], "description": "Message metadata"}, "is_system_message": {"type": "boolean", "title": "Is System Message", "description": "Whether message is system-generated"}, "is_automated": {"type": "boolean", "title": "Is Automated", "description": "Whether message is automated"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "Message expiration timestamp"}, "template_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Template Id", "description": "Template ID if message uses template"}}, "type": "object", "required": ["id", "ride_id", "sender_id", "sender_type", "recipient_id", "recipient_type", "content", "message_type", "status", "priority", "created_at", "is_system_message", "is_automated"], "title": "MessageResponse", "description": "Message response schema."}, "app__schemas__payment__MessageResponse": {"properties": {"message": {"type": "string", "title": "Message", "description": "Response message"}, "success": {"type": "boolean", "title": "Success", "description": "Whether operation was successful", "default": true}}, "type": "object", "required": ["message"], "title": "MessageResponse", "description": "Generic message response schema."}, "app__schemas__ride__ErrorResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": false}, "message": {"type": "string", "title": "Message", "description": "Error message"}, "error_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Code", "description": "Error code"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "Additional error details"}}, "type": "object", "required": ["message"], "title": "ErrorResponse", "description": "Generic error response schema."}, "app__schemas__ride__SuccessResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Request success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Response message", "default": "Operation completed successfully"}}, "type": "object", "title": "SuccessResponse", "description": "Generic success response schema."}, "app__schemas__user__ErrorResponse": {"properties": {"error": {"type": "string", "title": "Error", "description": "Error message"}, "detail": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Detail", "description": "Detailed error information"}, "error_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Code", "description": "Error code"}}, "type": "object", "required": ["error"], "title": "ErrorResponse", "description": "Schema for error responses."}, "app__schemas__user__SuccessResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Operation success status", "default": true}, "message": {"type": "string", "title": "Message", "description": "Success message"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data", "description": "Additional data"}}, "type": "object", "required": ["message"], "title": "SuccessResponse", "description": "Schema for success responses."}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}